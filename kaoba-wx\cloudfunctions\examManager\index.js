// 考试管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    switch (action) {
      case 'getExams':
        return await getExams(wxContext.OPENID, data)
      case 'addExam':
        return await addExam(wxContext.OPENID, data)
      case 'updateExam':
        return await updateExam(wxContext.OPENID, data)
      case 'deleteExam':
        return await deleteExam(wxContext.OPENID, data)
      case 'getUpcomingExams':
        return await getUpcomingExams(wxContext.OPENID, data)
      case 'getExamStats':
        return await getExamStats(wxContext.OPENID, data)
      case 'setExamReminder':
        return await setExamReminder(wxContext.OPENID, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('考试管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 获取考试列表
async function getExams(openid, params) {
  const { filter, limit = 20, skip = 0 } = params || {}
  
  let query = db.collection('exams').where({ _openid: openid })
  
  if (filter) {
    if (filter.subject) {
      query = query.where({ subject: filter.subject })
    }
    if (filter.dateRange) {
      const { start, end } = filter.dateRange
      query = query.where({
        examDate: _.gte(new Date(start)).and(_.lte(new Date(end)))
      })
    }
    if (filter.status === 'upcoming') {
      query = query.where({ examDate: _.gte(new Date()) })
    } else if (filter.status === 'past') {
      query = query.where({ examDate: _.lt(new Date()) })
    }
  }
  
  const result = await query
    .orderBy('examDate', 'asc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return { success: true, data: result.data, total: result.data.length }
}

// 添加考试
async function addExam(openid, examData) {
  const exam = {
    ...examData,
    _openid: openid,
    createTime: new Date(),
    updateTime: new Date()
  }
  
  const result = await db.collection('exams').add({ data: exam })
  
  // 如果设置了提醒，创建提醒记录
  if (examData.reminderDays && examData.reminderDays > 0) {
    await createExamReminder(openid, result._id, exam)
  }
  
  return { success: true, data: { _id: result._id, ...exam } }
}

// 更新考试
async function updateExam(openid, { examId, updates }) {
  const updateData = {
    ...updates,
    updateTime: new Date()
  }
  
  const result = await db.collection('exams')
    .where({ _id: examId, _openid: openid })
    .update({ data: updateData })
  
  return { success: true, data: result }
}

// 删除考试
async function deleteExam(openid, { examId }) {
  // 删除考试记录
  const result = await db.collection('exams')
    .where({ _id: examId, _openid: openid })
    .remove()
  
  // 删除相关提醒
  await db.collection('exam_reminders')
    .where({ examId: examId, _openid: openid })
    .remove()
  
  return { success: true, data: result }
}

// 获取即将到来的考试
async function getUpcomingExams(openid, params) {
  const { days = 7, limit = 10 } = params || {}
  
  const now = new Date()
  const futureDate = new Date()
  futureDate.setDate(futureDate.getDate() + days)
  
  const result = await db.collection('exams')
    .where({
      _openid: openid,
      examDate: _.gte(now).and(_.lte(futureDate))
    })
    .orderBy('examDate', 'asc')
    .limit(limit)
    .get()
  
  return { success: true, data: result.data }
}

// 获取考试统计
async function getExamStats(openid, params) {
  const { dateRange } = params || {}
  
  let query = db.collection('exams').where({ _openid: openid })
  
  if (dateRange) {
    const { start, end } = dateRange
    query = query.where({
      examDate: _.gte(new Date(start)).and(_.lte(new Date(end)))
    })
  }
  
  const allExams = await query.get()
  const exams = allExams.data
  const now = new Date()
  
  const stats = {
    total: exams.length,
    upcoming: exams.filter(e => new Date(e.examDate) >= now).length,
    past: exams.filter(e => new Date(e.examDate) < now).length,
    thisWeek: exams.filter(e => {
      const examDate = new Date(e.examDate)
      const weekFromNow = new Date()
      weekFromNow.setDate(weekFromNow.getDate() + 7)
      return examDate >= now && examDate <= weekFromNow
    }).length,
    bySubject: {}
  }
  
  // 按科目统计
  exams.forEach(exam => {
    if (exam.subject) {
      stats.bySubject[exam.subject] = (stats.bySubject[exam.subject] || 0) + 1
    }
  })
  
  return { success: true, data: stats }
}

// 设置考试提醒
async function setExamReminder(openid, { examId, reminderDays }) {
  const exam = await db.collection('exams').doc(examId).get()
  
  if (!exam.data || exam.data._openid !== openid) {
    return { success: false, error: '考试不存在或无权限' }
  }
  
  await createExamReminder(openid, examId, exam.data, reminderDays)
  
  return { success: true }
}

// 创建考试提醒
async function createExamReminder(openid, examId, examData, reminderDays = null) {
  const days = reminderDays || examData.reminderDays || 3
  const examDate = new Date(examData.examDate)
  const reminderDate = new Date(examDate)
  reminderDate.setDate(reminderDate.getDate() - days)
  
  const reminder = {
    _openid: openid,
    examId: examId,
    examTitle: examData.title,
    examSubject: examData.subject,
    examDate: examDate,
    reminderDate: reminderDate,
    reminderDays: days,
    sent: false,
    createTime: new Date()
  }
  
  await db.collection('exam_reminders').add({ data: reminder })
}
