/* pages/notification-center/index.wxss */

/* 通知头部 */
.header-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.header-actions {
  display: flex;
  gap: 12rpx;
}

.mark-all-read-btn {
  background-color: transparent;
  color: #1890FF;
  border: 1rpx solid #1890FF;
  border-radius: 6rpx;
  padding: 6rpx 12rpx;
  font-size: 24rpx;
}

.settings-btn {
  background-color: #F6F6F6;
  color: #666666;
  border: 1rpx solid #D9D9D9;
  border-radius: 6rpx;
  padding: 8rpx;
  width: 48rpx;
  height: 48rpx;
  font-size: 24rpx;
}

.notification-stats {
  display: flex;
  justify-content: space-around;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  margin-bottom: 4rpx;
  display: block;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
  display: block;
}

/* 筛选器 */
.filter-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.filter-tabs {
  display: flex;
  gap: 8rpx;
  overflow-x: auto;
}

.filter-tab {
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: #1890FF;
  color: #FFFFFF;
}

.filter-count {
  margin-left: 4rpx;
  font-size: 20rpx;
}

/* 通知列表 */
.notifications-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.notification-group {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  background-color: #F8F9FA;
  border-bottom: 1rpx solid #F0F0F0;
}

.group-date {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
}

.group-count {
  font-size: 22rpx;
  color: #666666;
}

.group-notifications {
  display: flex;
  flex-direction: column;
}

.notification-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
  transition: all 0.3s ease;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background-color: #F6FFED;
}

.notification-item:active {
  background-color: #F0F0F0;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  position: relative;
}

.notification-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  flex-shrink: 0;
}

.notification-body {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.notification-title {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  margin-right: 12rpx;
}

.notification-item.unread .notification-title {
  font-weight: 500;
}

.notification-time {
  font-size: 22rpx;
  color: #999999;
  flex-shrink: 0;
}

.notification-message {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: block;
}

.notification-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 12rpx;
}

.notification-action {
  border-radius: 6rpx;
  padding: 6rpx 12rpx;
  font-size: 24rpx;
  border: 1rpx solid;
}

.notification-action.primary {
  background-color: #E6F7FF;
  border-color: #1890FF;
  color: #1890FF;
}

.notification-action.secondary {
  background-color: #FFFFFF;
  border-color: #D9D9D9;
  color: #666666;
}

.unread-indicator {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #1890FF;
  flex-shrink: 0;
  margin-top: 8rpx;
}

/* 空状态 */
.empty-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  display: block;
  opacity: 0.3;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
  display: block;
}

.enable-notifications-btn {
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 操作菜单 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.action-sheet {
  background-color: #FFFFFF;
  border-radius: 16rpx 16rpx 0 0;
  width: 100%;
  animation: slideUp 0.3s ease-out;
}

.action-sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.action-sheet-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.action-sheet-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.action-sheet-body {
  padding: 16rpx 0 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background-color: transparent;
  border: none;
  padding: 16rpx 32rpx;
  width: 100%;
  text-align: left;
}

.action-item:active {
  background-color: #F8F9FA;
}

.action-icon {
  font-size: 24rpx;
  width: 32rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333333;
}

/* 设置弹窗 */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.modal-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-section {
  margin-bottom: 32rpx;
}

.setting-section:last-child {
  margin-bottom: 0;
}

.setting-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-name {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 4rpx;
  display: block;
}

.setting-description {
  font-size: 22rpx;
  color: #666666;
  display: block;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #333333;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
