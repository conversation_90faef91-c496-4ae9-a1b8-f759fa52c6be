/* pages/add-exam/index.wxss */

/* 容器 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.header {
  background-color: #FFFFFF;
  padding: 40rpx 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #7F8C8D;
}

/* 表单容器 */
.form-container {
  padding: 32rpx;
}

/* 表单组 */
.form-group {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #2C3E50;
  display: block;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #2C3E50;
  background-color: #F8F9FA;
}

.form-input:focus {
  border-color: #3498DB;
  background-color: #FFFFFF;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #2C3E50;
  background-color: #F8F9FA;
}

.char-count {
  font-size: 22rpx;
  color: #95A5A6;
  text-align: right;
  margin-top: 8rpx;
  display: block;
}

/* 日期时间选择 */
.datetime-container {
  display: flex;
  gap: 16rpx;
}

.datetime-picker {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  background-color: #F8F9FA;
}

.datetime-text {
  font-size: 26rpx;
  color: #2C3E50;
}

.picker-icon {
  font-size: 24rpx;
}

/* 重要程度选择 */
.importance-options {
  display: flex;
  gap: 12rpx;
}

.importance-btn {
  flex: 1;
  padding: 16rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  background-color: #F8F9FA;
  color: #7F8C8D;
  font-size: 24rpx;
  text-align: center;
}

.importance-btn.active {
  border-color: #3498DB;
  background-color: #E3F2FD;
  color: #2196F3;
}

.importance-btn.active.low {
  border-color: #4CAF50;
  background-color: #E8F5E8;
  color: #4CAF50;
}

.importance-btn.active.medium {
  border-color: #FF9800;
  background-color: #FFF3E0;
  color: #FF9800;
}

.importance-btn.active.high {
  border-color: #F44336;
  background-color: #FFEBEE;
  color: #F44336;
}

/* 考试类型选择 */
.type-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
}

.type-btn {
  padding: 20rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  background-color: #F8F9FA;
  color: #7F8C8D;
  font-size: 24rpx;
  text-align: center;
}

.type-btn.active {
  border-color: #3498DB;
  background-color: #E3F2FD;
  color: #2196F3;
}

/* 提醒设置 */
.reminder-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.reminder-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.reminder-text {
  font-size: 26rpx;
  color: #2C3E50;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background-color: #FFFFFF;
  border-top: 1rpx solid #E9ECEF;
  z-index: 100;
}

.cancel-btn {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  background-color: #F8F9FA;
  color: #7F8C8D;
  font-size: 28rpx;
}

.save-btn {
  flex: 2;
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.save-btn.enabled {
  background-color: #3498DB;
  color: #FFFFFF;
}

.save-btn.disabled {
  background-color: #BDC3C7;
  color: #FFFFFF;
}

/* 成功提示 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.success-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx;
  text-align: center;
  animation: slideUp 0.3s ease-out;
}

.success-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.success-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
  display: block;
  margin-bottom: 12rpx;
}

.success-desc {
  font-size: 24rpx;
  color: #7F8C8D;
  display: block;
  margin-bottom: 40rpx;
}

.success-btn {
  background-color: #27AE60;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 动画 */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
