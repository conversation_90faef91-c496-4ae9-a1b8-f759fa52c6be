/* pages/add-task/index.wxss */

/* 表单区域 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.add-subtask-btn {
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.add-subtask-btn:active {
  background-color: #096DD9;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
  font-weight: 500;
}

.form-input {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.form-input:focus {
  border-color: #1890FF;
  background-color: #FFFFFF;
}

.form-textarea {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
  min-height: 120rpx;
}

.form-textarea:focus {
  border-color: #1890FF;
  background-color: #FFFFFF;
}

/* 选择器 */
.selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
}

.selector-text {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.selector-arrow {
  font-size: 24rpx;
  color: #999999;
}

/* 学科标签 */
.subject-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.subject-tag {
  background-color: #F5F5F5;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.subject-tag.active {
  background-color: #1890FF;
  color: #FFFFFF;
  border-color: #1890FF;
}

/* 优先级选项 */
.priority-options {
  display: flex;
  gap: 16rpx;
}

.priority-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 16rpx;
  transition: all 0.3s ease;
}

.priority-option.active {
  background-color: #E6F7FF;
  border-color: #1890FF;
}

.priority-icon {
  font-size: 32rpx;
}

.priority-text {
  font-size: 22rpx;
  color: #333333;
}

/* 日期时间选择器 */
.datetime-picker {
  display: flex;
  gap: 16rpx;
}

.picker-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.picker-icon {
  font-size: 24rpx;
  color: #999999;
}

/* 时长选择器 */
.duration-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.duration-option {
  background-color: #F5F5F5;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.duration-option.active {
  background-color: #1890FF;
  color: #FFFFFF;
  border-color: #1890FF;
}

/* 子任务 */
.subtasks-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.subtask-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.subtask-input {
  flex: 1;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  font-size: 24rpx;
  color: #333333;
}

.remove-subtask-btn {
  width: 48rpx;
  height: 48rpx;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border: none;
  border-radius: 50%;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-subtask-btn:active {
  background-color: #D9363E;
}

.empty-subtasks {
  text-align: center;
  padding: 40rpx 0;
}

.empty-text {
  font-size: 24rpx;
  color: #999999;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-label {
  font-size: 26rpx;
  color: #333333;
}

/* 提醒选项 */
.reminder-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.reminder-option {
  background-color: #F5F5F5;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.reminder-option.active {
  background-color: #1890FF;
  color: #FFFFFF;
  border-color: #1890FF;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  position: sticky;
  bottom: 0;
  border-top: 1rpx solid #F0F0F0;
}

.cancel-btn {
  flex: 1;
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 28rpx;
}

.cancel-btn:active {
  background-color: #E6E6E6;
}

.submit-btn {
  flex: 2;
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 28rpx;
}

.submit-btn:active {
  background-color: #096DD9;
}

/* 考试选择弹窗 */
.exam-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.modal-body {
  padding: 16rpx 0;
  max-height: 60vh;
  overflow-y: auto;
}

.exam-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;
}

.exam-option:active {
  background-color: #F8F9FA;
}

.exam-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.exam-date {
  font-size: 24rpx;
  color: #666666;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
