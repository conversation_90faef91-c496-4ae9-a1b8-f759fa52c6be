# 微信小程序"要考试啦"部署指南

## 🚀 快速开始

### 1. 环境准备
- 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 注册微信小程序账号（如需发布）

### 2. 项目导入
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录：`kaoba-wx`
4. 输入AppID（测试可使用测试号）
5. 点击"导入"

### 3. 项目编译
- 项目导入后会自动编译
- 确保编译无错误
- 在模拟器中预览效果

## 📱 功能使用指南

### 🍅 增强版番茄钟使用

#### 快速专注模式
1. 打开番茄钟页面
2. 确保选中"快速专注"模式
3. 可选择背景音（雨声、海浪等）
4. 点击开始按钮开始25分钟专注
5. 可点击"专注模式"进入全屏体验

#### 任务专注模式
1. 选择"任务专注"模式
2. 点击"选择要学习的任务"
3. 从任务列表中选择一个任务
4. 查看智能拆解建议
5. 确认计划后开始学习

#### 专注模式体验
1. 点击"专注模式"按钮
2. 进入全屏深色界面
3. 享受沉浸式学习体验
4. 点击左上角×退出专注模式

#### 声音设置
1. 点击"声音设置"按钮
2. 选择喜欢的背景音
3. 调节音量大小
4. 开启/关闭提示音

### 📅 考试管理
1. 在考试中心创建考试
2. 设置考试时间和科目
3. 查看实时倒计时
4. 跟踪准备进度

### 📝 任务管理
1. 在任务中心创建学习任务
2. 设置优先级和截止时间
3. 添加子任务分解
4. 关联到番茄钟学习

### 📊 数据分析
1. 在数据中心查看学习统计
2. 分析学习效率和趋势
3. 查看考试准备度评分
4. 导出学习数据

## 🔧 开发者指南

### 项目结构
```
kaoba-wx/
├── app.js                 # 应用入口
├── app.json              # 应用配置
├── app.wxss              # 全局样式
├── custom-tab-bar/       # 自定义TabBar
├── pages/                # 页面目录
│   ├── index/           # 首页
│   ├── exam-center/     # 考试中心
│   ├── task-center/     # 任务中心
│   ├── pomodoro/        # 增强版番茄钟
│   ├── profile/         # 我的页面
│   └── ...              # 其他功能页面
├── sounds/              # 音频文件目录
├── uix/                 # UI设计文件
└── utils/               # 工具函数
```

### 核心技术栈
- **框架**: 微信小程序原生框架
- **UI**: 自定义组件 + emoji图标系统
- **数据**: 本地存储 + 状态管理
- **音频**: wx.createInnerAudioContext()
- **动画**: CSS3 + 微信小程序动画API

### 关键特性
- **自定义TabBar**: 灵活的导航系统
- **emoji图标**: 解决图片资源问题
- **数据可视化**: 圆形进度、统计图表
- **音频系统**: 背景音 + 提示音
- **专注模式**: 全屏沉浸式体验

## 🎵 音频文件配置

### 音频文件位置
将音频文件放置在 `sounds/` 目录下：
- `rain.mp3` - 雨声
- `ocean.mp3` - 海浪声
- `cafe.mp3` - 咖啡厅
- `forest.mp3` - 森林声
- `whitenoise.mp3` - 白噪音
- `start.mp3` - 开始提示音
- `pause.mp3` - 暂停提示音
- `complete.mp3` - 完成提示音
- `warning.mp3` - 警告提示音

### 音频格式要求
- 格式：MP3
- 大小：建议 < 1MB
- 时长：背景音可循环，提示音1-3秒

## 📦 发布部署

### 1. 代码审查
- 检查所有功能正常工作
- 确保无语法错误
- 测试各种使用场景

### 2. 版本管理
- 更新版本号
- 编写版本说明
- 提交代码变更

### 3. 小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台
4. 提交审核
5. 审核通过后发布

## 🐛 常见问题

### Q: 音频无法播放？
A: 检查音频文件路径和格式，确保文件存在且为MP3格式。

### Q: 专注模式无法进入？
A: 检查页面状态管理，确保focusMode状态正确切换。

### Q: 任务拆解不显示？
A: 检查任务数据结构，确保任务对象包含必要字段。

### Q: 数据丢失？
A: 检查本地存储逻辑，确保数据正确保存到wx.storage。

### Q: 页面卡顿？
A: 检查动画性能，减少不必要的setData调用。

## 📞 技术支持

如遇到技术问题，请检查：
1. 微信开发者工具版本
2. 项目代码完整性
3. 音频文件配置
4. 网络连接状态

## 🎯 后续优化建议

1. **性能优化**
   - 图片懒加载
   - 代码分包
   - 缓存策略优化

2. **功能扩展**
   - 云端数据同步
   - 社交功能
   - AI学习建议

3. **用户体验**
   - 个性化主题
   - 更多背景音
   - 手势操作

---

**项目版本**: v2.0.0
**最后更新**: 2025-06-28
**开发团队**: Augment Code
