// pages/pomodoro-complete/index.js
Page({
  data: {
    sessionData: {
      duration: '25分钟',
      efficiency: 88,
      taskName: '数学高数第一章复习',
      streak: '15天',
      startTime: '14:30',
      endTime: '14:55',
      isNewRecord: false,
      rating: 0,
      ratingText: '请为这次专注打分',
      feedback: '',
      notes: ''
    },

    ratingStars: [
      { value: 1, filled: false },
      { value: 2, filled: false },
      { value: 3, filled: false },
      { value: 4, filled: false },
      { value: 5, filled: false }
    ],

    todayAchievements: [
      {
        id: 'daily_goal',
        icon: '🎯',
        name: '每日目标',
        progress: '4/5',
        unlocked: false
      },
      {
        id: 'focus_master',
        icon: '🧘',
        name: '专注大师',
        progress: '15/20',
        unlocked: false
      },
      {
        id: 'streak_keeper',
        icon: '🔥',
        name: '坚持达人',
        progress: '15天',
        unlocked: true
      }
    ],

    breakSuggestion: {
      icon: '☕',
      title: '短暂休息',
      description: '建议休息5-10分钟，活动一下身体',
      duration: '5分钟'
    },

    todayStats: {
      completedPomodoros: 4,
      totalTime: '2.5h',
      goalProgress: 83
    },

    selectedBreak: '',
    breakOptions: [
      { value: '5min', label: '5分钟休息' },
      { value: '10min', label: '10分钟休息' },
      { value: '15min', label: '15分钟休息' },
      { value: 'none', label: '不休息' }
    ],

    showBreakTimer: false,
    breakProgress: 0,
    breakTimeDisplay: '05:00',
    breakRunning: false,
    breakTimer: null
  },

  onLoad(options) {
    this.loadSessionData(options)
    this.initRating()
  },

  onUnload() {
    this.saveSessionData()
    this.clearBreakTimer()
  },

  // 加载会话数据
  loadSessionData(options) {
    // 从参数中获取番茄钟会话数据
    const duration = options.duration || '25'
    const taskId = options.taskId || ''
    const startTime = options.startTime || ''

    // 计算结束时间
    const now = new Date()
    const endTime = now.toTimeString().slice(0, 5)

    // 模拟效率计算（实际应该基于用户行为）
    const efficiency = Math.floor(Math.random() * 20) + 80 // 80-100%

    // 更新会话数据
    this.setData({
      'sessionData.duration': `${duration}分钟`,
      'sessionData.startTime': startTime,
      'sessionData.endTime': endTime,
      'sessionData.efficiency': efficiency
    })

    // 检查是否创造新纪录
    this.checkNewRecord()
  },

  // 初始化评分
  initRating() {
    const stars = this.data.ratingStars.map(star => ({
      ...star,
      filled: false
    }))

    this.setData({ ratingStars: stars })
  },

  // 检查新纪录
  checkNewRecord() {
    // 模拟检查逻辑
    const isNewRecord = Math.random() > 0.8 // 20%概率创造新纪录

    if (isNewRecord) {
      this.setData({
        'sessionData.isNewRecord': true
      })

      wx.showToast({
        title: '🏆 创造新纪录！',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 评分
  rateSession(e) {
    const rating = e.currentTarget.dataset.rating
    const stars = this.data.ratingStars.map((star, index) => ({
      ...star,
      filled: index < rating
    }))

    const ratingTexts = [
      '',
      '需要改进',
      '一般般',
      '还不错',
      '很棒',
      '完美！'
    ]

    this.setData({
      ratingStars: stars,
      'sessionData.rating': rating,
      'sessionData.ratingText': ratingTexts[rating]
    })
  },

  // 更新笔记
  updateNotes(e) {
    this.setData({
      'sessionData.notes': e.detail.value
    })
  },

  // 继续学习
  continueStudy() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  // 休息一下
  takeBreak() {
    this.setData({ showBreakTimer: true })
    this.startBreakTimer(5) // 默认5分钟休息
  },

  // 查看任务
  viewTasks() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 查看统计
  viewStats() {
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  }
})