{"pageInfo": {"pageName": "数据导出", "pageId": "dataExport", "pageType": "page", "description": "学习数据导出和分享页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "page-title", "type": "text", "content": "数据导出", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "page-subtitle", "type": "text", "content": "导出你的学习数据，随时备份和分享", "style": {"fontSize": "28rpx", "color": "#666666"}}]}, {"id": "export-options", "type": "view", "className": "export-options-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "options-title", "type": "text", "content": "导出选项", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "data-types", "type": "view", "children": [{"id": "data-type-item", "type": "view", "forEach": "{{exportDataTypes}}", "forItem": "dataType", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "justifyContent": "space-between", "padding": "20rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "data-type-info", "type": "view", "style": {"flex": "1"}, "children": [{"id": "data-type-name", "type": "text", "content": "{{dataType.name}}", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "data-type-description", "type": "text", "content": "{{dataType.description}}", "style": {"fontSize": "24rpx", "color": "#666666", "marginBottom": "4rpx"}}, {"id": "data-type-count", "type": "text", "content": "{{dataType.count}} 条记录", "style": {"fontSize": "22rpx", "color": "#999999"}}]}, {"id": "data-type-checkbox", "type": "checkbox", "checked": "{{dataType.selected}}", "style": {"marginLeft": "16rpx"}, "events": {"change": "toggleDataType"}}]}]}, {"id": "select-all-actions", "type": "view", "style": {"display": "flex", "gap": "16rpx", "marginTop": "20rpx"}, "children": [{"id": "select-all-btn", "type": "button", "content": "全选", "style": {"fontSize": "24rpx", "color": "#1890FF", "backgroundColor": "transparent", "border": "1rpx solid #1890FF", "borderRadius": "6rpx", "padding": "6rpx 12rpx"}, "events": {"tap": "selectAllDataTypes"}}, {"id": "deselect-all-btn", "type": "button", "content": "取消全选", "style": {"fontSize": "24rpx", "color": "#666666", "backgroundColor": "transparent", "border": "1rpx solid #D9D9D9", "borderRadius": "6rpx", "padding": "6rpx 12rpx"}, "events": {"tap": "deselectAllDataTypes"}}]}]}, {"id": "export-formats", "type": "view", "className": "export-formats-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "formats-title", "type": "text", "content": "导出格式", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "format-options", "type": "view", "style": {"display": "flex", "gap": "16rpx", "flexWrap": "wrap"}, "children": [{"id": "format-option", "type": "button", "forEach": "{{exportFormats}}", "forItem": "format", "forIndex": "index", "style": {"fontSize": "26rpx", "padding": "12rpx 20rpx", "borderRadius": "8rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{selectedFormat === format.id ? '#E6F7FF' : '#FFFFFF'}}", "color": "{{selectedFormat === format.id ? '#1890FF' : '#333333'}}", "display": "flex", "alignItems": "center", "gap": "8rpx"}, "events": {"tap": "selectExportFormat"}, "children": [{"type": "text", "content": "{{format.icon}}"}, {"type": "text", "content": "{{format.name}}"}]}]}, {"id": "format-description", "type": "view", "condition": "{{selectedFormatInfo}}", "style": {"marginTop": "20rpx", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"id": "format-desc-text", "type": "text", "content": "{{selectedFormatInfo.description}}", "style": {"fontSize": "24rpx", "color": "#666666", "lineHeight": "1.4"}}]}]}, {"id": "date-range", "type": "view", "className": "date-range-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "date-range-title", "type": "text", "content": "时间范围", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "date-range-options", "type": "view", "style": {"display": "flex", "gap": "12rpx", "flexWrap": "wrap", "marginBottom": "24rpx"}, "children": [{"id": "date-range-option", "type": "button", "forEach": "{{dateRangeOptions}}", "forItem": "option", "forIndex": "index", "content": "{{option.label}}", "style": {"fontSize": "24rpx", "padding": "8rpx 16rpx", "borderRadius": "6rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{selectedDateRange === option.value ? '#E6F7FF' : '#FFFFFF'}}", "color": "{{selectedDateRange === option.value ? '#1890FF' : '#333333'}}"}, "events": {"tap": "selectDateRange"}}]}, {"id": "custom-date-range", "type": "view", "condition": "{{selectedDateRange === 'custom'}}", "style": {"display": "flex", "gap": "16rpx", "alignItems": "center"}, "children": [{"id": "start-date-picker", "type": "picker", "mode": "date", "value": "{{customStartDate}}", "style": {"flex": "1", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "12rpx", "border": "1rpx solid #E9ECEF"}, "events": {"change": "selectStartDate"}, "children": [{"type": "text", "content": "{{customStartDate || '开始日期'}}", "style": {"fontSize": "26rpx", "color": "{{customStartDate ? '#333333' : '#999999'}}"}}]}, {"id": "date-separator", "type": "text", "content": "至", "style": {"fontSize": "24rpx", "color": "#666666"}}, {"id": "end-date-picker", "type": "picker", "mode": "date", "value": "{{customEndDate}}", "style": {"flex": "1", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "12rpx", "border": "1rpx solid #E9ECEF"}, "events": {"change": "selectEndDate"}, "children": [{"type": "text", "content": "{{customEndDate || '结束日期'}}", "style": {"fontSize": "26rpx", "color": "{{customEndDate ? '#333333' : '#999999'}}"}}]}]}]}, {"id": "export-preview", "type": "view", "condition": "{{showPreview}}", "className": "export-preview-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "preview-title", "type": "text", "content": "导出预览", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "preview-stats", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr", "gap": "16rpx", "marginBottom": "24rpx"}, "children": [{"id": "preview-stat", "type": "view", "forEach": "{{previewStats}}", "forItem": "stat", "forIndex": "index", "style": {"textAlign": "center", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{stat.value}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#1890FF", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{stat.label}}", "style": {"fontSize": "22rpx", "color": "#666666"}}]}]}, {"id": "preview-sample", "type": "view", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "marginBottom": "20rpx"}, "children": [{"id": "sample-title", "type": "text", "content": "数据示例：", "style": {"fontSize": "24rpx", "color": "#666666", "marginBottom": "8rpx"}}, {"id": "sample-content", "type": "text", "content": "{{previewSample}}", "style": {"fontSize": "22rpx", "color": "#333333", "fontFamily": "monospace", "lineHeight": "1.4"}}]}, {"id": "file-size-info", "type": "text", "content": "预计文件大小：{{estimatedFileSize}}", "style": {"fontSize": "24rpx", "color": "#999999", "textAlign": "center"}}]}, {"id": "export-actions", "type": "view", "className": "export-actions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "action-buttons", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "16rpx"}, "children": [{"id": "preview-btn", "type": "button", "content": "预览数据", "disabled": "{{!hasSelectedData}}", "style": {"fontSize": "28rpx", "color": "{{hasSelectedData ? '#1890FF' : '#999999'}}", "backgroundColor": "{{hasSelectedData ? '#E6F7FF' : '#F5F5F5'}}", "border": "1rpx solid {{hasSelectedData ? '#91D5FF' : '#D9D9D9'}}", "borderRadius": "8rpx", "padding": "16rpx"}, "events": {"tap": "previewExportData"}}, {"id": "export-btn", "type": "button", "content": "开始导出", "disabled": "{{!hasSelectedData}}", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "{{hasSelectedData ? '#52C41A' : '#D9D9D9'}}", "borderRadius": "8rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "startExport"}}]}, {"id": "export-tips", "type": "view", "style": {"marginTop": "24rpx", "backgroundColor": "#FFF7E6", "border": "1rpx solid #FFD591", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"id": "tips-title", "type": "text", "content": "💡 导出提示", "style": {"fontSize": "24rpx", "color": "#FA8C16", "fontWeight": "500", "marginBottom": "8rpx"}}, {"id": "tips-content", "type": "text", "content": "• 导出的数据仅保存在本地，不会上传到服务器\n• 建议定期备份重要的学习数据\n• 大量数据导出可能需要较长时间，请耐心等待", "style": {"fontSize": "22rpx", "color": "#666666", "lineHeight": "1.5"}}]}]}], "data": {"selectedFormat": "json", "selectedDateRange": "all", "customStartDate": "", "customEndDate": "", "showPreview": false, "hasSelectedData": true, "estimatedFileSize": "2.3 MB", "exportDataTypes": [{"id": "tasks", "name": "学习任务", "description": "所有创建的学习任务和完成记录", "count": 156, "selected": true}, {"id": "exams", "name": "考试信息", "description": "考试安排和备考进度", "count": 8, "selected": true}, {"id": "pomodoro", "name": "专注记录", "description": "番茄钟学习会话记录", "count": 342, "selected": true}, {"id": "statistics", "name": "学习统计", "description": "每日学习时长和效率数据", "count": 89, "selected": false}, {"id": "settings", "name": "个人设置", "description": "应用配置和偏好设置", "count": 1, "selected": false}], "exportFormats": [{"id": "json", "name": "JSON", "icon": "📄", "description": "结构化数据格式，便于程序处理和导入其他应用"}, {"id": "csv", "name": "CSV", "icon": "📊", "description": "表格格式，可用Excel等软件打开，便于数据分析"}, {"id": "pdf", "name": "PDF", "icon": "📋", "description": "可读性强的报告格式，适合打印和分享"}, {"id": "txt", "name": "TXT", "icon": "📝", "description": "纯文本格式，兼容性最好，文件最小"}], "selectedFormatInfo": {"description": "结构化数据格式，便于程序处理和导入其他应用"}, "dateRangeOptions": [{"value": "all", "label": "全部时间"}, {"value": "last_week", "label": "最近一周"}, {"value": "last_month", "label": "最近一月"}, {"value": "last_3months", "label": "最近三月"}, {"value": "this_year", "label": "今年"}, {"value": "custom", "label": "自定义"}], "previewStats": [{"label": "数据条数", "value": "506"}, {"label": "时间跨度", "value": "89天"}, {"label": "文件大小", "value": "2.3MB"}, {"label": "导出格式", "value": "JSON"}], "previewSample": "{\n  \"tasks\": [\n    {\n      \"id\": \"task_001\",\n      \"title\": \"数学高数第一章复习\",\n      \"completed\": true,\n      \"createTime\": \"2025-06-20\"\n    }\n  ]\n}"}, "methods": {"toggleDataType": {"type": "function", "description": "切换数据类型选择", "params": ["dataTypeId"], "implementation": "updateDataTypeSelection"}, "selectAllDataTypes": {"type": "function", "description": "选择所有数据类型", "implementation": "selectAllTypes"}, "deselectAllDataTypes": {"type": "function", "description": "取消选择所有数据类型", "implementation": "deselectAllTypes"}, "selectExportFormat": {"type": "function", "description": "选择导出格式", "params": ["formatId"], "implementation": "updateExportFormat"}, "selectDateRange": {"type": "function", "description": "选择时间范围", "params": ["rangeValue"], "implementation": "updateDateRange"}, "selectStartDate": {"type": "function", "description": "选择开始日期", "params": ["date"], "implementation": "setCustomStartDate"}, "selectEndDate": {"type": "function", "description": "选择结束日期", "params": ["date"], "implementation": "setCustomEndDate"}, "previewExportData": {"type": "function", "description": "预览导出数据", "implementation": "generatePreview"}, "startExport": {"type": "function", "description": "开始导出", "implementation": "executeExport"}}, "lifecycle": {"onLoad": ["initExportOptions"], "onShow": ["refreshDataCounts"]}}