/* pages/pomodoro/index.wxss - 重新设计版本 */

/* 容器布局 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container.focus-mode {
  background-color: #1A1A1A;
  color: #FFFFFF;
}

.normal-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 32rpx;
}

/* 简洁头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  height: 120rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
}

.settings-btn {
  background: none;
  border: none;
  font-size: 32rpx;
  color: #7F8C8D;
  padding: 8rpx;
}

/* 任务信息区域 */
.task-info-area {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.current-task {
  text-align: center;
}

.task-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.task-progress {
  font-size: 24rpx;
  color: #27AE60;
}

.no-task {
  text-align: center;
}

.select-hint {
  font-size: 28rpx;
  color: #7F8C8D;
  display: block;
  margin-bottom: 8rpx;
}

.quick-mode-hint {
  font-size: 22rpx;
  color: #BDC3C7;
}

/* 主计时器区域 */
.main-timer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
}

.timer-circle {
  width: 400rpx;
  height: 400rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.15);
}

.timer-circle.breathing {
  animation: breathe 3s ease-in-out infinite;
}

.timer-inner {
  width: 340rpx;
  height: 340rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.time-display {
  font-size: 72rpx;
  font-weight: 300;
  color: #2C3E50;
  font-family: 'SF Mono', monospace;
  margin-bottom: 12rpx;
  letter-spacing: 2rpx;
}

.session-type {
  font-size: 24rpx;
  color: #7F8C8D;
  font-weight: 500;
}

/* 主要控制按钮 */
.primary-controls {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.main-control-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: none;
  font-size: 48rpx;
  color: #FFFFFF;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.main-control-btn.start {
  background-color: #27AE60;
}

.main-control-btn.pause {
  background-color: #F39C12;
}

.main-control-btn:active {
  transform: scale(0.95);
}

.stop-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: none;
  font-size: 32rpx;
  background-color: #E74C3C;
  color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(231,76,60,0.3);
}

/* 底部快捷操作 */
.bottom-actions {
  display: flex;
  justify-content: space-around;
  padding: 32rpx 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.1);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background: none;
  border: none;
  padding: 16rpx;
  min-width: 120rpx;
}

.action-icon {
  font-size: 32rpx;
}

.action-label {
  font-size: 22rpx;
  color: #7F8C8D;
}

/* 专注模式 */
.focus-layout {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #1A1A1A;
  color: #FFFFFF;
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

.focus-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  height: 120rpx;
}

.exit-focus-btn {
  font-size: 40rpx;
  color: #FFFFFF;
  background: none;
  border: none;
  padding: 8rpx;
}

.focus-title {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.focus-placeholder {
  width: 56rpx;
}

.focus-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 40rpx;
}

.focus-timer-display {
  margin-bottom: 80rpx;
}

.focus-timer-circle {
  width: 480rpx;
  height: 480rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: breathe 4s ease-in-out infinite;
}

.focus-timer-inner {
  width: 400rpx;
  height: 400rpx;
  background-color: #1A1A1A;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.focus-time-text {
  font-size: 96rpx;
  font-weight: 200;
  color: #FFFFFF;
  font-family: 'SF Mono', monospace;
  margin-bottom: 16rpx;
  letter-spacing: 4rpx;
}

.focus-session-indicator {
  font-size: 24rpx;
  color: rgba(255,255,255,0.6);
}

.focus-task-info {
  text-align: center;
  margin-bottom: 80rpx;
}

.focus-task-title {
  font-size: 32rpx;
  font-weight: 400;
  color: #FFFFFF;
  margin-bottom: 12rpx;
  display: block;
}

.focus-task-subtitle {
  font-size: 24rpx;
  color: rgba(255,255,255,0.7);
}

.focus-controls {
  display: flex;
  align-items: center;
  gap: 40rpx;
}

.focus-control-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255,255,255,0.3);
  background-color: rgba(255,255,255,0.1);
  color: #FFFFFF;
  font-size: 40rpx;
}

.focus-stop-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255,255,255,0.2);
  background-color: rgba(255,255,255,0.05);
  color: rgba(255,255,255,0.6);
  font-size: 32rpx;
}

/* 弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.task-modal,
.sound-modal,
.settings-modal {
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2C3E50;
}

.modal-close {
  font-size: 36rpx;
  color: #7F8C8D;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 任务选择 */
.task-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.task-option:active {
  background-color: #F8F9FA;
  transform: scale(0.98);
}

.task-option.quick-focus {
  background-color: #E8F5E8;
  border: 1rpx solid #27AE60;
}

.task-icon {
  font-size: 32rpx;
  width: 48rpx;
  text-align: center;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.task-desc {
  font-size: 22rpx;
  color: #7F8C8D;
}

/* 声音设置 */
.sound-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2C3E50;
  margin-bottom: 20rpx;
  display: block;
}

.sound-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.sound-card {
  background-color: #F8F9FA;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.sound-card.active {
  background-color: #E8F5E8;
  border-color: #27AE60;
}

.sound-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  display: block;
}

.sound-name {
  font-size: 22rpx;
  color: #2C3E50;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.volume-text {
  font-size: 24rpx;
  color: #7F8C8D;
  min-width: 80rpx;
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.setting-label {
  font-size: 28rpx;
  color: #2C3E50;
}

.time-adjuster {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 1rpx solid #BDC3C7;
  background-color: #FFFFFF;
  color: #7F8C8D;
  font-size: 24rpx;
}

.time-value {
  font-size: 24rpx;
  color: #2C3E50;
  min-width: 120rpx;
  text-align: center;
}

/* 动画 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
