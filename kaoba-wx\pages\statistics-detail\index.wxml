<!--pages/statistics-detail/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <text class="page-title">📈 统计详情</text>
    <text class="page-subtitle">深度分析您的学习数据</text>
  </view>

  <!-- 时间范围选择 -->
  <view class="time-range-selector">
    <view class="range-tabs">
      <button class="range-tab {{timeRange === 'week' ? 'active' : ''}}"
              bindtap="switchTimeRange"
              data-range="week">
        本周
      </button>
      <button class="range-tab {{timeRange === 'month' ? 'active' : ''}}"
              bindtap="switchTimeRange"
              data-range="month">
        本月
      </button>
      <button class="range-tab {{timeRange === 'quarter' ? 'active' : ''}}"
              bindtap="switchTimeRange"
              data-range="quarter">
        本季度
      </button>
      <button class="range-tab {{timeRange === 'year' ? 'active' : ''}}"
              bindtap="switchTimeRange"
              data-range="year">
        本年
      </button>
    </view>
    <text class="range-desc">{{rangeDescription}}</text>
  </view>

  <!-- 核心指标 -->
  <view class="core-metrics">
    <text class="section-title">📊 核心指标</text>
    <view class="metrics-grid">
      <view class="metric-card primary">
        <view class="metric-icon">🍅</view>
        <view class="metric-content">
          <text class="metric-value">{{coreMetrics.totalPomodoros}}</text>
          <text class="metric-label">番茄钟总数</text>
          <text class="metric-change {{coreMetrics.pomodoroChange >= 0 ? 'positive' : 'negative'}}">
            {{coreMetrics.pomodoroChange >= 0 ? '+' : ''}}{{coreMetrics.pomodoroChange}}%
          </text>
        </view>
      </view>

      <view class="metric-card success">
        <view class="metric-icon">⏱️</view>
        <view class="metric-content">
          <text class="metric-value">{{coreMetrics.totalTime}}</text>
          <text class="metric-label">学习时长</text>
          <text class="metric-change {{coreMetrics.timeChange >= 0 ? 'positive' : 'negative'}}">
            {{coreMetrics.timeChange >= 0 ? '+' : ''}}{{coreMetrics.timeChange}}%
          </text>
        </view>
      </view>

      <view class="metric-card warning">
        <view class="metric-icon">📈</view>
        <view class="metric-content">
          <text class="metric-value">{{coreMetrics.avgEfficiency}}%</text>
          <text class="metric-label">平均效率</text>
          <text class="metric-change {{coreMetrics.efficiencyChange >= 0 ? 'positive' : 'negative'}}">
            {{coreMetrics.efficiencyChange >= 0 ? '+' : ''}}{{coreMetrics.efficiencyChange}}%
          </text>
        </view>
      </view>

      <view class="metric-card info">
        <view class="metric-icon">🎯</view>
        <view class="metric-content">
          <text class="metric-value">{{coreMetrics.completionRate}}%</text>
          <text class="metric-label">完成率</text>
          <text class="metric-change {{coreMetrics.completionChange >= 0 ? 'positive' : 'negative'}}">
            {{coreMetrics.completionChange >= 0 ? '+' : ''}}{{coreMetrics.completionChange}}%
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习趋势图表 -->
  <view class="trend-chart">
    <text class="section-title">📈 学习趋势</text>
    <view class="chart-container">
      <view class="chart-header">
        <text class="chart-title">每日学习时长 (分钟)</text>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color study"></view>
            <text class="legend-text">学习时长</text>
          </view>
          <view class="legend-item">
            <view class="legend-color target"></view>
            <text class="legend-text">目标时长</text>
          </view>
        </view>
      </view>

      <view class="chart-area">
        <view class="chart-y-axis">
          <text class="y-label">120</text>
          <text class="y-label">90</text>
          <text class="y-label">60</text>
          <text class="y-label">30</text>
          <text class="y-label">0</text>
        </view>

        <view class="chart-content">
          <view class="chart-bars">
            <view class="bar-group" wx:for="{{trendData}}" wx:key="date">
              <view class="bar study-bar" style="height: {{item.studyHeight}}%"></view>
              <view class="bar target-bar" style="height: {{item.targetHeight}}%"></view>
            </view>
          </view>

          <view class="chart-x-axis">
            <text class="x-label" wx:for="{{trendData}}" wx:key="date">{{item.label}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 科目分析 -->
  <view class="subject-analysis">
    <text class="section-title">📚 科目分析</text>
    <view class="subject-list">
      <view class="subject-item" wx:for="{{subjectStats}}" wx:key="subject">
        <view class="subject-header">
          <view class="subject-info">
            <text class="subject-icon">{{item.icon}}</text>
            <text class="subject-name">{{item.subject}}</text>
          </view>
          <text class="subject-time">{{item.totalTime}}</text>
        </view>

        <view class="subject-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.percentage}}%"></view>
          </view>
          <text class="progress-text">{{item.percentage}}%</text>
        </view>

        <view class="subject-details">
          <view class="detail-item">
            <text class="detail-label">番茄钟数</text>
            <text class="detail-value">{{item.pomodoros}}个</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">平均效率</text>
            <text class="detail-value">{{item.efficiency}}%</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">完成任务</text>
            <text class="detail-value">{{item.completedTasks}}个</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 时间分布 -->
  <view class="time-distribution">
    <text class="section-title">⏰ 时间分布</text>
    <view class="distribution-chart">
      <view class="time-slots">
        <view class="time-slot" wx:for="{{timeDistribution}}" wx:key="hour">
          <view class="slot-bar" style="height: {{item.height}}%"></view>
          <text class="slot-label">{{item.hour}}</text>
        </view>
      </view>
      <text class="distribution-desc">最佳学习时间：{{bestStudyTime}}</text>
    </view>
  </view>

  <!-- 学习建议 -->
  <view class="study-suggestions">
    <text class="section-title">💡 学习建议</text>
    <view class="suggestions-list">
      <view class="suggestion-item" wx:for="{{suggestions}}" wx:key="id">
        <view class="suggestion-icon {{item.type}}">{{item.icon}}</view>
        <view class="suggestion-content">
          <text class="suggestion-title">{{item.title}}</text>
          <text class="suggestion-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 导出数据 -->
  <view class="export-section">
    <text class="section-title">📤 数据导出</text>
    <view class="export-options">
      <button class="export-btn" bindtap="exportData" data-format="excel">
        <text class="export-icon">📊</text>
        <text class="export-text">导出Excel</text>
      </button>
      <button class="export-btn" bindtap="exportData" data-format="pdf">
        <text class="export-icon">📄</text>
        <text class="export-text">导出PDF报告</text>
      </button>
      <button class="export-btn" bindtap="shareReport">
        <text class="export-icon">📱</text>
        <text class="export-text">分享报告</text>
      </button>
    </view>
  </view>
</view>