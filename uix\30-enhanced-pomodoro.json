{"pageInfo": {"pageName": "增强版番茄钟", "pageId": "enhanced-pomodoro", "pageType": "tabPage", "description": "增强版番茄工作法专注计时页面，支持专注模式、任务关联、声音系统", "version": "2.0.0", "lastModified": "2025-06-28", "features": ["专注模式全屏显示", "智能任务关联与拆解", "多种背景音和提示音", "沉浸式学习体验", "智能时间管理", "任务进度可视化", "个性化声音设置"]}, "modes": {"normal": {"description": "普通模式 - 常规页面布局", "layout": "standard"}, "focus": {"description": "专注模式 - 全屏沉浸式体验", "layout": "fullscreen", "theme": "dark", "hideNavigation": true, "hideTabBar": true}}, "normalModeLayout": {"type": "view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx", "components": [{"id": "mode-selector", "type": "view", "className": "mode-selector-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "24rpx 32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "mode-title", "type": "text", "content": "学习模式", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "mode-options", "type": "view", "style": {"display": "flex", "gap": "16rpx"}, "children": [{"id": "quick-focus-btn", "type": "button", "content": "🚀 快速专注", "style": {"flex": "1", "backgroundColor": "{{studyMode === 'quick' ? '#E6F7FF' : '#F8F9FA'}}", "color": "{{studyMode === 'quick' ? '#1890FF' : '#666666'}}", "border": "1rpx solid {{studyMode === 'quick' ? '#1890FF' : '#E9ECEF'}}", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "26rpx"}, "events": {"tap": "selectStudyMode", "data": "quick"}}, {"id": "task-focus-btn", "type": "button", "content": "📋 任务专注", "style": {"flex": "1", "backgroundColor": "{{studyMode === 'task' ? '#E6F7FF' : '#F8F9FA'}}", "color": "{{studyMode === 'task' ? '#1890FF' : '#666666'}}", "border": "1rpx solid {{studyMode === 'task' ? '#1890FF' : '#E9ECEF'}}", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "26rpx"}, "events": {"tap": "selectStudyMode", "data": "task"}}]}]}, {"id": "task-association", "type": "view", "condition": "{{studyMode === 'task'}}", "className": "task-association-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "task-selection-title", "type": "text", "content": "📋 关联任务", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "selected-task-display", "type": "view", "condition": "{{selectedTask}}", "style": {"backgroundColor": "#F6FFED", "border": "1rpx solid #B7EB8F", "borderRadius": "8rpx", "padding": "16rpx", "marginBottom": "16rpx"}, "children": [{"id": "task-info", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}, "children": [{"id": "task-details", "type": "view", "children": [{"id": "task-title", "type": "text", "content": "{{selectedTask.title}}", "style": {"fontSize": "26rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "task-progress", "type": "text", "content": "进度：🍅{{selectedTask.completedPomodoros || 0}}/{{selectedTask.totalPomodoros || 1}}", "style": {"fontSize": "22rpx", "color": "#52C41A"}}]}, {"id": "change-task-btn", "type": "button", "content": "更换", "style": {"backgroundColor": "transparent", "color": "#1890FF", "border": "1rpx solid #1890FF", "borderRadius": "4rpx", "padding": "4rpx 12rpx", "fontSize": "22rpx"}, "events": {"tap": "showTaskSelector"}}]}]}, {"id": "task-breakdown", "type": "view", "condition": "{{selectedTask && selectedTask.breakdown}}", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "marginBottom": "16rpx"}, "children": [{"id": "breakdown-title", "type": "text", "content": "🧠 智能拆解建议", "style": {"fontSize": "24rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "breakdown-list", "type": "view", "children": [{"id": "breakdown-item", "type": "view", "forEach": "{{selectedTask.breakdown}}", "forItem": "item", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "gap": "8rpx", "marginBottom": "8rpx"}, "children": [{"id": "pomodoro-icon", "type": "text", "content": "{{item.completed ? '✅' : '🍅'}}", "style": {"fontSize": "20rpx"}}, {"id": "breakdown-text", "type": "text", "content": "{{item.title}} ({{item.duration}}min)", "style": {"fontSize": "22rpx", "color": "{{item.completed ? '#999999' : '#333333'}}", "textDecoration": "{{item.completed ? 'line-through' : 'none'}}"}}]}]}]}, {"id": "select-task-btn", "type": "button", "condition": "{{!selectedTask}}", "content": "📋 选择要学习的任务", "style": {"width": "100%", "backgroundColor": "#F8F9FA", "color": "#666666", "border": "1rpx dashed #D9D9D9", "borderRadius": "8rpx", "padding": "20rpx", "fontSize": "26rpx"}, "events": {"tap": "showTaskSelector"}}]}, {"id": "sound-settings", "type": "view", "className": "sound-settings-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "sound-title", "type": "text", "content": "🔊 声音设置", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "background-sound", "type": "view", "style": {"marginBottom": "20rpx"}, "children": [{"id": "bg-sound-label", "type": "text", "content": "背景音：", "style": {"fontSize": "24rpx", "color": "#666666", "marginBottom": "8rpx"}}, {"id": "bg-sound-options", "type": "view", "style": {"display": "flex", "gap": "8rpx", "flexWrap": "wrap"}, "children": [{"id": "bg-sound-option", "type": "button", "forEach": "{{backgroundSounds}}", "forItem": "sound", "content": "{{sound.icon}} {{sound.name}}", "style": {"backgroundColor": "{{selectedBgSound === sound.id ? '#E6F7FF' : '#F5F5F5'}}", "color": "{{selectedBgSound === sound.id ? '#1890FF' : '#666666'}}", "border": "none", "borderRadius": "16rpx", "padding": "6rpx 12rpx", "fontSize": "22rpx"}, "events": {"tap": "selectBackgroundSound", "data": "{{sound.id}}"}}]}]}, {"id": "volume-control", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "12rpx"}, "children": [{"id": "volume-label", "type": "text", "content": "音量：", "style": {"fontSize": "24rpx", "color": "#666666"}}, {"id": "volume-slider", "type": "slider", "value": "{{volume}}", "min": "0", "max": "100", "style": {"flex": "1"}, "events": {"change": "adjustVolume"}}, {"id": "volume-value", "type": "text", "content": "{{volume}}%", "style": {"fontSize": "22rpx", "color": "#666666", "minWidth": "60rpx"}}]}]}, {"id": "timer-container", "type": "view", "className": "timer-main-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "24rpx", "padding": "60rpx 40rpx", "marginBottom": "24rpx", "boxShadow": "0 8rpx 24rpx rgba(0,0,0,0.1)", "textAlign": "center", "minHeight": "600rpx", "display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center"}, "children": [{"id": "current-session-info", "type": "view", "condition": "{{currentSession}}", "style": {"marginBottom": "40rpx"}, "children": [{"id": "session-title", "type": "text", "content": "{{currentSession.title}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "session-subtitle", "type": "text", "content": "{{currentSession.subtitle}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "timer-display", "type": "view", "style": {"position": "relative", "marginBottom": "60rpx"}, "children": [{"id": "timer-circle", "type": "view", "style": {"width": "320rpx", "height": "320rpx", "borderRadius": "50%", "background": "conic-gradient({{timerColor}} 0deg {{progressDegree}}deg, #F0F0F0 {{progressDegree}}deg 360deg)", "position": "relative", "display": "flex", "alignItems": "center", "justifyContent": "center", "margin": "0 auto", "animation": "{{isRunning ? 'breathe 2s ease-in-out infinite' : 'none'}}"}, "children": [{"id": "timer-inner", "type": "view", "style": {"width": "280rpx", "height": "280rpx", "backgroundColor": "#FFFFFF", "borderRadius": "50%", "display": "flex", "flexDirection": "column", "alignItems": "center", "justifyContent": "center", "boxShadow": "inset 0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "time-display", "type": "text", "content": "{{displayTime}}", "style": {"fontSize": "64rpx", "fontWeight": "700", "color": "#333333", "fontFamily": "monospace", "marginBottom": "8rpx"}}, {"id": "session-type", "type": "text", "content": "{{sessionTypeText}}", "style": {"fontSize": "24rpx", "color": "#666666", "fontWeight": "500"}}]}]}]}, {"id": "timer-controls", "type": "view", "style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "gap": "32rpx"}, "children": [{"id": "start-pause-btn", "type": "button", "content": "{{isRunning ? '⏸️' : '▶️'}}", "style": {"fontSize": "40rpx", "color": "#FFFFFF", "backgroundColor": "{{isRunning ? '#FA8C16' : '#52C41A'}}", "borderRadius": "50%", "width": "120rpx", "height": "120rpx", "border": "none", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.2)", "transition": "all 0.3s ease"}, "events": {"tap": "toggleTimer"}}, {"id": "stop-btn", "type": "button", "content": "⏹️", "condition": "{{isRunning || isPaused}}", "style": {"fontSize": "32rpx", "color": "#666666", "backgroundColor": "#F5F5F5", "borderRadius": "50%", "width": "80rpx", "height": "80rpx", "border": "none"}, "events": {"tap": "stopTimer"}}]}]}, {"id": "quick-actions", "type": "view", "className": "quick-actions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "24rpx 32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "actions-grid", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr", "gap": "16rpx"}, "children": [{"id": "focus-mode-btn", "type": "button", "content": "🖥️ 专注模式", "style": {"backgroundColor": "#F6FFED", "color": "#52C41A", "border": "1rpx solid #B7EB8F", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "26rpx", "fontWeight": "500"}, "events": {"tap": "enterFocusMode"}}, {"id": "sound-settings-btn", "type": "button", "content": "🔊 声音设置", "style": {"backgroundColor": "#FFF7E6", "color": "#FA8C16", "border": "1rpx solid #FFD591", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "26rpx", "fontWeight": "500"}, "events": {"tap": "showSoundSettings"}}, {"id": "statistics-btn", "type": "button", "content": "📊 学习统计", "style": {"backgroundColor": "#E6F7FF", "color": "#1890FF", "border": "1rpx solid #91D5FF", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "26rpx", "fontWeight": "500"}, "events": {"tap": "viewStatistics"}}, {"id": "task-management-btn", "type": "button", "content": "📋 任务管理", "style": {"backgroundColor": "#F9F0FF", "color": "#722ED1", "border": "1rpx solid #D3ADF7", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "26rpx", "fontWeight": "500"}, "events": {"tap": "manageTask"}}]}]}]}, "focusModeLayout": {"type": "view", "direction": "vertical", "backgroundColor": "#1A1A1A", "color": "#FFFFFF", "fullscreen": true, "components": [{"id": "focus-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "32rpx", "position": "fixed", "top": "0", "left": "0", "right": "0", "zIndex": "100", "backgroundColor": "rgba(26,26,26,0.9)"}, "children": [{"id": "exit-focus-btn", "type": "button", "content": "×", "style": {"fontSize": "36rpx", "color": "#FFFFFF", "backgroundColor": "transparent", "border": "none", "padding": "8rpx"}, "events": {"tap": "exitFocusMode"}}, {"id": "focus-title", "type": "text", "content": "🌙 深度专注", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "fontWeight": "500"}}, {"id": "focus-placeholder", "type": "view", "style": {"width": "52rpx"}}]}, {"id": "focus-main", "type": "view", "style": {"flex": "1", "display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center", "padding": "120rpx 40rpx 40rpx", "textAlign": "center"}, "children": [{"id": "focus-timer-display", "type": "view", "style": {"marginBottom": "80rpx"}, "children": [{"id": "focus-timer-circle", "type": "view", "style": {"width": "400rpx", "height": "400rpx", "borderRadius": "50%", "background": "conic-gradient({{timerColor}} 0deg {{progressDegree}}deg, rgba(255,255,255,0.1) {{progressDegree}}deg 360deg)", "display": "flex", "alignItems": "center", "justifyContent": "center", "margin": "0 auto", "animation": "{{isRunning ? 'breathe 3s ease-in-out infinite' : 'none'}}"}, "children": [{"id": "focus-timer-inner", "type": "view", "style": {"width": "340rpx", "height": "340rpx", "backgroundColor": "#1A1A1A", "borderRadius": "50%", "display": "flex", "flexDirection": "column", "alignItems": "center", "justifyContent": "center"}, "children": [{"id": "focus-time-display", "type": "text", "content": "{{displayTime}}", "style": {"fontSize": "80rpx", "fontWeight": "300", "color": "#FFFFFF", "fontFamily": "monospace", "marginBottom": "16rpx", "letterSpacing": "4rpx"}}, {"id": "focus-session-indicator", "type": "text", "content": "{{sessionIndicator}}", "style": {"fontSize": "24rpx", "color": "rgba(255,255,255,0.6)", "marginBottom": "8rpx"}}]}]}]}, {"id": "focus-task-info", "type": "view", "condition": "{{currentSession}}", "style": {"marginBottom": "80rpx", "textAlign": "center"}, "children": [{"id": "focus-task-title", "type": "text", "content": "{{currentSession.title}}", "style": {"fontSize": "36rpx", "fontWeight": "400", "color": "#FFFFFF", "marginBottom": "12rpx"}}, {"id": "focus-task-subtitle", "type": "text", "content": "{{currentSession.subtitle}}", "style": {"fontSize": "26rpx", "color": "rgba(255,255,255,0.7)"}}]}, {"id": "focus-controls", "type": "view", "style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "gap": "60rpx"}, "children": [{"id": "focus-pause-btn", "type": "button", "content": "⏸️", "condition": "{{isRunning}}", "style": {"fontSize": "48rpx", "color": "rgba(255,255,255,0.8)", "backgroundColor": "rgba(255,255,255,0.1)", "borderRadius": "50%", "width": "100rpx", "height": "100rpx", "border": "2rpx solid rgba(255,255,255,0.2)"}, "events": {"tap": "pauseTimer"}}, {"id": "focus-stop-btn", "type": "button", "content": "🛑", "style": {"fontSize": "40rpx", "color": "rgba(255,255,255,0.6)", "backgroundColor": "rgba(255,255,255,0.05)", "borderRadius": "50%", "width": "80rpx", "height": "80rpx", "border": "2rpx solid rgba(255,255,255,0.1)"}, "events": {"tap": "stopTimer"}}]}]}]}, "animations": {"breathe": {"keyframes": [{"transform": "scale(1)", "opacity": "1"}, {"transform": "scale(1.02)", "opacity": "0.9"}, {"transform": "scale(1)", "opacity": "1"}], "duration": "2s", "timingFunction": "ease-in-out", "iterationCount": "infinite"}}, "soundSystem": {"backgroundSounds": [{"id": "silent", "name": "静音", "icon": "🔇", "file": null}, {"id": "rain", "name": "雨声", "icon": "🌧️", "file": "/sounds/rain.mp3", "description": "适合深度思考"}, {"id": "ocean", "name": "海浪", "icon": "🌊", "file": "/sounds/ocean.mp3", "description": "适合放松学习"}, {"id": "cafe", "name": "咖啡厅", "icon": "☕", "file": "/sounds/cafe.mp3", "description": "适合轻松学习"}, {"id": "forest", "name": "森林", "icon": "🌲", "file": "/sounds/forest.mp3", "description": "适合长时间专注"}, {"id": "whitenoise", "name": "白噪音", "icon": "🎵", "file": "/sounds/whitenoise.mp3", "description": "屏蔽外界干扰"}], "notificationSounds": [{"id": "start", "name": "开始提示", "file": "/sounds/start.mp3", "description": "温和的钟声"}, {"id": "pause", "name": "暂停提示", "file": "/sounds/pause.mp3", "description": "轻柔的提示"}, {"id": "complete", "name": "完成提示", "file": "/sounds/complete.mp3", "description": "成就感的音效"}, {"id": "warning", "name": "时间警告", "file": "/sounds/warning.mp3", "description": "时间不足提醒"}]}}