<!--pages/cloud-test/index.wxml-->
<view class="container">
  <view class="header">
    <text class="title">云开发测试</text>
    <view class="status">
      <text>状态: </text>
      <text class="status-text {{cloudStatus === '已初始化' ? 'success' : 'error'}}">{{cloudStatus}}</text>
    </view>
  </view>

  <view class="test-buttons">
    <button class="test-btn" bindtap="testConnection" disabled="{{loading}}">测试连接</button>
    <button class="test-btn" bindtap="testDatabase" disabled="{{loading}}">测试数据库</button>
    <button class="test-btn" bindtap="testCreateTask" disabled="{{loading}}">测试创建任务</button>
    <button class="test-btn" bindtap="testApiCall" disabled="{{loading}}">测试API调用</button>
    <button class="test-btn primary" bindtap="runFullTest" disabled="{{loading}}">运行完整测试</button>
  </view>

  <view class="actions">
    <button class="action-btn" bindtap="clearResults" disabled="{{loading}}">清除结果</button>
    <button class="action-btn" bindtap="copyResults" disabled="{{testResults.length === 0}}">复制结果</button>
  </view>

  <view class="loading" wx:if="{{loading}}">
    <text>测试中...</text>
  </view>

  <view class="results" wx:if="{{testResults.length > 0}}">
    <view class="results-header">
      <text>测试结果 ({{testResults.length}})</text>
    </view>
    
    <view class="result-item" wx:for="{{testResults}}" wx:key="index">
      <view class="result-header">
        <text class="result-name">{{item.name}}</text>
        <text class="result-status {{item.success ? 'success' : 'error'}}">
          {{item.success ? '✓' : '✗'}}
        </text>
        <text class="result-time">{{item.time}}</text>
      </view>
      
      <view class="result-message">{{item.message}}</view>
      
      <view class="result-data" wx:if="{{item.data}}">
        <text class="data-label">数据:</text>
        <text class="data-content">{{item.data}}</text>
      </view>
    </view>
  </view>

  <view class="empty" wx:if="{{testResults.length === 0 && !loading}}">
    <text>点击上方按钮开始测试</text>
  </view>
</view>
