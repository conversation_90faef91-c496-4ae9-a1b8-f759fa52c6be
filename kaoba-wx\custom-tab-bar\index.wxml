<!--custom-tab-bar/index.wxml-->
<view class="tab-bar">
  <view class="tab-bar-item" 
        wx:for="{{list}}" 
        wx:key="index" 
        bindtap="switchTab" 
        data-path="{{item.pagePath}}" 
        data-index="{{index}}">
    <view class="tab-icon">
      <text class="icon-text {{selected === index ? 'active' : ''}}">{{item.iconText}}</text>
    </view>
    <text class="tab-text {{selected === index ? 'active' : ''}}">{{item.text}}</text>
  </view>
</view>
