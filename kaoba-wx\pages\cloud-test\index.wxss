/* pages/cloud-test/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.status {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.status-text {
  font-weight: bold;
}

.status-text.success {
  color: #52c41a;
}

.status-text.error {
  color: #ff4d4f;
}

.test-buttons {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.test-btn {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #f0f0f0;
  color: #333;
}

.test-btn:last-child {
  margin-bottom: 0;
}

.test-btn.primary {
  background-color: #1890ff;
  color: white;
}

.test-btn:disabled {
  opacity: 0.6;
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #f0f0f0;
  color: #666;
}

.loading {
  background: white;
  padding: 40rpx;
  border-radius: 10rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.loading text {
  font-size: 28rpx;
  color: #666;
}

.results {
  background: white;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.results-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.result-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-name {
  flex: 1;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.result-status {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.result-status.success {
  color: #52c41a;
}

.result-status.error {
  color: #ff4d4f;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.result-message {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.result-data {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 6rpx;
  margin-top: 10rpx;
}

.data-label {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.data-content {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
}

.empty {
  background: white;
  padding: 60rpx;
  border-radius: 10rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.empty text {
  font-size: 28rpx;
  color: #999;
}
