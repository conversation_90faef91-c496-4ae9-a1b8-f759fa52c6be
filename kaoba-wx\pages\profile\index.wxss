/* pages/profile/index.wxss */

/* 用户信息容器 */
.user-info-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.user-profile {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background-color: #F5F5F5;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.user-exam {
  font-size: 26rpx;
  color: #666666;
}

.login-status {
  font-size: 22rpx;
  color: #52c41a;
  margin-top: 8rpx;
}

.login-prompt {
  opacity: 0.7;
}

.login-prompt .user-details {
  color: #999;
}

.edit-profile-btn, .login-btn {
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.edit-profile-btn:active {
  background-color: #096DD9;
}

.login-btn {
  background-color: #52c41a;
}

.login-btn:active {
  background-color: #389e0d;
}

.user-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16rpx;
}

.stat-item {
  text-align: center;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 700;
  color: #1890FF;
  display: block;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

/* 成就容器 */
.achievements-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.view-all {
  font-size: 24rpx;
  color: #1890FF;
}

.achievements-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16rpx;
}

.achievement-item {
  text-align: center;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
}

.achievement-icon {
  font-size: 32rpx;
  display: block;
  margin-bottom: 8rpx;
}

.achievement-name {
  font-size: 20rpx;
  color: #333333;
}

/* 菜单容器 */
.menu-container {
  margin-bottom: 24rpx;
}

.menu-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.menu-section-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #666666;
  padding: 24rpx 32rpx 16rpx;
}

.menu-items {
  padding-bottom: 8rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;
}

.menu-item:active {
  background-color: #F8F9FA;
}

.menu-item-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.menu-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
}

.menu-text {
  font-size: 28rpx;
  color: #333333;
}

.menu-item-right {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.menu-badge {
  background-color: #FF4D4F;
  color: #FFFFFF;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
  min-width: 16rpx;
  height: 16rpx;
  line-height: 16rpx;
  text-align: center;
}

.menu-arrow {
  font-size: 24rpx;
  color: #999999;
}

/* 数据卡片 */
.data-cards-container {
  margin-bottom: 24rpx;
}

.data-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 26rpx;
  color: #666666;
}

.card-icon {
  font-size: 32rpx;
}

.card-content {
  margin-bottom: 12rpx;
}

.card-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 8rpx;
}

.card-description {
  font-size: 24rpx;
  color: #666666;
}

.card-trend {
  text-align: right;
}

.trend-text {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.trend-up {
  background-color: #F6FFED;
  color: #52C41A;
}

.trend-down {
  background-color: #FFF1F0;
  color: #FF4D4F;
}

/* 快捷操作 */
.quick-actions-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16rpx;
  margin-top: 20rpx;
}

.quick-action-item {
  text-align: center;
  padding: 20rpx 16rpx;
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12rpx;
  font-size: 32rpx;
}

.action-text {
  font-size: 22rpx;
  color: #333333;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx 0;
}

.version-text {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.copyright {
  font-size: 22rpx;
  color: #CCCCCC;
}

/* 编辑弹窗 */
.edit-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.edit-modal {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.form-input {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.form-textarea {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
  min-height: 120rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
}

.modal-btn {
  flex: 1;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #F5F5F5;
  color: #666666;
}

.cancel-btn:active {
  background-color: #E6E6E6;
}

.confirm-btn {
  background-color: #1890FF;
  color: #FFFFFF;
}

.confirm-btn:active {
  background-color: #096DD9;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
