/* pages/task-detail/index.wxss */

/* 任务头部 */
.task-header-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.task-status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-pending {
  background-color: #FA8C16;
}

.status-completed {
  background-color: #52C41A;
}

.status-overdue {
  background-color: #FF4D4F;
}

.status-text {
  font-size: 24rpx;
  color: #666666;
  flex: 1;
}

.task-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.edit-btn {
  background-color: #1890FF;
  color: #FFFFFF;
}

.task-title-section {
  margin-bottom: 16rpx;
}

.task-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.task-subject {
  font-size: 20rpx;
  color: #1890FF;
  background-color: #E6F7FF;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.task-exam {
  font-size: 20rpx;
  color: #52C41A;
  background-color: #F6FFED;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.task-priority {
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.priority-high {
  background-color: #FFF1F0;
  color: #FF4D4F;
}

.priority-medium {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.priority-low {
  background-color: #F6FFED;
  color: #52C41A;
}

.task-description {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.description-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 进度容器 */
.progress-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.progress-percentage {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
}

.progress-bar {
  background-color: #F0F0F0;
  border-radius: 8rpx;
  height: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.progress-fill {
  background-color: #52C41A;
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 22rpx;
  color: #999999;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

/* 时间信息 */
.time-info-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.time-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
}

.time-content {
  flex: 1;
}

.time-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.time-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

/* 快捷操作 */
.quick-actions-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  transition: all 0.3s ease;
}

.quick-action-btn:active {
  background-color: #E9ECEF;
  transform: scale(0.98);
}

.start-btn {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.complete-btn {
  background-color: #F6FFED;
  color: #52C41A;
}

.edit-btn {
  background-color: #E6F7FF;
  color: #1890FF;
}

.share-btn {
  background-color: #F9F0FF;
  color: #722ED1;
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 操作菜单 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.action-sheet {
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}

.action-sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.action-sheet-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.action-sheet-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.action-sheet-body {
  padding: 16rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  gap: 16rpx;
}

.action-item:active {
  background-color: #F8F9FA;
}

.action-item.danger {
  color: #FF4D4F;
}

.action-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
}

.action-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.action-item.danger .action-text {
  color: #FF4D4F;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
