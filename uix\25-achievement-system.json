{"pageInfo": {"pageName": "成就系统", "pageId": "achievementSystem", "pageType": "page", "description": "学习成就和徽章系统页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "borderRadius": "16rpx", "padding": "40rpx 32rpx", "marginBottom": "24rpx", "color": "#FFFFFF", "textAlign": "center"}, "children": [{"id": "page-title", "type": "text", "content": "学习成就", "style": {"fontSize": "36rpx", "fontWeight": "700", "marginBottom": "12rpx", "textShadow": "0 2rpx 8rpx rgba(0,0,0,0.2)"}}, {"id": "achievement-summary", "type": "text", "content": "已获得 {{unlockedCount}} 个成就，还有 {{lockedCount}} 个待解锁", "style": {"fontSize": "26rpx", "opacity": "0.9", "marginBottom": "20rpx"}}, {"id": "progress-ring", "type": "view", "style": {"width": "120rpx", "height": "120rpx", "borderRadius": "50%", "border": "6rpx solid rgba(255,255,255,0.3)", "borderTop": "6rpx solid #FFFFFF", "margin": "0 auto", "position": "relative", "transform": "rotate({{achievementProgress * 3.6}}deg)"}, "children": [{"id": "progress-text", "type": "text", "content": "{{achievementProgress}}%", "style": {"position": "absolute", "top": "50%", "left": "50%", "transform": "translate(-50%, -50%) rotate(-{{achievementProgress * 3.6}}deg)", "fontSize": "24rpx", "fontWeight": "600"}}]}]}, {"id": "recent-achievements", "type": "view", "condition": "{{recentAchievements.length > 0}}", "className": "recent-achievements-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "recent-title", "type": "text", "content": "🎉 最新获得", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "20rpx"}}, {"id": "recent-list", "type": "view", "children": [{"id": "recent-achievement", "type": "view", "forEach": "{{recentAchievements}}", "forItem": "achievement", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "gap": "16rpx", "padding": "16rpx 0", "borderBottom": "{{index < recentAchievements.length - 1 ? '1rpx solid #F0F0F0' : 'none'}}", "animation": "slideIn 0.5s ease-out {{index * 0.1}}s both"}, "children": [{"id": "achievement-badge", "type": "view", "style": {"width": "64rpx", "height": "64rpx", "borderRadius": "50%", "backgroundColor": "{{achievement.color}}", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "32rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.2)"}, "children": [{"type": "text", "content": "{{achievement.icon}}"}]}, {"id": "achievement-info", "type": "view", "style": {"flex": "1"}, "children": [{"id": "achievement-name", "type": "text", "content": "{{achievement.name}}", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "achievement-time", "type": "text", "content": "{{achievement.unlockedTime}}", "style": {"fontSize": "22rpx", "color": "#999999"}}]}, {"id": "new-badge", "type": "view", "condition": "{{achievement.isNew}}", "style": {"backgroundColor": "#FF4D4F", "color": "#FFFFFF", "fontSize": "20rpx", "padding": "2rpx 6rpx", "borderRadius": "4rpx"}, "children": [{"type": "text", "content": "NEW"}]}]}]}]}, {"id": "achievement-categories", "type": "view", "className": "categories-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "categories-title", "type": "text", "content": "成就分类", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "category-tabs", "type": "view", "style": {"display": "flex", "gap": "8rpx", "marginBottom": "24rpx", "overflowX": "auto"}, "children": [{"id": "category-tab", "type": "button", "forEach": "{{achievementCategories}}", "forItem": "category", "forIndex": "index", "content": "{{category.name}}", "style": {"fontSize": "24rpx", "padding": "8rpx 16rpx", "borderRadius": "8rpx", "border": "none", "backgroundColor": "{{selectedCategory === category.id ? '#1890FF' : '#F8F9FA'}}", "color": "{{selectedCategory === category.id ? '#FFFFFF' : '#666666'}}", "whiteSpace": "nowrap"}, "events": {"tap": "switchCategory"}}]}, {"id": "category-progress", "type": "view", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "marginBottom": "24rpx"}, "children": [{"id": "category-stats", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "12rpx"}, "children": [{"id": "category-name", "type": "text", "content": "{{currentCategoryInfo.name}}", "style": {"fontSize": "26rpx", "fontWeight": "500", "color": "#333333"}}, {"id": "category-count", "type": "text", "content": "{{currentCategoryInfo.unlockedCount}}/{{currentCategoryInfo.totalCount}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "category-progress-bar", "type": "view", "style": {"backgroundColor": "#E9ECEF", "borderRadius": "4rpx", "height": "8rpx", "position": "relative"}, "children": [{"id": "category-progress-fill", "type": "view", "style": {"backgroundColor": "#1890FF", "borderRadius": "4rpx", "height": "100%", "width": "{{currentCategoryInfo.progress}}%", "transition": "width 0.3s ease"}}]}]}]}, {"id": "achievements-grid", "type": "view", "className": "achievements-grid-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "achievements-list", "type": "view", "children": [{"id": "achievement-item", "type": "view", "forEach": "{{filteredAchievements}}", "forItem": "achievement", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "gap": "20rpx", "padding": "24rpx 0", "borderBottom": "1rpx solid #F0F0F0", "opacity": "{{achievement.unlocked ? '1' : '0.6'}}"}, "events": {"tap": "viewAchievementDetail"}, "children": [{"id": "achievement-icon-container", "type": "view", "style": {"position": "relative"}, "children": [{"id": "achievement-icon", "type": "view", "style": {"width": "80rpx", "height": "80rpx", "borderRadius": "50%", "backgroundColor": "{{achievement.unlocked ? achievement.color : '#E9ECEF'}}", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "40rpx", "boxShadow": "{{achievement.unlocked ? '0 4rpx 12rpx rgba(0,0,0,0.2)' : 'none'}}"}, "children": [{"type": "text", "content": "{{achievement.unlocked ? achievement.icon : '🔒'}}"}]}, {"id": "achievement-level", "type": "view", "condition": "{{achievement.level > 1}}", "style": {"position": "absolute", "top": "-8rpx", "right": "-8rpx", "width": "32rpx", "height": "32rpx", "borderRadius": "50%", "backgroundColor": "#FA8C16", "color": "#FFFFFF", "fontSize": "18rpx", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontWeight": "600"}, "children": [{"type": "text", "content": "{{achievement.level}}"}]}]}, {"id": "achievement-content", "type": "view", "style": {"flex": "1"}, "children": [{"id": "achievement-header", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "8rpx", "marginBottom": "8rpx"}, "children": [{"id": "achievement-title", "type": "text", "content": "{{achievement.name}}", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "{{achievement.unlocked ? '#333333' : '#999999'}}"}}, {"id": "achievement-rarity", "type": "view", "condition": "{{achievement.rarity}}", "style": {"backgroundColor": "{{achievement.rarityColor}}", "color": "#FFFFFF", "fontSize": "18rpx", "padding": "2rpx 6rpx", "borderRadius": "4rpx"}, "children": [{"type": "text", "content": "{{achievement.rarity}}"}]}]}, {"id": "achievement-description", "type": "text", "content": "{{achievement.description}}", "style": {"fontSize": "24rpx", "color": "#666666", "lineHeight": "1.4", "marginBottom": "8rpx"}}, {"id": "achievement-progress", "type": "view", "condition": "{{!achievement.unlocked && achievement.progress !== undefined}}", "children": [{"id": "progress-bar", "type": "view", "style": {"backgroundColor": "#F0F0F0", "borderRadius": "4rpx", "height": "6rpx", "position": "relative", "marginBottom": "4rpx"}, "children": [{"id": "progress-fill", "type": "view", "style": {"backgroundColor": "#1890FF", "borderRadius": "4rpx", "height": "100%", "width": "{{achievement.progress}}%"}}]}, {"id": "progress-text", "type": "text", "content": "{{achievement.currentValue}}/{{achievement.targetValue}}", "style": {"fontSize": "20rpx", "color": "#999999"}}]}, {"id": "achievement-reward", "type": "view", "condition": "{{achievement.reward}}", "style": {"marginTop": "8rpx"}, "children": [{"id": "reward-text", "type": "text", "content": "奖励：{{achievement.reward}}", "style": {"fontSize": "22rpx", "color": "#FA8C16"}}]}]}, {"id": "achievement-arrow", "type": "text", "content": "›", "style": {"fontSize": "24rpx", "color": "#999999"}}]}]}]}, {"id": "achievement-stats", "type": "view", "className": "stats-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "stats-title", "type": "text", "content": "成就统计", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "stats-grid", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr", "gap": "16rpx"}, "children": [{"id": "stat-item", "type": "view", "forEach": "{{achievementStats}}", "forItem": "stat", "forIndex": "index", "style": {"textAlign": "center", "backgroundColor": "{{stat.bgColor}}", "borderRadius": "8rpx", "padding": "20rpx"}, "children": [{"type": "text", "content": "{{stat.value}}", "style": {"fontSize": "32rpx", "fontWeight": "700", "color": "{{stat.color}}", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "{{stat.label}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}]}]}], "data": {"unlockedCount": 12, "lockedCount": 18, "achievementProgress": 40, "selectedCategory": "study", "recentAchievements": [{"id": "persistent_7days", "name": "坚持不懈", "icon": "🏆", "color": "#FA8C16", "unlockedTime": "2小时前", "isNew": true}, {"id": "pomodoro_master", "name": "番茄大师", "icon": "🍅", "color": "#52C41A", "unlockedTime": "昨天", "isNew": false}], "achievementCategories": [{"id": "study", "name": "学习"}, {"id": "time", "name": "时间"}, {"id": "task", "name": "任务"}, {"id": "exam", "name": "考试"}, {"id": "special", "name": "特殊"}], "currentCategoryInfo": {"name": "学习成就", "unlockedCount": 8, "totalCount": 15, "progress": 53}, "filteredAchievements": [{"id": "first_task", "name": "初来乍到", "description": "完成第一个学习任务", "icon": "🎯", "color": "#1890FF", "unlocked": true, "rarity": "普通", "rarityColor": "#52C41A", "reward": "经验值 +10"}, {"id": "study_10hours", "name": "勤奋学者", "description": "累计学习时长达到10小时", "icon": "📚", "color": "#722ED1", "unlocked": true, "rarity": "稀有", "rarityColor": "#1890FF", "reward": "专注时长 +5分钟"}, {"id": "perfect_week", "name": "完美一周", "description": "连续7天完成学习目标", "icon": "⭐", "color": "#FA8C16", "unlocked": false, "progress": 71, "currentValue": 5, "targetValue": 7, "rarity": "史诗", "rarityColor": "#FA8C16", "reward": "成就徽章"}], "achievementStats": [{"label": "总成就数", "value": "30", "color": "#1890FF", "bgColor": "#E6F7FF"}, {"label": "已解锁", "value": "12", "color": "#52C41A", "bgColor": "#F6FFED"}, {"label": "稀有成就", "value": "3", "color": "#FA8C16", "bgColor": "#FFF2E8"}, {"label": "完成度", "value": "40%", "color": "#722ED1", "bgColor": "#F9F0FF"}]}, "methods": {"switchCategory": {"type": "function", "description": "切换成就分类", "params": ["categoryId"], "implementation": "changeAchievementCategory"}, "viewAchievementDetail": {"type": "function", "description": "查看成就详情", "params": ["achievementId"], "implementation": "showAchievementDetail"}}, "lifecycle": {"onLoad": ["loadAchievements"], "onShow": ["refreshAchievementData", "checkNewAchievements"]}, "animations": {"slideIn": {"keyframes": {"0%": {"opacity": "0", "transform": "translateX(-30rpx)"}, "100%": {"opacity": "1", "transform": "translateX(0)"}}, "duration": "0.5s", "timingFunction": "ease-out"}}}