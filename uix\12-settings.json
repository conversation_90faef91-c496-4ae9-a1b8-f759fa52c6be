{"pageInfo": {"pageName": "设置", "pageId": "settings", "pageType": "page", "description": "应用设置页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "page-title", "type": "text", "content": "设置", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "page-subtitle", "type": "text", "content": "个性化你的学习体验", "style": {"fontSize": "28rpx", "color": "#666666"}}]}, {"id": "notification-settings", "type": "view", "className": "settings-section", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "notification-title", "type": "text", "content": "通知设置", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "notification-items", "type": "view", "children": [{"id": "notification-item", "type": "view", "forEach": "{{notificationSettings}}", "forItem": "item", "forIndex": "index", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "20rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "notification-info", "type": "view", "children": [{"id": "notification-name", "type": "text", "content": "{{item.name}}", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "notification-desc", "type": "text", "content": "{{item.description}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "notification-switch", "type": "switch", "checked": "{{item.enabled}}", "color": "#1890FF", "events": {"change": "toggleNotification"}}]}]}]}, {"id": "study-settings", "type": "view", "className": "settings-section", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "study-title", "type": "text", "content": "学习设置", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "pomodoro-duration", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "pomodoro-label", "type": "text", "content": "默认番茄钟时长", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "pomodoro-options", "type": "view", "style": {"display": "flex", "gap": "12rpx", "flexWrap": "wrap"}, "children": [{"id": "pomodoro-option", "type": "button", "forEach": "{{pomodoroDurations}}", "forItem": "option", "forIndex": "index", "content": "{{option.label}}", "style": {"fontSize": "26rpx", "padding": "12rpx 20rpx", "borderRadius": "8rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{settings.defaultPomodoroDuration === option.value ? '#1890FF' : '#FFFFFF'}}", "color": "{{settings.defaultPomodoroDuration === option.value ? '#FFFFFF' : '#333333'}}"}, "events": {"tap": "selectPomodoroDuration"}}]}]}, {"id": "auto-break", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "20rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "auto-break-info", "type": "view", "children": [{"id": "auto-break-name", "type": "text", "content": "自动开始休息", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "auto-break-desc", "type": "text", "content": "番茄钟结束后自动开始休息计时", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "auto-break-switch", "type": "switch", "checked": "{{settings.autoStartBreak}}", "color": "#1890FF", "events": {"change": "toggleAutoBreak"}}]}, {"id": "keep-screen-on", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "20rpx 0"}, "children": [{"id": "keep-screen-info", "type": "view", "children": [{"id": "keep-screen-name", "type": "text", "content": "保持屏幕常亮", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "keep-screen-desc", "type": "text", "content": "专注时保持屏幕不熄灭", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "keep-screen-switch", "type": "switch", "checked": "{{settings.keepScreenOn}}", "color": "#1890FF", "events": {"change": "toggleKeepScreen"}}]}]}, {"id": "appearance-settings", "type": "view", "className": "settings-section", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "appearance-title", "type": "text", "content": "外观设置", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "theme-setting", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "theme-label", "type": "text", "content": "主题模式", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "theme-options", "type": "view", "style": {"display": "flex", "gap": "12rpx"}, "children": [{"id": "theme-option", "type": "button", "forEach": "{{themeOptions}}", "forItem": "option", "forIndex": "index", "content": "{{option.label}}", "style": {"fontSize": "26rpx", "padding": "12rpx 20rpx", "borderRadius": "8rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{settings.theme === option.value ? '#1890FF' : '#FFFFFF'}}", "color": "{{settings.theme === option.value ? '#FFFFFF' : '#333333'}}"}, "events": {"tap": "selectTheme"}}]}]}, {"id": "font-size-setting", "type": "view", "children": [{"id": "font-size-label", "type": "text", "content": "字体大小", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "font-size-slider", "type": "slider", "min": "12", "max": "20", "value": "{{settings.fontSize}}", "step": "1", "showValue": true, "style": {"width": "100%"}, "events": {"change": "changeFontSize"}}]}]}, {"id": "data-settings", "type": "view", "className": "settings-section", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "data-title", "type": "text", "content": "数据管理", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "data-actions", "type": "view", "children": [{"id": "export-data-btn", "type": "button", "content": "导出学习数据", "style": {"fontSize": "28rpx", "color": "#1890FF", "backgroundColor": "#F0F9FF", "border": "1rpx solid #BAE7FF", "borderRadius": "8rpx", "padding": "16rpx", "width": "100%", "marginBottom": "16rpx"}, "events": {"tap": "exportData"}}, {"id": "clear-cache-btn", "type": "button", "content": "清除缓存", "style": {"fontSize": "28rpx", "color": "#666666", "backgroundColor": "#F5F5F5", "border": "1rpx solid #D9D9D9", "borderRadius": "8rpx", "padding": "16rpx", "width": "100%", "marginBottom": "16rpx"}, "events": {"tap": "clearCache"}}, {"id": "reset-data-btn", "type": "button", "content": "重置所有数据", "style": {"fontSize": "28rpx", "color": "#FF4D4F", "backgroundColor": "#FFF2F0", "border": "1rpx solid #FFCCC7", "borderRadius": "8rpx", "padding": "16rpx", "width": "100%"}, "events": {"tap": "confirmResetData"}}]}]}], "data": {"settings": {"defaultPomodoroDuration": 25, "autoStartBreak": true, "keepScreenOn": true, "theme": "light", "fontSize": 16}, "notificationSettings": [{"id": "exam_reminder", "name": "考试提醒", "description": "考试前的重要提醒", "enabled": true}, {"id": "task_reminder", "name": "任务提醒", "description": "任务截止日期提醒", "enabled": true}, {"id": "study_reminder", "name": "学习提醒", "description": "每日学习时间提醒", "enabled": false}, {"id": "break_reminder", "name": "休息提醒", "description": "长时间学习后的休息提醒", "enabled": true}], "pomodoroDurations": [{"value": 15, "label": "15分钟"}, {"value": 25, "label": "25分钟"}, {"value": 45, "label": "45分钟"}, {"value": 60, "label": "60分钟"}], "themeOptions": [{"value": "light", "label": "浅色"}, {"value": "dark", "label": "深色"}, {"value": "auto", "label": "跟随系统"}]}, "methods": {"toggleNotification": {"type": "function", "description": "切换通知设置", "params": ["notificationId", "enabled"], "implementation": "updateNotificationSetting"}, "selectPomodoroDuration": {"type": "function", "description": "选择番茄钟时长", "params": ["duration"], "implementation": "setPomodoroDuration"}, "toggleAutoBreak": {"type": "function", "description": "切换自动休息", "params": ["enabled"], "implementation": "setAutoBreak"}, "toggleKeepScreen": {"type": "function", "description": "切换保持屏幕常亮", "params": ["enabled"], "implementation": "setKeepScreen"}, "selectTheme": {"type": "function", "description": "选择主题", "params": ["theme"], "implementation": "setTheme"}, "changeFontSize": {"type": "function", "description": "改变字体大小", "params": ["size"], "implementation": "setFontSize"}, "exportData": {"type": "function", "description": "导出学习数据", "implementation": "exportStudyData"}, "clearCache": {"type": "function", "description": "清除缓存", "implementation": "clearAppCache"}, "confirmResetData": {"type": "modal", "title": "重置数据", "content": "此操作将删除所有学习数据，无法恢复。确定要继续吗？", "confirmText": "重置", "cancelText": "取消", "confirmAction": "resetAllData"}, "resetAllData": {"type": "function", "description": "重置所有数据", "implementation": "resetUserData"}}, "lifecycle": {"onLoad": ["loadSettings"], "onShow": ["refreshSettings"]}}