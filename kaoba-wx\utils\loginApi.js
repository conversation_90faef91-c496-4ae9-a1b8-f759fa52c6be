// 登录API工具类
class LoginApi {
  // 用户登录
  static async login(userInfo = null) {
    try {
      console.log('开始用户登录...')
      
      // 调用登录云函数
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'login',
          userInfo: userInfo
        }
      })

      if (result.result && result.result.success) {
        const loginData = result.result.data
        
        // 保存用户信息到全局数据和本地存储
        const app = getApp()
        app.globalData.userInfo = loginData.user
        app.globalData.openid = loginData.openid
        
        wx.setStorageSync('kaoba_user_info', loginData.user)
        wx.setStorageSync('kaoba_openid', loginData.openid)
        
        console.log('登录成功:', loginData.user.nickName)
        
        return {
          success: true,
          data: loginData,
          isFirstLogin: loginData.isFirstLogin
        }
      } else {
        console.error('登录失败:', result.result?.error)
        return {
          success: false,
          error: result.result?.error || '登录失败'
        }
      }
    } catch (error) {
      console.error('登录异常:', error)
      
      // 如果云函数调用失败，尝试本地登录
      return await this.localLogin(userInfo)
    }
  }

  // 本地登录（云开发不可用时的后备方案）
  static async localLogin(userInfo = null) {
    try {
      console.log('使用本地登录...')
      
      // 生成本地用户ID
      let localUserId = wx.getStorageSync('kaoba_local_user_id')
      if (!localUserId) {
        localUserId = 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
        wx.setStorageSync('kaoba_local_user_id', localUserId)
      }

      // 获取或创建本地用户信息
      let localUser = wx.getStorageSync('kaoba_local_user_info')
      const now = new Date().toISOString()
      
      if (!localUser) {
        // 创建新的本地用户
        localUser = {
          _id: localUserId,
          type: 'user',
          openid: localUserId,
          nickName: userInfo?.nickName || '本地用户',
          avatarUrl: userInfo?.avatarUrl || '',
          gender: userInfo?.gender || 0,
          country: userInfo?.country || '',
          province: userInfo?.province || '',
          city: userInfo?.city || '',
          language: userInfo?.language || 'zh_CN',
          isFirstLogin: true,
          loginCount: 1,
          lastLoginTime: now,
          createTime: now,
          updateTime: now
        }
        
        wx.setStorageSync('kaoba_local_user_info', localUser)
        console.log('创建本地用户:', localUser.nickName)
      } else {
        // 更新登录信息
        localUser.lastLoginTime = now
        localUser.loginCount = (localUser.loginCount || 0) + 1
        localUser.updateTime = now
        localUser.isFirstLogin = false
        
        // 更新用户信息
        if (userInfo) {
          if (userInfo.nickName) localUser.nickName = userInfo.nickName
          if (userInfo.avatarUrl) localUser.avatarUrl = userInfo.avatarUrl
          if (userInfo.gender !== undefined) localUser.gender = userInfo.gender
          if (userInfo.country) localUser.country = userInfo.country
          if (userInfo.province) localUser.province = userInfo.province
          if (userInfo.city) localUser.city = userInfo.city
          if (userInfo.language) localUser.language = userInfo.language
        }
        
        wx.setStorageSync('kaoba_local_user_info', localUser)
        console.log('本地用户登录:', localUser.nickName)
      }

      // 保存到全局数据
      const app = getApp()
      app.globalData.userInfo = localUser
      app.globalData.openid = localUserId
      
      return {
        success: true,
        data: {
          openid: localUserId,
          user: localUser,
          isFirstLogin: localUser.isFirstLogin
        },
        isLocal: true
      }
    } catch (error) {
      console.error('本地登录失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取用户信息
  static async getUserInfo() {
    try {
      // 先尝试从云端获取
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'getUserInfo'
        }
      })

      if (result.result && result.result.success) {
        return result.result
      } else {
        // 从本地获取
        const localUser = wx.getStorageSync('kaoba_local_user_info')
        if (localUser) {
          return {
            success: true,
            data: localUser,
            isLocal: true
          }
        } else {
          return {
            success: false,
            error: '用户信息不存在'
          }
        }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      
      // 从本地获取
      const localUser = wx.getStorageSync('kaoba_local_user_info')
      if (localUser) {
        return {
          success: true,
          data: localUser,
          isLocal: true
        }
      } else {
        return {
          success: false,
          error: error.message
        }
      }
    }
  }

  // 更新用户资料
  static async updateUserProfile(userInfo) {
    try {
      // 先尝试云端更新
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          action: 'updateProfile',
          userInfo: userInfo
        }
      })

      if (result.result && result.result.success) {
        // 更新本地缓存
        const app = getApp()
        if (app.globalData.userInfo) {
          Object.assign(app.globalData.userInfo, userInfo)
          wx.setStorageSync('kaoba_user_info', app.globalData.userInfo)
        }
        
        return result.result
      } else {
        throw new Error(result.result?.error || '云端更新失败')
      }
    } catch (error) {
      console.error('云端更新用户资料失败，使用本地更新:', error)
      
      // 本地更新
      const localUser = wx.getStorageSync('kaoba_local_user_info')
      if (localUser) {
        Object.assign(localUser, userInfo, {
          updateTime: new Date().toISOString()
        })
        
        wx.setStorageSync('kaoba_local_user_info', localUser)
        
        // 更新全局数据
        const app = getApp()
        app.globalData.userInfo = localUser
        
        return {
          success: true,
          data: localUser,
          isLocal: true
        }
      } else {
        return {
          success: false,
          error: '本地用户信息不存在'
        }
      }
    }
  }

  // 检查登录状态
  static checkLoginStatus() {
    try {
      const app = getApp()

      // 检查全局数据
      if (app && app.globalData && app.globalData.userInfo && app.globalData.openid) {
        return {
          isLoggedIn: true,
          userInfo: app.globalData.userInfo,
          openid: app.globalData.openid
        }
      }

      // 检查本地存储
      const localUser = wx.getStorageSync('kaoba_local_user_info') || wx.getStorageSync('kaoba_user_info')
      const localOpenid = wx.getStorageSync('kaoba_local_user_id') || wx.getStorageSync('kaoba_openid')

      if (localUser && localOpenid) {
        // 恢复到全局数据（如果app可用）
        if (app && app.globalData) {
          app.globalData.userInfo = localUser
          app.globalData.openid = localOpenid
        }

        return {
          isLoggedIn: true,
          userInfo: localUser,
          openid: localOpenid
        }
      }

      return {
        isLoggedIn: false,
        userInfo: null,
        openid: null
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)

      // 如果getApp()失败，只检查本地存储
      const localUser = wx.getStorageSync('kaoba_local_user_info') || wx.getStorageSync('kaoba_user_info')
      const localOpenid = wx.getStorageSync('kaoba_local_user_id') || wx.getStorageSync('kaoba_openid')

      if (localUser && localOpenid) {
        return {
          isLoggedIn: true,
          userInfo: localUser,
          openid: localOpenid
        }
      }

      return {
        isLoggedIn: false,
        userInfo: null,
        openid: null
      }
    }
  }

  // 退出登录
  static logout() {
    const app = getApp()
    
    // 清除全局数据
    app.globalData.userInfo = null
    app.globalData.openid = null
    
    // 清除本地存储
    wx.removeStorageSync('kaoba_user_info')
    wx.removeStorageSync('kaoba_openid')
    wx.removeStorageSync('kaoba_local_user_info')
    wx.removeStorageSync('kaoba_local_user_id')
    
    console.log('用户已退出登录')
    
    return { success: true }
  }
}

module.exports = LoginApi
