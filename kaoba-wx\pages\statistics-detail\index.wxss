/* pages/statistics-detail/index.wxss */

/* 容器 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.header {
  background-color: #FFFFFF;
  padding: 40rpx 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #7F8C8D;
}

/* 时间范围选择器 */
.time-range-selector {
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.range-tabs {
  display: flex;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.range-tab {
  flex: 1;
  padding: 16rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  background-color: #F8F9FA;
  color: #7F8C8D;
  font-size: 24rpx;
  text-align: center;
}

.range-tab.active {
  background-color: #3498DB;
  color: #FFFFFF;
  border-color: #3498DB;
}

.range-desc {
  font-size: 22rpx;
  color: #95A5A6;
  text-align: center;
}

/* 通用区块样式 */
.core-metrics,
.trend-chart,
.subject-analysis,
.time-distribution,
.study-suggestions,
.export-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 0 32rpx 24rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 24rpx;
  display: block;
}

/* 核心指标 */
.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  position: relative;
  overflow: hidden;
}

.metric-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-card.warning {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-card.info {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.metric-icon {
  font-size: 32rpx;
  color: #FFFFFF;
}

.metric-content {
  flex: 1;
  color: #FFFFFF;
}

.metric-value {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 4rpx;
}

.metric-label {
  font-size: 22rpx;
  opacity: 0.9;
  display: block;
  margin-bottom: 8rpx;
}

.metric-change {
  font-size: 20rpx;
  font-weight: 500;
}

.metric-change.positive {
  color: #E8F5E8;
}

.metric-change.negative {
  color: #FFEBEE;
}

/* 趋势图表 */
.chart-container {
  border: 1rpx solid #F0F0F0;
  border-radius: 12rpx;
  padding: 24rpx;
  background-color: #FAFAFA;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.chart-title {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  gap: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-color {
  width: 12rpx;
  height: 12rpx;
  border-radius: 2rpx;
}

.legend-color.study {
  background-color: #3498DB;
}

.legend-color.target {
  background-color: #E74C3C;
}

.legend-text {
  font-size: 20rpx;
  color: #7F8C8D;
}

.chart-area {
  display: flex;
  height: 200rpx;
}

.chart-y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 40rpx;
  padding-right: 8rpx;
}

.y-label {
  font-size: 18rpx;
  color: #95A5A6;
  text-align: right;
}

.chart-content {
  flex: 1;
  position: relative;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 160rpx;
  padding: 0 8rpx;
}

.bar-group {
  display: flex;
  gap: 2rpx;
  align-items: end;
}

.bar {
  width: 8rpx;
  border-radius: 2rpx;
  min-height: 4rpx;
}

.study-bar {
  background-color: #3498DB;
}

.target-bar {
  background-color: #E74C3C;
}

.chart-x-axis {
  display: flex;
  justify-content: space-around;
  margin-top: 8rpx;
  padding: 0 8rpx;
}

.x-label {
  font-size: 18rpx;
  color: #95A5A6;
}

/* 科目分析 */
.subject-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.subject-item {
  border: 1rpx solid #F0F0F0;
  border-radius: 12rpx;
  padding: 20rpx;
  background-color: #FAFAFA;
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.subject-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.subject-icon {
  font-size: 24rpx;
}

.subject-name {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
}

.subject-time {
  font-size: 24rpx;
  color: #3498DB;
  font-weight: 600;
}

.subject-progress {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #E9ECEF;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498DB, #2ECC71);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 20rpx;
  color: #7F8C8D;
  min-width: 40rpx;
}

.subject-details {
  display: flex;
  justify-content: space-around;
}

.detail-item {
  text-align: center;
}

.detail-label {
  font-size: 20rpx;
  color: #95A5A6;
  display: block;
  margin-bottom: 4rpx;
}

.detail-value {
  font-size: 22rpx;
  color: #2C3E50;
  font-weight: 500;
}

/* 时间分布 */
.distribution-chart {
  text-align: center;
}

.time-slots {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 120rpx;
  margin-bottom: 16rpx;
  padding: 0 8rpx;
}

.time-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.slot-bar {
  width: 12rpx;
  background: linear-gradient(to top, #3498DB, #2ECC71);
  border-radius: 2rpx;
  min-height: 4rpx;
}

.slot-label {
  font-size: 18rpx;
  color: #95A5A6;
}

.distribution-desc {
  font-size: 24rpx;
  color: #2C3E50;
  font-weight: 500;
}

/* 学习建议 */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1rpx solid #F0F0F0;
  background-color: #FAFAFA;
}

.suggestion-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #FFFFFF;
}

.suggestion-icon.success {
  background-color: #27AE60;
}

.suggestion-icon.info {
  background-color: #3498DB;
}

.suggestion-icon.warning {
  background-color: #F39C12;
}

.suggestion-icon.tip {
  background-color: #9B59B6;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.suggestion-desc {
  font-size: 22rpx;
  color: #7F8C8D;
  line-height: 1.5;
}

/* 导出选项 */
.export-options {
  display: flex;
  gap: 16rpx;
}

.export-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 24rpx 16rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 12rpx;
  background-color: #F8F9FA;
  color: #2C3E50;
}

.export-btn:active {
  background-color: #E9ECEF;
}

.export-icon {
  font-size: 32rpx;
}

.export-text {
  font-size: 22rpx;
}
