<!--pages/data-center/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header-container">
    <text class="page-title">数据中心</text>
    <text class="page-subtitle">了解你的复习效果</text>
  </view>

  <!-- 考试准备度 -->
  <view class="preparation-score-container">
    <view class="score-header">
      <text class="score-title">考试准备度</text>
      <button class="score-info-btn" bindtap="showScoreInfo">ℹ️</button>
    </view>

    <view class="score-display">
      <view class="score-circle">
        <view class="score-progress" style="background: conic-gradient(#52C41A 0deg {{preparationScore * 3.6}}deg, #F0F0F0 {{preparationScore * 3.6}}deg 360deg)">
          <view class="score-inner">
            <text class="score-value">{{preparationScore}}</text>
            <text class="score-unit">分</text>
          </view>
        </view>
      </view>
      <view class="score-details">
        <text class="score-level">{{preparationLevel}}</text>
        <text class="score-description">{{preparationDescription}}</text>
      </view>
    </view>

    <view class="score-factors">
      <view class="factor-item" wx:for="{{scoreFactors}}" wx:key="name">
        <text class="factor-name">{{item.name}}</text>
        <view class="factor-progress">
          <view class="factor-bar">
            <view class="factor-fill" style="width: {{item.score}}%; background-color: {{item.color}}"></view>
          </view>
          <text class="factor-score">{{item.score}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习统计 -->
  <view class="study-stats-container">
    <view class="stats-header">
      <text class="stats-title">学习统计</text>
      <view class="time-range-selector">
        <text class="time-range-item {{currentTimeRange === item.value ? 'active' : ''}}"
              wx:for="{{timeRangeOptions}}"
              wx:key="value"
              bindtap="switchTimeRange"
              data-range="{{item.value}}">
          {{item.label}}
        </text>
      </view>
    </view>

    <view class="stats-grid">
      <view class="stat-card" wx:for="{{studyStats}}" wx:key="label">
        <view class="stat-icon" style="background-color: {{item.bgColor}}">
          <text>{{item.icon}}</text>
        </view>
        <view class="stat-content">
          <text class="stat-value">{{item.value}}</text>
          <text class="stat-label">{{item.label}}</text>
          <view class="stat-trend" wx:if="{{item.trend}}">
            <text class="trend-icon">{{item.trend.icon}}</text>
            <text class="trend-text">{{item.trend.text}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习效率分析 -->
  <view class="efficiency-container">
    <view class="section-header">
      <text class="section-title">学习效率</text>
      <text class="view-detail" bindtap="viewEfficiencyDetail">详细分析</text>
    </view>

    <view class="efficiency-chart">
      <view class="chart-header">
        <text class="chart-title">每日效率趋势</text>
        <text class="chart-period">最近7天</text>
      </view>
      <view class="chart-content">
        <view class="chart-bars">
          <view class="chart-bar" wx:for="{{efficiencyData}}" wx:key="date">
            <view class="bar-fill" style="height: {{item.efficiency}}%; background-color: {{item.efficiency >= 80 ? '#52C41A' : item.efficiency >= 60 ? '#FA8C16' : '#FF4D4F'}}"></view>
            <text class="bar-label">{{item.day}}</text>
            <text class="bar-value">{{item.efficiency}}%</text>
          </view>
        </view>
      </view>
    </view>

    <view class="efficiency-insights">
      <text class="insights-title">效率洞察</text>
      <view class="insight-item" wx:for="{{efficiencyInsights}}" wx:key="id">
        <text class="insight-icon">{{item.icon}}</text>
        <text class="insight-text">{{item.text}}</text>
      </view>
    </view>
  </view>

  <!-- 科目分析 -->
  <view class="subjects-analysis-container">
    <view class="section-header">
      <text class="section-title">科目分析</text>
      <text class="view-detail" bindtap="viewSubjectsDetail">查看详情</text>
    </view>

    <view class="subjects-list">
      <view class="subject-item" wx:for="{{subjectsData}}" wx:key="name">
        <view class="subject-header">
          <view class="subject-info">
            <text class="subject-name">{{item.name}}</text>
            <text class="subject-exam">{{item.exam}}</text>
          </view>
          <view class="subject-score">
            <text class="score-number">{{item.score}}</text>
            <text class="score-max">/100</text>
          </view>
        </view>

        <view class="subject-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%; background-color: {{item.progressColor}}"></view>
          </view>
          <text class="progress-text">学习进度 {{item.progress}}%</text>
        </view>

        <view class="subject-stats">
          <view class="subject-stat">
            <text class="stat-label">学习时长</text>
            <text class="stat-value">{{item.studyTime}}</text>
          </view>
          <view class="subject-stat">
            <text class="stat-label">完成任务</text>
            <text class="stat-value">{{item.completedTasks}}</text>
          </view>
          <view class="subject-stat">
            <text class="stat-label">平均效率</text>
            <text class="stat-value">{{item.avgEfficiency}}%</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习习惯 -->
  <view class="habits-container">
    <view class="section-header">
      <text class="section-title">学习习惯</text>
    </view>

    <view class="habits-grid">
      <view class="habit-card" wx:for="{{studyHabits}}" wx:key="title">
        <view class="habit-icon">
          <text>{{item.icon}}</text>
        </view>
        <text class="habit-title">{{item.title}}</text>
        <text class="habit-value">{{item.value}}</text>
        <text class="habit-description">{{item.description}}</text>
      </view>
    </view>
  </view>

  <!-- 成就展示 -->
  <view class="achievements-container">
    <view class="section-header">
      <text class="section-title">最近成就</text>
      <text class="view-detail" bindtap="viewAllAchievements">查看全部</text>
    </view>

    <view class="achievements-list">
      <view class="achievement-item" wx:for="{{recentAchievements}}" wx:key="id">
        <view class="achievement-icon">
          <text>{{item.icon}}</text>
        </view>
        <view class="achievement-content">
          <text class="achievement-name">{{item.name}}</text>
          <text class="achievement-description">{{item.description}}</text>
          <text class="achievement-time">{{item.time}}</text>
        </view>
        <view class="achievement-badge" wx:if="{{item.isNew}}">
          <text>NEW</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据导出 -->
  <view class="export-container">
    <view class="export-card">
      <view class="export-info">
        <text class="export-title">数据导出</text>
        <text class="export-description">导出你的学习数据，便于分析和备份</text>
      </view>
      <button class="export-btn" bindtap="exportData">
        <text class="export-icon">📊</text>
        <text class="export-text">导出</text>
      </button>
    </view>
  </view>
</view>

<!-- 准备度说明弹窗 -->
<view class="score-info-modal" wx:if="{{showScoreInfo}}" bindtap="hideScoreInfo">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">考试准备度说明</text>
      <text class="modal-close" bindtap="hideScoreInfo">×</text>
    </view>
    <view class="modal-body">
      <text class="modal-text">考试准备度是基于以下因素综合计算的智能评分：</text>
      <view class="factor-list">
        <view class="factor-explanation" wx:for="{{scoreFactorExplanations}}" wx:key="name">
          <text class="factor-name">{{item.name}}</text>
          <text class="factor-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>
</view>