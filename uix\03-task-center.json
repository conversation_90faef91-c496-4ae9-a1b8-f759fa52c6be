{"pageInfo": {"pageName": "任务中心", "pageId": "taskCenter", "pageType": "tabPage", "description": "任务管理页面，展示和管理所有复习任务", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "header-top", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "16rpx"}, "children": [{"id": "page-title", "type": "text", "content": "任务中心", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333"}}, {"id": "add-task-btn", "type": "button", "content": "+ 添加任务", "style": {"fontSize": "26rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "borderRadius": "8rpx", "padding": "8rpx 16rpx", "border": "none"}, "events": {"tap": "navigateToAddTask"}}]}, {"id": "task-stats", "type": "view", "style": {"display": "flex", "justifyContent": "space-around", "backgroundColor": "#F8F9FA", "borderRadius": "12rpx", "padding": "20rpx"}, "children": [{"id": "total-tasks", "type": "view", "style": {"textAlign": "center"}, "children": [{"type": "text", "content": "{{taskStats.total}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "display": "block"}}, {"type": "text", "content": "总任务", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "completed-tasks", "type": "view", "style": {"textAlign": "center"}, "children": [{"type": "text", "content": "{{taskStats.completed}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#52C41A", "display": "block"}}, {"type": "text", "content": "已完成", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "pending-tasks", "type": "view", "style": {"textAlign": "center"}, "children": [{"type": "text", "content": "{{taskStats.pending}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#FA8C16", "display": "block"}}, {"type": "text", "content": "待完成", "style": {"fontSize": "24rpx", "color": "#666666"}}]}]}]}, {"id": "filter-section", "type": "view", "className": "filter-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "24rpx 32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "filter-tabs", "type": "view", "style": {"display": "flex", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "4rpx"}, "children": [{"id": "filter-tab", "type": "button", "forEach": "{{filterTabs}}", "forItem": "tab", "forIndex": "index", "content": "{{tab.name}}", "style": {"flex": "1", "fontSize": "26rpx", "padding": "12rpx", "borderRadius": "6rpx", "border": "none", "backgroundColor": "{{currentFilter === tab.key ? '#1890FF' : 'transparent'}}", "color": "{{currentFilter === tab.key ? '#FFFFFF' : '#666666'}}"}, "events": {"tap": "switchFilter"}}]}]}, {"id": "task-list-section", "type": "view", "className": "task-list-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "task-list", "type": "view", "condition": "{{filteredTasks.length > 0}}", "children": [{"id": "task-item", "type": "view", "forEach": "{{filteredTasks}}", "forItem": "task", "forIndex": "index", "style": {"padding": "24rpx 0", "borderBottom": "1rpx solid #F0F0F0", "position": "relative"}, "children": [{"id": "task-main", "type": "view", "style": {"display": "flex", "alignItems": "flex-start", "gap": "16rpx"}, "children": [{"id": "task-checkbox", "type": "checkbox", "checked": "{{task.completed}}", "style": {"marginTop": "4rpx"}, "events": {"change": "toggleTaskComplete"}}, {"id": "task-content", "type": "view", "style": {"flex": "1"}, "children": [{"id": "task-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "flex-start", "marginBottom": "8rpx"}, "children": [{"id": "task-title", "type": "text", "content": "{{task.title}}", "style": {"fontSize": "30rpx", "fontWeight": "500", "color": "{{task.completed ? '#999999' : '#333333'}}", "textDecoration": "{{task.completed ? 'line-through' : 'none'}}", "flex": "1"}}, {"id": "task-priority", "type": "view", "condition": "{{task.priority === 'high'}}", "style": {"backgroundColor": "#FF4D4F", "color": "#FFFFFF", "fontSize": "20rpx", "padding": "2rpx 6rpx", "borderRadius": "4rpx", "marginLeft": "12rpx"}, "children": [{"type": "text", "content": "高"}]}]}, {"id": "task-meta", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "16rpx", "marginBottom": "12rpx"}, "children": [{"id": "task-subject", "type": "view", "condition": "{{task.subject}}", "style": {"backgroundColor": "#E6F7FF", "color": "#1890FF", "fontSize": "22rpx", "padding": "4rpx 8rpx", "borderRadius": "4rpx"}, "children": [{"type": "text", "content": "{{task.subject}}"}]}, {"id": "task-due-date", "type": "text", "condition": "{{task.dueDate}}", "content": "{{task.dueDate}}", "style": {"fontSize": "24rpx", "color": "{{task.isOverdue ? '#FF4D4F' : '#666666'}}"}}, {"id": "task-duration", "type": "text", "condition": "{{task.estimatedDuration}}", "content": "预计 {{task.estimatedDuration}} 分钟", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "task-description", "type": "text", "condition": "{{task.description}}", "content": "{{task.description}}", "style": {"fontSize": "26rpx", "color": "#999999", "lineHeight": "1.4", "marginBottom": "12rpx"}}, {"id": "task-progress", "type": "view", "condition": "{{task.subtasks && task.subtasks.length > 0}}", "style": {"marginBottom": "12rpx"}, "children": [{"id": "progress-bar", "type": "view", "style": {"backgroundColor": "#F0F0F0", "borderRadius": "4rpx", "height": "8rpx", "position": "relative", "marginBottom": "8rpx"}, "children": [{"id": "progress-fill", "type": "view", "style": {"backgroundColor": "#52C41A", "borderRadius": "4rpx", "height": "100%", "width": "{{task.progressPercentage}}%", "transition": "width 0.3s ease"}}]}, {"id": "progress-text", "type": "text", "content": "{{task.completedSubtasks}}/{{task.totalSubtasks}} 子任务完成", "style": {"fontSize": "22rpx", "color": "#666666"}}]}]}, {"id": "task-actions", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "8rpx"}, "children": [{"id": "start-pomodoro-btn", "type": "button", "condition": "{{!task.completed}}", "content": "🍅", "style": {"fontSize": "24rpx", "backgroundColor": "#FFF2E8", "color": "#FA8C16", "border": "1rpx solid #FFD591", "borderRadius": "6rpx", "padding": "8rpx", "width": "48rpx", "height": "48rpx"}, "events": {"tap": "startPomodoroForTask"}}, {"id": "edit-task-btn", "type": "button", "content": "✏️", "style": {"fontSize": "24rpx", "backgroundColor": "#F6F6F6", "color": "#666666", "border": "1rpx solid #D9D9D9", "borderRadius": "6rpx", "padding": "8rpx", "width": "48rpx", "height": "48rpx"}, "events": {"tap": "editTask"}}]}]}]}]}, {"id": "empty-state", "type": "view", "condition": "{{filteredTasks.length === 0}}", "style": {"textAlign": "center", "padding": "60rpx 0"}, "children": [{"type": "text", "content": "📝", "style": {"fontSize": "64rpx", "display": "block", "marginBottom": "16rpx"}}, {"type": "text", "content": "{{emptyStateText}}", "style": {"fontSize": "28rpx", "color": "#999999", "marginBottom": "24rpx"}}, {"id": "add-task-empty-btn", "type": "button", "content": "添加第一个任务", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "borderRadius": "8rpx", "padding": "12rpx 24rpx", "border": "none"}, "events": {"tap": "navigateToAddTask"}}]}]}, {"id": "quick-templates-section", "type": "view", "className": "quick-templates-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "templates-title", "type": "text", "content": "快速创建", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "templates-subtitle", "type": "text", "content": "选择模板快速创建任务", "style": {"fontSize": "26rpx", "color": "#666666", "marginBottom": "24rpx"}}, {"id": "templates-list", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "12rpx"}, "children": [{"id": "template-item", "type": "button", "forEach": "{{taskTemplates}}", "forItem": "template", "forIndex": "index", "style": {"backgroundColor": "#F8F9FA", "border": "1rpx solid #E9ECEF", "borderRadius": "8rpx", "padding": "16rpx 20rpx", "textAlign": "left", "display": "flex", "alignItems": "center", "gap": "12rpx"}, "events": {"tap": "selectTaskTemplate"}, "children": [{"type": "text", "content": "{{template.icon}}", "style": {"fontSize": "24rpx"}}, {"type": "text", "content": "{{template.name}}", "style": {"fontSize": "26rpx", "color": "#333333", "flex": "1"}}, {"type": "text", "content": "{{template.duration}}分钟", "style": {"fontSize": "22rpx", "color": "#999999"}}]}]}]}], "data": {"taskStats": {"total": 15, "completed": 8, "pending": 7}, "currentFilter": "all", "filterTabs": [{"key": "all", "name": "全部"}, {"key": "pending", "name": "待完成"}, {"key": "completed", "name": "已完成"}, {"key": "today", "name": "今日"}], "filteredTasks": [{"id": "task_001", "title": "数学高数第一章复习", "subject": "数学", "description": "复习极限的概念和计算方法", "completed": false, "priority": "high", "dueDate": "今天", "isOverdue": false, "estimatedDuration": 90, "subtasks": [], "progressPercentage": 0, "completedSubtasks": 0, "totalSubtasks": 0}, {"id": "task_002", "title": "英语单词背诵", "subject": "英语", "description": "背诵考研核心词汇100个", "completed": false, "priority": "medium", "dueDate": "明天", "isOverdue": false, "estimatedDuration": 60, "subtasks": ["单词1-25", "单词26-50", "单词51-75", "单词76-100"], "progressPercentage": 50, "completedSubtasks": 2, "totalSubtasks": 4}, {"id": "task_003", "title": "政治马原理论学习", "subject": "政治", "description": "学习马克思主义基本原理", "completed": true, "priority": "low", "dueDate": "昨天", "isOverdue": false, "estimatedDuration": 120, "subtasks": [], "progressPercentage": 100, "completedSubtasks": 0, "totalSubtasks": 0}], "emptyStateText": "暂无任务", "taskTemplates": [{"id": "template_001", "name": "数学练习题", "icon": "📊", "duration": 90}, {"id": "template_002", "name": "英语阅读理解", "icon": "📖", "duration": 60}, {"id": "template_003", "name": "专业课复习", "icon": "📚", "duration": 120}, {"id": "template_004", "name": "真题模拟", "icon": "📝", "duration": 180}]}, "methods": {"navigateToAddTask": {"type": "navigate", "url": "/pages/task-center/add-task"}, "editTask": {"type": "navigate", "url": "/pages/task-center/edit-task", "params": ["taskId"]}, "toggleTaskComplete": {"type": "function", "description": "切换任务完成状态", "params": ["taskId"], "implementation": "updateTaskStatus"}, "startPomodoroForTask": {"type": "navigate", "url": "/pages/pomodoro/index", "params": ["taskId"]}, "switchFilter": {"type": "function", "description": "切换任务筛选", "params": ["<PERSON><PERSON><PERSON>"], "implementation": "filterTasks"}, "selectTaskTemplate": {"type": "navigate", "url": "/pages/task-center/add-task", "params": ["templateId"]}}, "lifecycle": {"onLoad": ["loadTaskList", "loadTaskTemplates"], "onShow": ["refreshTaskList", "updateTaskStats"]}, "interactions": {"pullToRefresh": {"enabled": true, "action": "refreshTaskList"}, "swipeActions": {"enabled": true, "actions": ["edit", "delete", "complete"]}}}