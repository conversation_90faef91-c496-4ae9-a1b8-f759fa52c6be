// custom-tab-bar/index.js
Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#1890FF",
    list: [
      {
        pagePath: "/pages/home/<USER>",
        iconText: "🏠",
        text: "首页"
      },
      {
        pagePath: "/pages/exam-center/index",
        iconText: "📅",
        text: "考试"
      },
      {
        pagePath: "/pages/task-center/index",
        iconText: "📝",
        text: "任务"
      },
      {
        pagePath: "/pages/pomodoro/index",
        iconText: "🍅",
        text: "专注"
      },
      {
        pagePath: "/pages/data-center/index",
        iconText: "📊",
        text: "数据"
      }
    ]
  },
  
  attached() {
    // 延迟执行，确保页面已经完全初始化
    setTimeout(() => {
      this.setSelected()
    }, 100)
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      const index = data.index
      
      // 更新选中状态
      this.setData({
        selected: index
      })
      
      // 跳转页面
      wx.switchTab({
        url
      })
    },

    setSelected() {
      try {
        // 获取当前页面路径
        const pages = getCurrentPages()
        if (pages.length === 0) return

        const currentPage = pages[pages.length - 1]
        if (!currentPage || !currentPage.route) return

        const currentRoute = '/' + currentPage.route

        // 找到对应的tab索引
        const index = this.data.list.findIndex(item => item.pagePath === currentRoute)

        if (index !== -1) {
          this.setData({
            selected: index
          })
        }
      } catch (error) {
        console.error('TabBar setSelected error:', error)
      }
    }
  }
})
