// pages/task-center/index.js
Page({
  data: {
    taskStats: [],
    filterOptions: [],
    currentFilter: 'all',
    tasks: [],
    filteredTasks: [],
    groupedTasks: [],
    showActionSheet: false,
    selectedTask: null,
    taskActions: [],
    showFilterMenu: false,
    currentSort: 'dueDate',
    selectedPriorities: [],
    sortOptions: [],
    priorityOptions: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadData()
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      })
    }
  },

  onPullDownRefresh() {
    this.loadData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 初始化页面
  initPage() {
    this.initFilterOptions()
    this.initTaskActions()
    this.initSortOptions()
    this.loadData()
  },

  // 初始化筛选选项
  initFilterOptions() {
    const filterOptions = [
      { value: 'all', label: '全部', count: 0 },
      { value: 'today', label: '今天', count: 0 },
      { value: 'pending', label: '待完成', count: 0 },
      { value: 'completed', label: '已完成', count: 0 },
      { value: 'overdue', label: '已逾期', count: 0 }
    ]

    this.setData({ filterOptions })
  },

  // 初始化任务操作
  initTaskActions() {
    const taskActions = [
      { id: 'view', icon: '👁️', text: '查看详情', action: 'viewDetail' },
      { id: 'edit', icon: '✏️', text: '编辑任务', action: 'edit' },
      { id: 'start', icon: '▶️', text: '开始任务', action: 'start' },
      { id: 'complete', icon: '✅', text: '标记完成', action: 'complete' },
      { id: 'delete', icon: '🗑️', text: '删除任务', action: 'delete' }
    ]

    this.setData({ taskActions })
  },

  // 初始化排序选项
  initSortOptions() {
    const sortOptions = [
      { value: 'dueDate', label: '按截止时间' },
      { value: 'priority', label: '按优先级' },
      { value: 'createTime', label: '按创建时间' },
      { value: 'progress', label: '按完成进度' }
    ]

    const priorityOptions = [
      { value: 'high', label: '高优先级' },
      { value: 'medium', label: '中优先级' },
      { value: 'low', label: '低优先级' }
    ]

    this.setData({ sortOptions, priorityOptions })
  },

  // 加载数据
  loadData() {
    this.loadTaskStats()
    this.loadTasks()
  },

  // 加载任务统计
  loadTaskStats() {
    const taskStats = [
      { label: '总任务', value: '24' },
      { label: '今日', value: '8' },
      { label: '已完成', value: '16' },
      { label: '完成率', value: '67%' }
    ]

    this.setData({ taskStats })
  },

  // 加载任务列表
  loadTasks() {
    // 模拟数据
    const tasks = [
      {
        id: 'task_001',
        title: '数学高数第一章复习',
        subject: '数学',
        examName: '2025年考研',
        priority: 'high',
        priorityText: '高优先级',
        status: 'pending',
        statusText: '进行中',
        completed: false,
        dueDate: '今天 18:00',
        estimatedTime: '2小时',
        progress: 60,
        subtasks: [
          { id: 'sub_001', title: '复习极限概念', completed: true },
          { id: 'sub_002', title: '练习极限计算', completed: true },
          { id: 'sub_003', title: '做课后习题', completed: false }
        ],
        completedSubtasks: 2,
        showSubtasks: false
      },
      {
        id: 'task_002',
        title: '英语单词背诵',
        subject: '英语',
        examName: '英语四级',
        priority: 'medium',
        priorityText: '中优先级',
        status: 'completed',
        statusText: '已完成',
        completed: true,
        completionTime: '今天 14:30',
        actualTime: '45分钟',
        estimatedTime: '30分钟'
      },
      {
        id: 'task_003',
        title: '政治马原理论学习',
        subject: '政治',
        examName: '2025年考研',
        priority: 'low',
        priorityText: '低优先级',
        status: 'pending',
        statusText: '待开始',
        completed: false,
        dueDate: '明天 20:00',
        estimatedTime: '1.5小时',
        progress: 0
      }
    ]

    this.setData({ tasks })
    this.filterTasks()
    this.updateFilterCounts()
  },

  // 筛选任务
  filterTasks() {
    const { tasks, currentFilter } = this.data
    let filteredTasks = tasks

    if (currentFilter !== 'all') {
      switch (currentFilter) {
        case 'today':
          filteredTasks = tasks.filter(task => task.dueDate && task.dueDate.includes('今天'))
          break
        case 'pending':
          filteredTasks = tasks.filter(task => !task.completed)
          break
        case 'completed':
          filteredTasks = tasks.filter(task => task.completed)
          break
        case 'overdue':
          // 这里可以添加逾期逻辑
          filteredTasks = []
          break
      }
    }

    this.setData({ filteredTasks })
    this.groupTasks()
  },

  // 按日期分组任务
  groupTasks() {
    const { filteredTasks } = this.data
    const groups = {}

    filteredTasks.forEach(task => {
      let dateKey = '其他'

      if (task.dueDate) {
        if (task.dueDate.includes('今天')) {
          dateKey = '今天'
        } else if (task.dueDate.includes('明天')) {
          dateKey = '明天'
        } else {
          dateKey = task.dueDate.split(' ')[0] || '其他'
        }
      } else if (task.completed && task.completionTime) {
        if (task.completionTime.includes('今天')) {
          dateKey = '今天'
        }
      }

      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(task)
    })

    const groupedTasks = Object.keys(groups).map(date => ({
      date,
      tasks: groups[date]
    }))

    this.setData({ groupedTasks })
  },

  // 更新筛选计数
  updateFilterCounts() {
    const { tasks, filterOptions } = this.data
    const updatedOptions = filterOptions.map(option => {
      let count = 0
      switch (option.value) {
        case 'all':
          count = tasks.length
          break
        case 'today':
          count = tasks.filter(task => task.dueDate && task.dueDate.includes('今天')).length
          break
        case 'pending':
          count = tasks.filter(task => !task.completed).length
          break
        case 'completed':
          count = tasks.filter(task => task.completed).length
          break
        case 'overdue':
          count = 0 // 逾期任务计数
          break
      }
      return { ...option, count }
    })

    this.setData({ filterOptions: updatedOptions })
  },

  // 切换筛选
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterTasks()
  },

  // 切换任务状态
  toggleTask(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.completed = !task.completed
        task.statusText = task.completed ? '已完成' : '进行中'
        if (task.completed) {
          task.completionTime = '刚刚'
        }
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()
    this.updateFilterCounts()

    wx.showToast({
      title: tasks.find(t => t.id === taskId).completed ? '任务完成！' : '任务重新开始',
      icon: 'success'
    })
  },

  // 切换子任务显示
  toggleSubtasks(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.showSubtasks = !task.showSubtasks
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()
  },

  // 切换子任务状态
  toggleSubtask(e) {
    const { taskId, subtaskId } = e.currentTarget.dataset
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.id === subtaskId) {
            subtask.completed = !subtask.completed
          }
          return subtask
        })
        task.completedSubtasks = task.subtasks.filter(s => s.completed).length
        task.progress = Math.round((task.completedSubtasks / task.subtasks.length) * 100)
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()
  },

  // 显示任务操作菜单
  showTaskActions(e) {
    const task = e.currentTarget.dataset.task
    this.setData({
      selectedTask: task,
      showActionSheet: true
    })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedTask: null
    })
  },

  // 显示筛选菜单
  showFilterMenu() {
    this.setData({ showFilterMenu: true })
  },

  // 隐藏筛选菜单
  hideFilterMenu() {
    this.setData({ showFilterMenu: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 执行任务操作
  executeTaskAction(e) {
    const action = e.currentTarget.dataset.action
    const task = this.data.selectedTask

    this.hideActionSheet()

    switch (action) {
      case 'viewDetail':
        this.viewTaskDetail({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'edit':
        this.editTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'start':
        this.startTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'complete':
        this.toggleTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'delete':
        this.deleteTask(task)
        break
    }
  },

  // 页面跳转和操作方法
  openSearch() {
    wx.navigateTo({
      url: '/pages/search/index?type=task'
    })
  },

  viewTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/task-detail/index?id=' + taskId
    })
  },

  addTask() {
    wx.navigateTo({
      url: '/pages/add-task/index'
    })
  },

  editTask(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/edit-task/index?id=' + taskId
    })
  },

  startTask(e) {
    const taskId = e.currentTarget.dataset.id
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  startPomodoro(e) {
    const taskId = e.currentTarget.dataset.id
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  deleteTask(task) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除任务"${task.title}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          const tasks = this.data.tasks.filter(t => t.id !== task.id)
          this.setData({ tasks })
          this.filterTasks()
          this.updateFilterCounts()

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 获取空状态标题和消息
  getEmptyTitle() {
    const { currentFilter } = this.data
    const titles = {
      all: '还没有任务',
      today: '今天没有任务',
      pending: '没有待完成的任务',
      completed: '还没有完成的任务',
      overdue: '没有逾期的任务'
    }
    return titles[currentFilter] || '暂无数据'
  },

  getEmptyMessage() {
    const { currentFilter } = this.data
    const messages = {
      all: '添加你的第一个学习任务',
      today: '今天的任务都完成了',
      pending: '所有任务都已完成',
      completed: '完成一些任务来查看记录',
      overdue: '保持良好的学习习惯'
    }
    return messages[currentFilter] || '暂无相关数据'
  }
})