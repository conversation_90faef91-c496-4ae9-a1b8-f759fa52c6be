// pages/cloud-test/index.js
const CloudTest = require('../../utils/cloudTest')
const CloudApi = require('../../utils/cloudApi')

Page({
  data: {
    testResults: [],
    loading: false,
    cloudStatus: '未知'
  },

  onLoad() {
    this.checkCloudStatus()
  },

  // 检查云开发状态
  checkCloudStatus() {
    if (wx.cloud) {
      this.setData({
        cloudStatus: '已初始化'
      })
    } else {
      this.setData({
        cloudStatus: '未初始化'
      })
    }
  },

  // 测试云开发连接
  async testConnection() {
    this.setData({ loading: true })
    
    try {
      const result = await CloudTest.testConnection()
      this.addTestResult('连接测试', result)
    } catch (error) {
      this.addTestResult('连接测试', { success: false, error: error.message })
    }
    
    this.setData({ loading: false })
  },

  // 测试数据库
  async testDatabase() {
    this.setData({ loading: true })
    
    try {
      const result = await CloudTest.testDatabase()
      this.addTestResult('数据库测试', result)
    } catch (error) {
      this.addTestResult('数据库测试', { success: false, error: error.message })
    }
    
    this.setData({ loading: false })
  },

  // 测试创建任务
  async testCreateTask() {
    this.setData({ loading: true })
    
    try {
      const result = await CloudTest.createTestTask()
      this.addTestResult('创建任务测试', result)
    } catch (error) {
      this.addTestResult('创建任务测试', { success: false, error: error.message })
    }
    
    this.setData({ loading: false })
  },

  // 测试API调用
  async testApiCall() {
    this.setData({ loading: true })
    
    try {
      const result = await CloudApi.getTasks({}, 5)
      this.addTestResult('API调用测试', result)
    } catch (error) {
      this.addTestResult('API调用测试', { success: false, error: error.message })
    }
    
    this.setData({ loading: false })
  },

  // 运行完整测试
  async runFullTest() {
    this.setData({ 
      loading: true,
      testResults: []
    })
    
    try {
      const result = await CloudTest.runFullTest()
      this.addTestResult('完整测试', result)
      
      // 如果基础测试通过，继续测试API
      if (result.success) {
        await this.testApiCall()
      }
    } catch (error) {
      this.addTestResult('完整测试', { success: false, error: error.message })
    }
    
    this.setData({ loading: false })
  },

  // 添加测试结果
  addTestResult(testName, result) {
    const testResults = this.data.testResults
    testResults.push({
      name: testName,
      success: result.success,
      message: result.success ? '成功' : result.error,
      data: result.data || null,
      time: new Date().toLocaleTimeString()
    })
    
    this.setData({ testResults })
  },

  // 清除测试结果
  clearResults() {
    this.setData({ testResults: [] })
  },

  // 复制结果到剪贴板
  copyResults() {
    const results = this.data.testResults.map(result => 
      `${result.name}: ${result.success ? '成功' : '失败'} - ${result.message}`
    ).join('\n')
    
    wx.setClipboardData({
      data: results,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  }
})
