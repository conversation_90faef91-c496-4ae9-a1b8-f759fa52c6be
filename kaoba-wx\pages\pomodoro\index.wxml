<!--pages/pomodoro/index.wxml-->
<view class="container {{focusMode ? 'focus-mode' : ''}}">
  <!-- 普通模式布局 -->
  <view class="normal-layout" wx:if="{{!focusMode}}">
    <!-- 学习模式选择 -->
    <view class="mode-selector-container">
      <text class="section-title">学习模式</text>
      <view class="mode-options">
        <button class="mode-option {{studyMode === 'quick' ? 'active' : ''}}"
                bindtap="selectStudyMode"
                data-mode="quick">
          🚀 快速专注
        </button>
        <button class="mode-option {{studyMode === 'task' ? 'active' : ''}}"
                bindtap="selectStudyMode"
                data-mode="task">
          📋 任务专注
        </button>
      </view>
    </view>

    <!-- 任务关联 -->
    <view class="task-association-container" wx:if="{{studyMode === 'task'}}">
      <text class="section-title">📋 关联任务</text>

      <!-- 已选择任务显示 -->
      <view class="selected-task-display" wx:if="{{selectedTask}}">
        <view class="task-info">
          <view class="task-details">
            <text class="task-title">{{selectedTask.title}}</text>
            <text class="task-progress">进度：🍅{{selectedTask.completedPomodoros || 0}}/{{selectedTask.totalPomodoros || 1}}</text>
          </view>
          <button class="change-task-btn" bindtap="showTaskSelector">更换</button>
        </view>

        <!-- 智能拆解显示 -->
        <view class="task-breakdown" wx:if="{{selectedTask.breakdown}}">
          <text class="breakdown-title">🧠 智能拆解建议</text>
          <view class="breakdown-list">
            <view class="breakdown-item" wx:for="{{selectedTask.breakdown}}" wx:key="index">
              <text class="pomodoro-icon">{{item.completed ? '✅' : '🍅'}}</text>
              <text class="breakdown-text {{item.completed ? 'completed' : ''}}">{{item.title}} ({{item.duration}}min)</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 选择任务按钮 -->
      <button class="select-task-btn" wx:if="{{!selectedTask}}" bindtap="showTaskSelector">
        📋 选择要学习的任务
      </button>
    </view>

    <!-- 声音设置 -->
    <view class="sound-settings-container">
      <text class="section-title">🔊 声音设置</text>

      <view class="background-sound">
        <text class="setting-label">背景音：</text>
        <view class="sound-options">
          <button class="sound-option {{selectedBgSound === sound.id ? 'active' : ''}}"
                  wx:for="{{backgroundSounds}}"
                  wx:for-item="sound"
                  wx:key="id"
                  bindtap="selectBackgroundSound"
                  data-sound="{{sound.id}}">
            {{sound.icon}} {{sound.name}}
          </button>
        </view>
      </view>

      <view class="volume-control">
        <text class="setting-label">音量：</text>
        <slider class="volume-slider"
                value="{{volume}}"
                min="0"
                max="100"
                bindchange="adjustVolume"/>
        <text class="volume-value">{{volume}}%</text>
      </view>
    </view>

    <!-- 番茄钟主体 -->
    <view class="timer-container">
      <!-- 当前会话信息 -->
      <view class="current-session" wx:if="{{currentSession}}">
        <text class="session-title">{{currentSession.title}}</text>
        <text class="session-subtitle">{{currentSession.subtitle}}</text>
      </view>

      <!-- 计时器显示 -->
      <view class="timer-display">
        <view class="timer-circle {{isRunning ? 'breathing' : ''}}"
              style="background: conic-gradient({{timerColor}} 0deg {{progressDegree}}deg, #F0F0F0 {{progressDegree}}deg 360deg)">
          <view class="timer-inner">
            <text class="time-text">{{displayTime}}</text>
            <text class="session-type">{{sessionTypeText}}</text>
          </view>
        </view>
      </view>

      <!-- 控制按钮 -->
      <view class="timer-controls">
        <button class="control-btn start-btn {{isRunning ? 'pause' : 'start'}}" bindtap="toggleTimer">
          {{isRunning ? '⏸️' : '▶️'}}
        </button>
        <button class="control-btn stop-btn" wx:if="{{isRunning || isPaused}}" bindtap="stopTimer">
          ⏹️
        </button>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions-container">
      <view class="actions-grid">
        <button class="action-btn focus-mode-btn" bindtap="enterFocusMode">
          🖥️ 专注模式
        </button>
        <button class="action-btn sound-btn" bindtap="toggleSoundSettings">
          🔊 声音设置
        </button>
        <button class="action-btn stats-btn" bindtap="viewStatistics">
          📊 学习统计
        </button>
        <button class="action-btn task-btn" bindtap="manageTask">
          📋 任务管理
        </button>
      </view>
    </view>
  </view>

  <!-- 专注模式布局 -->
  <view class="focus-layout" wx:if="{{focusMode}}">
    <!-- 专注模式头部 -->
    <view class="focus-header">
      <button class="exit-focus-btn" bindtap="exitFocusMode">×</button>
      <text class="focus-title">🌙 深度专注</text>
      <view class="focus-placeholder"></view>
    </view>

    <!-- 专注模式主体 -->
    <view class="focus-main">
      <!-- 专注模式计时器 -->
      <view class="focus-timer-display">
        <view class="focus-timer-circle {{isRunning ? 'breathing' : ''}}"
              style="background: conic-gradient({{timerColor}} 0deg {{progressDegree}}deg, rgba(255,255,255,0.1) {{progressDegree}}deg 360deg)">
          <view class="focus-timer-inner">
            <text class="focus-time-text">{{displayTime}}</text>
            <text class="focus-session-indicator">{{sessionIndicator}}</text>
          </view>
        </view>
      </view>

      <!-- 专注模式任务信息 -->
      <view class="focus-task-info" wx:if="{{currentSession}}">
        <text class="focus-task-title">{{currentSession.title}}</text>
        <text class="focus-task-subtitle">{{currentSession.subtitle}}</text>
      </view>

      <!-- 专注模式控制 -->
      <view class="focus-controls">
        <button class="focus-pause-btn" wx:if="{{isRunning}}" bindtap="pauseTimer">⏸️</button>
        <button class="focus-stop-btn" bindtap="stopTimer">🛑</button>
      </view>
    </view>
  </view>
</view>

<!-- 任务选择弹窗 -->
<view class="task-modal-mask" wx:if="{{showTaskModal}}" bindtap="hideTaskModal">
  <view class="task-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择任务</text>
      <text class="modal-close" bindtap="hideTaskModal">×</text>
    </view>
    <view class="modal-body">
      <view class="task-option no-task {{!selectedTask ? 'selected' : ''}}" bindtap="selectTaskOption" data-task="">
        <text class="task-icon">🎯</text>
        <text class="task-name">自由专注</text>
        <text class="task-check" wx:if="{{!selectedTask}}">✓</text>
      </view>
      <view class="task-option {{selectedTask && selectedTask.id === item.id ? 'selected' : ''}}"
            wx:for="{{availableTasks}}"
            wx:key="id"
            bindtap="selectTaskOption"
            data-task="{{item}}">
        <text class="task-icon">📝</text>
        <view class="task-content">
          <text class="task-name">{{item.title}}</text>
          <text class="task-subject" wx:if="{{item.subject}}">{{item.subject}}</text>
        </view>
        <text class="task-check" wx:if="{{selectedTask && selectedTask.id === item.id}}">✓</text>
      </view>
    </view>
  </view>
</view>

<!-- 任务拆解弹窗 -->
<view class="breakdown-modal-mask" wx:if="{{showBreakdownModal}}" bindtap="hideBreakdownModal">
  <view class="breakdown-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">智能拆解建议</text>
      <text class="modal-close" bindtap="hideBreakdownModal">×</text>
    </view>
    <view class="modal-body">
      <view class="breakdown-info">
        <text class="breakdown-task-title">{{selectedTask.title}}</text>
        <text class="breakdown-description">根据任务复杂度，建议拆解为以下学习阶段：</text>
      </view>

      <view class="breakdown-plan">
        <view class="breakdown-stage" wx:for="{{taskBreakdown}}" wx:key="index">
          <view class="stage-header">
            <text class="stage-icon">{{item.completed ? '✅' : '🍅'}}</text>
            <text class="stage-title">第{{index + 1}}个番茄钟：{{item.title}}</text>
            <text class="stage-duration">{{item.duration}}分钟</text>
          </view>
          <text class="stage-description">{{item.description}}</text>
        </view>
      </view>

      <view class="breakdown-actions">
        <button class="breakdown-btn secondary" bindtap="customizeBreakdown">自定义</button>
        <button class="breakdown-btn primary" bindtap="confirmBreakdown">确认计划</button>
      </view>
    </view>
  </view>
</view>

<!-- 声音设置弹窗 -->
<view class="sound-modal-mask" wx:if="{{showSoundModal}}" bindtap="hideSoundModal">
  <view class="sound-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">声音设置</text>
      <text class="modal-close" bindtap="hideSoundModal">×</text>
    </view>
    <view class="modal-body">
      <view class="sound-section">
        <text class="sound-section-title">背景音</text>
        <view class="sound-grid">
          <view class="sound-card {{selectedBgSound === sound.id ? 'active' : ''}}"
                wx:for="{{backgroundSounds}}"
                wx:for-item="sound"
                wx:key="id"
                bindtap="selectBackgroundSound"
                data-sound="{{sound.id}}">
            <text class="sound-card-icon">{{sound.icon}}</text>
            <text class="sound-card-name">{{sound.name}}</text>
            <text class="sound-card-desc">{{sound.description}}</text>
            <button class="sound-preview-btn" catchtap="previewSound" data-sound="{{sound.id}}">试听</button>
          </view>
        </view>
      </view>

      <view class="sound-section">
        <text class="sound-section-title">提示音</text>
        <view class="notification-sounds">
          <view class="notification-item" wx:for="{{notificationSounds}}" wx:key="id">
            <text class="notification-name">{{item.name}}</text>
            <switch checked="{{item.enabled}}" bindchange="toggleNotificationSound" data-sound="{{item.id}}"/>
          </view>
        </view>
      </view>

      <view class="sound-section">
        <text class="sound-section-title">音量控制</text>
        <view class="volume-controls">
          <view class="volume-item">
            <text class="volume-label">背景音量</text>
            <slider value="{{bgVolume}}" min="0" max="100" bindchange="adjustBgVolume"/>
            <text class="volume-text">{{bgVolume}}%</text>
          </view>
          <view class="volume-item">
            <text class="volume-label">提示音量</text>
            <slider value="{{notificationVolume}}" min="0" max="100" bindchange="adjustNotificationVolume"/>
            <text class="volume-text">{{notificationVolume}}%</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
