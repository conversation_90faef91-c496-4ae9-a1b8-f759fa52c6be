// pages/settings/index.js
Page({
  data: {
    settings: {
      // 学习设置
      workDuration: 25,
      shortBreakDuration: 5,
      longBreakDuration: 15,
      autoStartBreak: true,

      // 提醒设置
      examReminder: true,
      taskReminder: true,
      studyReminder: false,
      reminderTime: '20:00',

      // 声音设置
      soundEnabled: true,
      vibrationEnabled: true,
      volume: 70,

      // 显示设置
      darkMode: false,
      animationEnabled: true
    }
  },

  onLoad(options) {
    this.loadSettings()
  },

  // 加载设置
  loadSettings() {
    try {
      const savedSettings = wx.getStorageSync('appSettings') || {}
      const settings = { ...this.data.settings, ...savedSettings }
      this.setData({ settings })
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  },

  // 保存设置
  saveSettings() {
    try {
      wx.setStorageSync('appSettings', this.data.settings)
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  },

  // 调整时间设置
  adjustTime(e) {
    const { type, action } = e.currentTarget.dataset
    const settings = { ...this.data.settings }

    let currentValue = settings[type === 'work' ? 'workDuration' :
                              type === 'shortBreak' ? 'shortBreakDuration' :
                              'longBreakDuration']

    if (action === 'increase') {
      currentValue += type === 'shortBreak' ? 1 : 5
    } else {
      currentValue -= type === 'shortBreak' ? 1 : 5
    }

    // 设置范围限制
    if (type === 'work') {
      currentValue = Math.max(5, Math.min(60, currentValue))
      settings.workDuration = currentValue
    } else if (type === 'shortBreak') {
      currentValue = Math.max(1, Math.min(30, currentValue))
      settings.shortBreakDuration = currentValue
    } else {
      currentValue = Math.max(5, Math.min(60, currentValue))
      settings.longBreakDuration = currentValue
    }

    this.setData({ settings })
    this.saveSettings()
  },

  // 切换开关设置
  toggleAutoStartBreak(e) {
    const settings = { ...this.data.settings }
    settings.autoStartBreak = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  toggleExamReminder(e) {
    const settings = { ...this.data.settings }
    settings.examReminder = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  toggleTaskReminder(e) {
    const settings = { ...this.data.settings }
    settings.taskReminder = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  toggleStudyReminder(e) {
    const settings = { ...this.data.settings }
    settings.studyReminder = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  toggleSound(e) {
    const settings = { ...this.data.settings }
    settings.soundEnabled = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  toggleVibration(e) {
    const settings = { ...this.data.settings }
    settings.vibrationEnabled = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  toggleDarkMode(e) {
    const settings = { ...this.data.settings }
    settings.darkMode = e.detail.value
    this.setData({ settings })
    this.saveSettings()

    wx.showToast({
      title: '重启应用后生效',
      icon: 'none'
    })
  },

  toggleAnimation(e) {
    const settings = { ...this.data.settings }
    settings.animationEnabled = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  // 时间选择
  onReminderTimeChange(e) {
    const settings = { ...this.data.settings }
    settings.reminderTime = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  // 音量调节
  adjustVolume(e) {
    const settings = { ...this.data.settings }
    settings.volume = e.detail.value
    this.setData({ settings })
    this.saveSettings()
  },

  // 数据操作
  exportData() {
    wx.navigateTo({
      url: '/pages/data-export/index'
    })
  },

  clearData() {
    wx.showModal({
      title: '确认清除',
      content: '此操作将清除所有本地数据，包括考试、任务、学习记录等，且无法恢复。确定要继续吗？',
      confirmText: '确认清除',
      confirmColor: '#E74C3C',
      success: (res) => {
        if (res.confirm) {
          this.performClearData()
        }
      }
    })
  },

  performClearData() {
    try {
      // 清除所有数据
      wx.clearStorageSync()

      wx.showToast({
        title: '数据已清除',
        icon: 'success'
      })

      // 重新加载默认设置
      setTimeout(() => {
        this.loadSettings()
      }, 1000)

    } catch (error) {
      console.error('清除数据失败:', error)
      wx.showToast({
        title: '清除失败',
        icon: 'none'
      })
    }
  },

  // 关于和帮助
  showAbout() {
    wx.showModal({
      title: '关于要考试啦',
      content: '版本：v2.0.0\n开发：Augment Code\n\n一款专为学生设计的学习管理工具，帮助您高效管理考试、任务和学习时间。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  showHelp() {
    wx.navigateTo({
      url: '/pages/help-feedback/index'
    })
  },

  checkUpdate() {
    wx.showLoading({
      title: '检查中...'
    })

    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '已是最新版本',
        icon: 'success'
      })
    }, 1500)
  }
})