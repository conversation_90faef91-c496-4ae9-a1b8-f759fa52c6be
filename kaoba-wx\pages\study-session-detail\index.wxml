<!--pages/study-session-detail/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <text class="page-title">📊 学习会话详情</text>
    <text class="page-subtitle">深度分析您的学习效率</text>
  </view>

  <!-- 筛选器 -->
  <view class="filter-bar">
    <view class="filter-tabs">
      <button class="filter-tab {{filterType === 'all' ? 'active' : ''}}"
              bindtap="switchFilter"
              data-type="all">
        全部
      </button>
      <button class="filter-tab {{filterType === 'today' ? 'active' : ''}}"
              bindtap="switchFilter"
              data-type="today">
        今日
      </button>
      <button class="filter-tab {{filterType === 'week' ? 'active' : ''}}"
              bindtap="switchFilter"
              data-type="week">
        本周
      </button>
      <button class="filter-tab {{filterType === 'month' ? 'active' : ''}}"
              bindtap="switchFilter"
              data-type="month">
        本月
      </button>
    </view>

    <view class="sort-options">
      <picker range="{{sortOptions}}"
              range-key="name"
              value="{{sortIndex}}"
              bindchange="onSortChange">
        <view class="sort-picker">
          <text class="sort-text">{{sortOptions[sortIndex].name}}</text>
          <text class="sort-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 统计概览 -->
  <view class="stats-overview">
    <view class="stat-card">
      <text class="stat-value">{{overview.totalSessions}}</text>
      <text class="stat-label">总会话数</text>
    </view>
    <view class="stat-card">
      <text class="stat-value">{{overview.totalTime}}</text>
      <text class="stat-label">总时长</text>
    </view>
    <view class="stat-card">
      <text class="stat-value">{{overview.avgEfficiency}}%</text>
      <text class="stat-label">平均效率</text>
    </view>
    <view class="stat-card">
      <text class="stat-value">{{overview.completionRate}}%</text>
      <text class="stat-label">完成率</text>
    </view>
  </view>

  <!-- 会话列表 -->
  <view class="sessions-container">
    <view class="section-header">
      <text class="section-title">学习会话记录</text>
      <text class="session-count">共 {{filteredSessions.length}} 条记录</text>
    </view>

    <view class="sessions-list" wx:if="{{filteredSessions.length > 0}}">
      <view class="session-item"
            wx:for="{{filteredSessions}}"
            wx:key="id"
            bindtap="viewSessionDetail"
            data-session="{{item}}">

        <!-- 会话基本信息 -->
        <view class="session-header">
          <view class="session-type">
            <text class="type-icon">{{item.typeIcon}}</text>
            <text class="type-text">{{item.typeName}}</text>
          </view>
          <view class="session-status {{item.status}}">
            <text class="status-text">{{item.statusText}}</text>
          </view>
        </view>

        <!-- 会话详情 -->
        <view class="session-content">
          <view class="session-info">
            <text class="session-title">{{item.title || '专注学习'}}</text>
            <text class="session-desc" wx:if="{{item.taskName}}">关联任务：{{item.taskName}}</text>
            <text class="session-desc" wx:if="{{item.subject}}">科目：{{item.subject}}</text>
          </view>

          <view class="session-metrics">
            <view class="metric-item">
              <text class="metric-icon">⏱️</text>
              <text class="metric-text">{{item.duration}}</text>
            </view>
            <view class="metric-item" wx:if="{{item.efficiency}}">
              <text class="metric-icon">📈</text>
              <text class="metric-text">{{item.efficiency}}%</text>
            </view>
            <view class="metric-item" wx:if="{{item.interruptions}}">
              <text class="metric-icon">⚠️</text>
              <text class="metric-text">{{item.interruptions}}次中断</text>
            </view>
          </view>
        </view>

        <!-- 会话时间 -->
        <view class="session-footer">
          <text class="session-time">{{item.startTime}} - {{item.endTime}}</text>
          <text class="session-date">{{item.date}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredSessions.length === 0}}">
      <text class="empty-icon">📝</text>
      <text class="empty-title">暂无学习记录</text>
      <text class="empty-desc">开始您的第一次专注学习吧</text>
      <button class="start-study-btn" bindtap="startStudy">开始学习</button>
    </view>
  </view>
</view>

<!-- 会话详情弹窗 -->
<view class="session-modal" wx:if="{{showSessionModal}}" bindtap="closeSessionModal">
  <view class="session-modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">会话详情</text>
      <text class="modal-close" bindtap="closeSessionModal">×</text>
    </view>

    <view class="modal-body" wx:if="{{selectedSession}}">
      <!-- 会话概览 -->
      <view class="detail-section">
        <text class="detail-title">📊 会话概览</text>
        <view class="detail-grid">
          <view class="detail-item">
            <text class="detail-label">会话类型</text>
            <text class="detail-value">{{selectedSession.typeName}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">开始时间</text>
            <text class="detail-value">{{selectedSession.startTime}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">结束时间</text>
            <text class="detail-value">{{selectedSession.endTime}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">实际时长</text>
            <text class="detail-value">{{selectedSession.duration}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">计划时长</text>
            <text class="detail-value">{{selectedSession.plannedDuration}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">完成状态</text>
            <text class="detail-value">{{selectedSession.statusText}}</text>
          </view>
        </view>
      </view>

      <!-- 效率分析 -->
      <view class="detail-section" wx:if="{{selectedSession.efficiency}}">
        <text class="detail-title">📈 效率分析</text>
        <view class="efficiency-chart">
          <view class="efficiency-bar">
            <view class="efficiency-fill" style="width: {{selectedSession.efficiency}}%"></view>
          </view>
          <text class="efficiency-text">学习效率：{{selectedSession.efficiency}}%</text>
        </view>
        <view class="efficiency-factors">
          <view class="factor-item good" wx:if="{{selectedSession.focusTime}}">
            <text class="factor-icon">✅</text>
            <text class="factor-text">专注时间：{{selectedSession.focusTime}}</text>
          </view>
          <view class="factor-item warning" wx:if="{{selectedSession.interruptions > 0}}">
            <text class="factor-icon">⚠️</text>
            <text class="factor-text">中断次数：{{selectedSession.interruptions}}次</text>
          </view>
          <view class="factor-item info" wx:if="{{selectedSession.backgroundSound}}">
            <text class="factor-icon">🔊</text>
            <text class="factor-text">背景音：{{selectedSession.backgroundSound}}</text>
          </view>
        </view>
      </view>

      <!-- 关联信息 -->
      <view class="detail-section" wx:if="{{selectedSession.taskName}}">
        <text class="detail-title">🔗 关联信息</text>
        <view class="related-info">
          <view class="related-item">
            <text class="related-label">关联任务</text>
            <text class="related-value">{{selectedSession.taskName}}</text>
          </view>
          <view class="related-item" wx:if="{{selectedSession.subject}}">
            <text class="related-label">学习科目</text>
            <text class="related-value">{{selectedSession.subject}}</text>
          </view>
          <view class="related-item" wx:if="{{selectedSession.examName}}">
            <text class="related-label">目标考试</text>
            <text class="related-value">{{selectedSession.examName}}</text>
          </view>
        </view>
      </view>

      <!-- 学习建议 -->
      <view class="detail-section" wx:if="{{selectedSession.suggestions}}">
        <text class="detail-title">💡 学习建议</text>
        <view class="suggestions-list">
          <text class="suggestion-item" wx:for="{{selectedSession.suggestions}}" wx:key="index">
            • {{item}}
          </text>
        </view>
      </view>
    </view>
  </view>
</view>