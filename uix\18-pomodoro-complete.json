{"pageInfo": {"pageName": "专注完成", "pageId": "pomodoroComplete", "pageType": "page", "description": "番茄钟完成后的反馈和记录页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx", "minHeight": "100vh"}, "components": [{"id": "celebration-section", "type": "view", "className": "celebration-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "24rpx", "padding": "60rpx 40rpx", "marginBottom": "24rpx", "boxShadow": "0 8rpx 24rpx rgba(0,0,0,0.1)", "textAlign": "center"}, "children": [{"id": "celebration-icon", "type": "text", "content": "🎉", "style": {"fontSize": "80rpx", "marginBottom": "24rpx", "animation": "bounce 1s ease-in-out"}}, {"id": "celebration-title", "type": "text", "content": "专注完成！", "style": {"fontSize": "48rpx", "fontWeight": "700", "color": "#52C41A", "marginBottom": "16rpx"}}, {"id": "celebration-subtitle", "type": "text", "content": "恭喜你完成了 {{sessionData.duration}} 分钟的专注学习", "style": {"fontSize": "28rpx", "color": "#666666", "lineHeight": "1.4", "marginBottom": "32rpx"}}, {"id": "session-summary", "type": "view", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "16rpx", "padding": "24rpx", "marginBottom": "32rpx"}, "children": [{"id": "task-info", "type": "view", "condition": "{{sessionData.taskName}}", "style": {"marginBottom": "16rpx"}, "children": [{"id": "task-label", "type": "text", "content": "学习任务", "style": {"fontSize": "24rpx", "color": "#999999", "marginBottom": "4rpx"}}, {"id": "task-name", "type": "text", "content": "{{sessionData.taskName}}", "style": {"fontSize": "28rpx", "color": "#333333", "fontWeight": "500"}}]}, {"id": "time-info", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}, "children": [{"id": "start-time", "type": "view", "children": [{"type": "text", "content": "开始时间", "style": {"fontSize": "24rpx", "color": "#999999", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{sessionData.startTime}}", "style": {"fontSize": "26rpx", "color": "#333333"}}]}, {"id": "end-time", "type": "view", "children": [{"type": "text", "content": "结束时间", "style": {"fontSize": "24rpx", "color": "#999999", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{sessionData.endTime}}", "style": {"fontSize": "26rpx", "color": "#333333"}}]}]}]}, {"id": "achievement-badge", "type": "view", "condition": "{{sessionData.isNewRecord}}", "style": {"backgroundColor": "#FFF7E6", "border": "1rpx solid #FFD591", "borderRadius": "12rpx", "padding": "16rpx", "marginBottom": "24rpx"}, "children": [{"type": "text", "content": "🏆 新纪录！", "style": {"fontSize": "24rpx", "color": "#FA8C16", "fontWeight": "500"}}]}]}, {"id": "session-rating", "type": "view", "className": "rating-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "rating-title", "type": "text", "content": "这次专注效果如何？", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "textAlign": "center", "marginBottom": "24rpx"}}, {"id": "rating-stars", "type": "view", "style": {"display": "flex", "justifyContent": "center", "gap": "16rpx", "marginBottom": "24rpx"}, "children": [{"id": "rating-star", "type": "button", "forEach": "{{ratingStars}}", "forItem": "star", "forIndex": "index", "content": "{{star.filled ? '⭐' : '☆'}}", "style": {"fontSize": "48rpx", "backgroundColor": "transparent", "border": "none", "padding": "8rpx"}, "events": {"tap": "setRating"}}]}, {"id": "rating-feedback", "type": "view", "condition": "{{sessionRating.rating > 0}}", "children": [{"id": "feedback-input", "type": "textarea", "placeholder": "分享一下这次学习的感受吧（可选）", "value": "{{sessionRating.feedback}}", "maxlength": "200", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "26rpx", "border": "1rpx solid #E9ECEF", "minHeight": "120rpx", "width": "100%"}, "events": {"input": "updateFeedback"}}]}]}, {"id": "break-suggestion", "type": "view", "className": "break-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "break-title", "type": "text", "content": "休息建议", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "break-options", "type": "view", "style": {"display": "flex", "gap": "12rpx", "flexWrap": "wrap"}, "children": [{"id": "break-option", "type": "button", "forEach": "{{breakOptions}}", "forItem": "option", "forIndex": "index", "content": "{{option.label}}", "style": {"fontSize": "24rpx", "padding": "8rpx 16rpx", "borderRadius": "6rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{selectedBreak === option.value ? '#E6F7FF' : '#FFFFFF'}}", "color": "{{selectedBreak === option.value ? '#1890FF' : '#333333'}}"}, "events": {"tap": "selectBreakOption"}}]}, {"id": "break-timer", "type": "view", "condition": "{{selectedBreak && selectedBreak !== 'none'}}", "style": {"marginTop": "20rpx", "textAlign": "center"}, "children": [{"id": "break-countdown", "type": "text", "content": "{{breakCountdown}}", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#4ECDC4", "marginBottom": "8rpx"}}, {"id": "break-status", "type": "text", "content": "休息中...", "style": {"fontSize": "24rpx", "color": "#666666"}}]}]}, {"id": "daily-progress", "type": "view", "className": "progress-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "progress-title", "type": "text", "content": "今日进度", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "20rpx"}}, {"id": "progress-stats", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr 1fr", "gap": "16rpx"}, "children": [{"id": "today-pomodoros", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#FFF2E8", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{todayStats.completedPomodoros}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#FA8C16", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "番茄钟", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "today-time", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#E6F7FF", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{todayStats.totalTime}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#1890FF", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "总时长", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "today-goal", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#F6FFED", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{todayStats.goalProgress}}%", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#52C41A", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "目标达成", "style": {"fontSize": "22rpx", "color": "#666666"}}]}]}]}, {"id": "action-buttons", "type": "view", "className": "actions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "primary-actions", "type": "view", "style": {"display": "flex", "gap": "16rpx", "marginBottom": "16rpx"}, "children": [{"id": "continue-btn", "type": "button", "content": "继续专注", "style": {"flex": "1", "fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#52C41A", "borderRadius": "8rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "startNewPomodoro"}}, {"id": "finish-btn", "type": "button", "content": "结束学习", "style": {"flex": "1", "fontSize": "28rpx", "color": "#666666", "backgroundColor": "#F5F5F5", "borderRadius": "8rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "finishStudy"}}]}, {"id": "secondary-actions", "type": "view", "style": {"display": "flex", "justifyContent": "center", "gap": "24rpx"}, "children": [{"id": "view-stats-btn", "type": "button", "content": "查看统计", "style": {"fontSize": "26rpx", "color": "#1890FF", "backgroundColor": "transparent", "border": "1rpx solid #1890FF", "borderRadius": "6rpx", "padding": "8rpx 16rpx"}, "events": {"tap": "viewStatistics"}}, {"id": "share-btn", "type": "button", "content": "分享成果", "style": {"fontSize": "26rpx", "color": "#52C41A", "backgroundColor": "transparent", "border": "1rpx solid #52C41A", "borderRadius": "6rpx", "padding": "8rpx 16rpx"}, "events": {"tap": "shareAchievement"}}]}]}], "data": {"sessionData": {"duration": 25, "taskName": "数学高数第一章复习", "startTime": "14:30", "endTime": "14:55", "isNewRecord": false}, "sessionRating": {"rating": 0, "feedback": ""}, "ratingStars": [{"value": 1, "filled": false}, {"value": 2, "filled": false}, {"value": 3, "filled": false}, {"value": 4, "filled": false}, {"value": 5, "filled": false}], "selectedBreak": "", "breakCountdown": "05:00", "breakOptions": [{"value": "5min", "label": "5分钟休息"}, {"value": "10min", "label": "10分钟休息"}, {"value": "15min", "label": "15分钟休息"}, {"value": "none", "label": "不休息"}], "todayStats": {"completedPomodoros": 4, "totalTime": "2.5h", "goalProgress": 83}}, "methods": {"setRating": {"type": "function", "description": "设置评分", "params": ["rating"], "implementation": "updateSessionRating"}, "updateFeedback": {"type": "function", "description": "更新反馈内容", "params": ["feedback"], "implementation": "setSessionFeedback"}, "selectBreakOption": {"type": "function", "description": "选择休息选项", "params": ["option"], "implementation": "startBreakTimer"}, "startNewPomodoro": {"type": "navigate", "url": "/pages/pomodoro/index"}, "finishStudy": {"type": "function", "description": "结束学习", "implementation": "saveSessionAndReturn"}, "viewStatistics": {"type": "navigate", "url": "/pages/data-center/index"}, "shareAchievement": {"type": "function", "description": "分享学习成果", "implementation": "generateShareImage"}}, "lifecycle": {"onLoad": ["loadSessionData", "initRating"], "onUnload": ["saveSessionData"]}, "animations": {"bounce": {"keyframes": {"0%, 20%, 53%, 80%, 100%": {"transform": "translateY(0)"}, "40%, 43%": {"transform": "translateY(-30rpx)"}, "70%": {"transform": "translateY(-15rpx)"}, "90%": {"transform": "translateY(-4rpx)"}}, "duration": "1s", "timingFunction": "ease-in-out"}}}