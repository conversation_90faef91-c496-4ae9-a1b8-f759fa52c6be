// 云开发API工具类
class CloudApi {
  // 任务管理相关API
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getTasks',
          data: { filter, limit, skip }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取任务列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addTask(taskData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'addTask',
          data: taskData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateTask(taskId, updates) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'updateTask',
          data: { taskId, updates }
        }
      })
      return result.result
    } catch (error) {
      console.error('更新任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteTask(taskId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'deleteTask',
          data: { taskId }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async completeTask(taskId, completed = true) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'completeTask',
          data: { taskId, completed }
        }
      })
      return result.result
    } catch (error) {
      console.error('完成任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getTaskStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getTaskStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取任务统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 考试管理相关API
  static async getExams(filter = {}, limit = 20, skip = 0) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExams',
          data: { filter, limit, skip }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addExam(examData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'addExam',
          data: examData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateExam(examId, updates) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'updateExam',
          data: { examId, updates }
        }
      })
      return result.result
    } catch (error) {
      console.error('更新考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteExam(examId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'deleteExam',
          data: { examId }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getUpcomingExams',
          data: { days, limit }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取即将到来的考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExamStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 学习管理相关API
  static async startStudySession(sessionData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'startStudySession',
          data: sessionData
        }
      })
      return result.result
    } catch (error) {
      console.error('开始学习会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async endStudySession(sessionId, endTime = null, notes = '') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'endStudySession',
          data: { sessionId, endTime, notes }
        }
      })
      return result.result
    } catch (error) {
      console.error('结束学习会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getStudyStats(dateRange = null, groupBy = 'day') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'getStudyStats',
          data: { dateRange, groupBy }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取学习统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addPomodoroSession(pomodoroData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'addPomodoroSession',
          data: pomodoroData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加番茄钟会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getPomodoroStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'getPomodoroStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取番茄钟统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 用户登录
  static async login() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'login'
      })
      return result.result
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = CloudApi
