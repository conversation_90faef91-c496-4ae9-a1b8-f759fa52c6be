// pages/home/<USER>
Page({
  data: {
    greetingTime: '',
    greetingMessage: '',
    currentDate: '',
    lunarDate: '',
    weather: null,
    nearestExam: null,
    countdown: [],
    todayTasks: [],
    completedTasks: 0,
    totalTasks: 0,
    todayStats: [],
    quickActions: [],
    recentActivities: [],
    fabExpanded: false,
    fabMenuItems: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 初始化页面
  initPage() {
    this.setGreeting()
    this.setDateInfo()
    this.initQuickActions()
    this.initFabMenu()
    this.loadData()
  },

  // 设置问候语
  setGreeting() {
    const hour = new Date().getHours()
    let greetingTime, greetingMessage

    if (hour < 6) {
      greetingTime = '深夜好'
      greetingMessage = '夜深了，注意休息哦'
    } else if (hour < 12) {
      greetingTime = '早上好'
      greetingMessage = '新的一天，加油学习！'
    } else if (hour < 18) {
      greetingTime = '下午好'
      greetingMessage = '继续保持学习状态'
    } else {
      greetingTime = '晚上好'
      greetingMessage = '今天学习得怎么样？'
    }

    this.setData({
      greetingTime,
      greetingMessage
    })
  },

  // 设置日期信息
  setDateInfo() {
    const now = new Date()
    const currentDate = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })

    this.setData({
      currentDate,
      lunarDate: '农历十一月初八' // 这里可以接入农历API
    })
  },

  // 初始化快捷操作
  initQuickActions() {
    const quickActions = [
      {
        id: 'add_task',
        label: '添加任务',
        icon: '📝',
        bgColor: '#E6F7FF',
        action: 'addTask'
      },
      {
        id: 'add_exam',
        label: '添加考试',
        icon: '📅',
        bgColor: '#F6FFED',
        action: 'addExam'
      },
      {
        id: 'start_pomodoro',
        label: '开始专注',
        icon: '🍅',
        bgColor: '#FFF2E8',
        action: 'startPomodoro'
      },
      {
        id: 'view_stats',
        label: '查看统计',
        icon: '📊',
        bgColor: '#F9F0FF',
        action: 'viewStats'
      }
    ]

    this.setData({ quickActions })
  },

  // 初始化悬浮按钮菜单
  initFabMenu() {
    const fabMenuItems = [
      {
        id: 'add_task',
        label: '添加任务',
        icon: '📝',
        color: '#52C41A',
        action: 'addTask'
      },
      {
        id: 'add_exam',
        label: '添加考试',
        icon: '📅',
        color: '#1890FF',
        action: 'addExam'
      },
      {
        id: 'start_pomodoro',
        label: '开始专注',
        icon: '🍅',
        color: '#FA8C16',
        action: 'startPomodoro'
      }
    ]

    this.setData({ fabMenuItems })
  },

  // 加载数据
  loadData() {
    this.loadNearestExam()
    this.loadTodayTasks()
    this.loadTodayStats()
    this.loadRecentActivities()
  },

  // 刷新数据
  refreshData() {
    this.setGreeting()
    this.setDateInfo()
    this.loadData()
  },

  // 加载最近考试
  loadNearestExam() {
    // 模拟数据，实际应该从存储或API获取
    const nearestExam = {
      id: 'exam_001',
      name: '2025年考研',
      date: '2025-12-23',
      status: '备考中',
      preparationProgress: 65,
      daysLeft: 178
    }

    // 计算倒计时
    const countdown = this.calculateCountdown(nearestExam.date)

    this.setData({
      nearestExam,
      countdown
    })
  },

  // 计算倒计时
  calculateCountdown(targetDate) {
    const now = new Date()
    const target = new Date(targetDate)
    const diff = target - now

    if (diff <= 0) {
      return []
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    return [
      { value: days, unit: '天' },
      { value: hours, unit: '时' },
      { value: minutes, unit: '分' }
    ]
  },

  // 加载今日任务
  loadTodayTasks() {
    // 模拟数据
    const todayTasks = [
      {
        id: 'task_001',
        title: '数学高数第一章复习',
        subject: '数学',
        priority: 'high',
        priorityText: '高优先级',
        estimatedTime: '2小时',
        completed: false,
        statusText: '进行中'
      },
      {
        id: 'task_002',
        title: '英语单词背诵',
        subject: '英语',
        priority: 'medium',
        priorityText: '中优先级',
        estimatedTime: '30分钟',
        completed: true,
        statusText: '已完成'
      },
      {
        id: 'task_003',
        title: '政治马原理论学习',
        subject: '政治',
        priority: 'low',
        priorityText: '低优先级',
        estimatedTime: '1小时',
        completed: false,
        statusText: '待开始'
      }
    ]

    const completedTasks = todayTasks.filter(task => task.completed).length
    const totalTasks = todayTasks.length

    this.setData({
      todayTasks,
      completedTasks,
      totalTasks
    })
  },

  // 加载今日统计
  loadTodayStats() {
    const todayStats = [
      {
        label: '学习时长',
        value: '3.5h',
        icon: '⏰',
        color: '#1890FF'
      },
      {
        label: '完成任务',
        value: '5个',
        icon: '✅',
        color: '#52C41A'
      },
      {
        label: '专注次数',
        value: '8次',
        icon: '🍅',
        color: '#FA8C16'
      },
      {
        label: '学习效率',
        value: '92%',
        icon: '📈',
        color: '#722ED1'
      }
    ]

    this.setData({ todayStats })
  },

  // 加载最近活动
  loadRecentActivities() {
    const recentActivities = [
      {
        id: 'activity_001',
        title: '完成数学练习题',
        time: '2小时前',
        icon: '📝',
        status: 'success',
        statusText: '已完成'
      },
      {
        id: 'activity_002',
        title: '25分钟专注学习',
        time: '3小时前',
        icon: '🍅',
        status: 'success',
        statusText: '已完成'
      },
      {
        id: 'activity_003',
        title: '英语单词测试',
        time: '昨天',
        icon: '📖',
        status: 'warning',
        statusText: '需复习'
      }
    ]

    this.setData({ recentActivities })
  },

  // 切换任务状态
  toggleTask(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.todayTasks.map(task => {
      if (task.id === taskId) {
        task.completed = !task.completed
        task.statusText = task.completed ? '已完成' : '进行中'
      }
      return task
    })

    const completedTasks = tasks.filter(task => task.completed).length

    this.setData({
      todayTasks: tasks,
      completedTasks
    })

    // 显示反馈
    wx.showToast({
      title: tasks.find(t => t.id === taskId).completed ? '任务完成！' : '任务重新开始',
      icon: 'success'
    })
  },

  // 切换悬浮按钮
  toggleFab() {
    this.setData({
      fabExpanded: !this.data.fabExpanded
    })
  },

  // 执行快捷操作
  executeQuickAction(e) {
    const action = e.currentTarget.dataset.action
    this.handleAction(action)
  },

  // 执行悬浮按钮操作
  executeFabAction(e) {
    const action = e.currentTarget.dataset.action
    this.setData({ fabExpanded: false })
    this.handleAction(action)
  },

  // 处理操作
  handleAction(action) {
    switch (action) {
      case 'addTask':
        this.addTask()
        break
      case 'addExam':
        this.addExam()
        break
      case 'startPomodoro':
        this.startPomodoro()
        break
      case 'viewStats':
        this.viewDataCenter()
        break
      default:
        console.log('未知操作:', action)
    }
  },

  // 页面跳转方法
  viewExamDetail() {
    wx.navigateTo({
      url: '/pages/exam-detail/index?id=' + this.data.nearestExam.id
    })
  },

  startStudy() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewAllTasks() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/task-detail/index?id=' + taskId
    })
  },

  addTask() {
    wx.navigateTo({
      url: '/pages/add-task/index'
    })
  },

  addExam() {
    wx.navigateTo({
      url: '/pages/add-exam/index'
    })
  },

  startPomodoro() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  viewDataCenter() {
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  }
})
