{
  "pageInfo": {
    "pageName": "通知中心",
    "pageId": "notificationCenter",
    "pageType": "page",
    "description": "通知和提醒管理页面",
    "version": "1.0.0",
    "lastModified": "2025-06-27"
  },
  "layout": {
    "type": "scroll-view",
    "direction": "vertical",
    "backgroundColor": "#F5F5F5",
    "padding": "20rpx"
  },
  "components": [
    {
      "id": "header",
      "type": "view",
      "className": "header-container",
      "style": {
        "backgroundColor": "#FFFFFF",
        "borderRadius": "16rpx",
        "padding": "32rpx",
        "marginBottom": "24rpx",
        "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
      },
      "children": [
        {
          "id": "header-top",
          "type": "view",
          "style": {
            "display": "flex",
            "justifyContent": "space-between",
            "alignItems": "center",
            "marginBottom": "16rpx"
          },
          "children": [
            {
              "id": "page-title",
              "type": "text",
              "content": "通知中心",
              "style": {
                "fontSize": "36rpx",
                "fontWeight": "600",
                "color": "#333333"
              }
            },
            {
              "id": "header-actions",
              "type": "view",
              "style": {
                "display": "flex",
                "gap": "12rpx"
              },
              "children": [
                {
                  "id": "mark-all-read-btn",
                  "type": "button",
                  "condition": "{{unreadCount > 0}}",
                  "content": "全部已读",
                  "style": {
                    "fontSize": "24rpx",
                    "color": "#1890FF",
                    "backgroundColor": "transparent",
                    "border": "1rpx solid #1890FF",
                    "borderRadius": "6rpx",
                    "padding": "6rpx 12rpx"
                  },
                  "events": {
                    "tap": "markAllAsRead"
                  }
                },
                {
                  "id": "settings-btn",
                  "type": "button",
                  "content": "⚙️",
                  "style": {
                    "fontSize": "24rpx",
                    "backgroundColor": "#F6F6F6",
                    "color": "#666666",
                    "border": "1rpx solid #D9D9D9",
                    "borderRadius": "6rpx",
                    "padding": "8rpx",
                    "width": "48rpx",
                    "height": "48rpx"
                  },
                  "events": {
                    "tap": "openNotificationSettings"
                  }
                }
              ]
            }
          ]
        },
        {
          "id": "notification-stats",
          "type": "view",
          "style": {
            "display": "flex",
            "alignItems": "center",
            "gap": "16rpx"
          },
          "children": [
            {
              "id": "unread-count",
              "type": "text",
              "content": "{{unreadCount}} 条未读",
              "style": {
                "fontSize": "26rpx",
                "color": "#666666"
              }
            },
            {
              "id": "total-count",
              "type": "text",
              "content": "共 {{totalCount}} 条通知",
              "style": {
                "fontSize": "26rpx",
                "color": "#999999"
              }
            }
          ]
        }
      ]
    },
    {
      "id": "notification-filters",
      "type": "view",
      "className": "filters-container",
      "style": {
        "backgroundColor": "#FFFFFF",
        "borderRadius": "16rpx",
        "padding": "24rpx 32rpx",
        "marginBottom": "24rpx",
        "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
      },
      "children": [
        {
          "id": "filter-tabs",
          "type": "view",
          "style": {
            "display": "flex",
            "gap": "8rpx"
          },
          "children": [
            {
              "id": "filter-tab",
              "type": "button",
              "forEach": "{{notificationFilters}}",
              "forItem": "filter",
              "forIndex": "index",
              "content": "{{filter.label}}",
              "style": {
                "fontSize": "26rpx",
                "padding": "8rpx 16rpx",
                "borderRadius": "8rpx",
                "border": "none",
                "backgroundColor": "{{filter.active ? '#1890FF' : '#F8F9FA'}}",
                "color": "{{filter.active ? '#FFFFFF' : '#666666'}}"
              },
              "events": {
                "tap": "switchFilter"
              }
            }
          ]
        }
      ]
    },
    {
      "id": "notification-list",
      "type": "view",
      "condition": "{{filteredNotifications.length > 0}}",
      "className": "notifications-container",
      "children": [
        {
          "id": "notification-group",
          "type": "view",
          "forEach": "{{notificationGroups}}",
          "forItem": "group",
          "forIndex": "groupIndex",
          "style": {
            "marginBottom": "24rpx"
          },
          "children": [
            {
              "id": "group-header",
              "type": "view",
              "style": {
                "marginBottom": "12rpx"
              },
              "children": [
                {
                  "id": "group-date",
                  "type": "text",
                  "content": "{{group.date}}",
                  "style": {
                    "fontSize": "24rpx",
                    "color": "#999999",
                    "fontWeight": "500"
                  }
                }
              ]
            },
            {
              "id": "group-notifications",
              "type": "view",
              "style": {
                "backgroundColor": "#FFFFFF",
                "borderRadius": "16rpx",
                "overflow": "hidden",
                "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
              },
              "children": [
                {
                  "id": "notification-item",
                  "type": "view",
                  "forEach": "{{group.notifications}}",
                  "forItem": "notification",
                  "forIndex": "index",
                  "style": {
                    "padding": "24rpx 32rpx",
                    "borderBottom": "{{index < group.notifications.length - 1 ? '1rpx solid #F0F0F0' : 'none'}}",
                    "backgroundColor": "{{notification.read ? '#FFFFFF' : '#F6FFED'}}",
                    "position": "relative"
                  },
                  "events": {
                    "tap": "openNotification",
                    "longpress": "showNotificationActions"
                  },
                  "children": [
                    {
                      "id": "notification-content",
                      "type": "view",
                      "style": {
                        "display": "flex",
                        "alignItems": "flex-start",
                        "gap": "16rpx"
                      },
                      "children": [
                        {
                          "id": "notification-icon",
                          "type": "view",
                          "style": {
                            "width": "48rpx",
                            "height": "48rpx",
                            "borderRadius": "50%",
                            "backgroundColor": "{{notification.iconBg}}",
                            "display": "flex",
                            "alignItems": "center",
                            "justifyContent": "center",
                            "flexShrink": "0"
                          },
                          "children": [
                            {
                              "type": "text",
                              "content": "{{notification.icon}}",
                              "style": {
                                "fontSize": "24rpx"
                              }
                            }
                          ]
                        },
                        {
                          "id": "notification-body",
                          "type": "view",
                          "style": {
                            "flex": "1"
                          },
                          "children": [
                            {
                              "id": "notification-header",
                              "type": "view",
                              "style": {
                                "display": "flex",
                                "justifyContent": "space-between",
                                "alignItems": "flex-start",
                                "marginBottom": "8rpx"
                              },
                              "children": [
                                {
                                  "id": "notification-title",
                                  "type": "text",
                                  "content": "{{notification.title}}",
                                  "style": {
                                    "fontSize": "28rpx",
                                    "fontWeight": "{{notification.read ? '400' : '500'}}",
                                    "color": "#333333",
                                    "flex": "1",
                                    "marginRight": "12rpx"
                                  }
                                },
                                {
                                  "id": "notification-time",
                                  "type": "text",
                                  "content": "{{notification.time}}",
                                  "style": {
                                    "fontSize": "22rpx",
                                    "color": "#999999",
                                    "flexShrink": "0"
                                  }
                                }
                              ]
                            },
                            {
                              "id": "notification-message",
                              "type": "text",
                              "content": "{{notification.message}}",
                              "style": {
                                "fontSize": "26rpx",
                                "color": "#666666",
                                "lineHeight": "1.4",
                                "marginBottom": "12rpx"
                              }
                            },
                            {
                              "id": "notification-actions",
                              "type": "view",
                              "condition": "{{notification.actions && notification.actions.length > 0}}",
                              "style": {
                                "display": "flex",
                                "gap": "12rpx",
                                "marginTop": "12rpx"
                              },
                              "children": [
                                {
                                  "id": "notification-action",
                                  "type": "button",
                                  "forEach": "{{notification.actions}}",
                                  "forItem": "action",
                                  "forIndex": "actionIndex",
                                  "content": "{{action.label}}",
                                  "style": {
                                    "fontSize": "24rpx",
                                    "padding": "6rpx 12rpx",
                                    "borderRadius": "6rpx",
                                    "border": "1rpx solid {{action.type === 'primary' ? '#1890FF' : '#D9D9D9'}}",
                                    "backgroundColor": "{{action.type === 'primary' ? '#E6F7FF' : '#FFFFFF'}}",
                                    "color": "{{action.type === 'primary' ? '#1890FF' : '#666666'}}"
                                  },
                                  "events": {
                                    "tap": "executeNotificationAction"
                                  }
                                }
                              ]
                            }
                          ]
                        },
                        {
                          "id": "unread-indicator",
                          "type": "view",
                          "condition": "{{!notification.read}}",
                          "style": {
                            "width": "8rpx",
                            "height": "8rpx",
                            "borderRadius": "50%",
                            "backgroundColor": "#1890FF",
                            "flexShrink": "0",
                            "marginTop": "8rpx"
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "id": "empty-notifications",
      "type": "view",
      "condition": "{{filteredNotifications.length === 0}}",
      "className": "empty-container",
      "style": {
        "backgroundColor": "#FFFFFF",
        "borderRadius": "16rpx",
        "padding": "80rpx 40rpx",
        "textAlign": "center",
        "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
      },
      "children": [
        {
          "id": "empty-icon",
          "type": "text",
          "content": "🔔",
          "style": {
            "fontSize": "64rpx",
            "display": "block",
            "marginBottom": "16rpx",
            "opacity": "0.5"
          }
        },
        {
          "id": "empty-title",
          "type": "text",
          "content": "{{currentFilter === 'unread' ? '没有未读通知' : '暂无通知'}}",
          "style": {
            "fontSize": "28rpx",
            "color": "#666666",
            "marginBottom": "8rpx"
          }
        },
        {
          "id": "empty-subtitle",
          "type": "text",
          "content": "{{currentFilter === 'unread' ? '所有通知都已阅读' : '开启通知提醒，不错过重要信息'}}",
          "style": {
            "fontSize": "24rpx",
            "color": "#999999"
          }
        }
      ]
    },
    {
      "id": "load-more",
      "type": "view",
      "condition": "{{hasMore && filteredNotifications.length > 0}}",
      "className": "load-more-container",
      "style": {
        "textAlign": "center",
        "padding": "40rpx",
        "marginTop": "24rpx"
      },
      "children": [
        {
          "id": "load-more-btn",
          "type": "button",
          "content": "加载更多",
          "style": {
            "fontSize": "26rpx",
            "color": "#1890FF",
            "backgroundColor": "transparent",
            "border": "1rpx solid #1890FF",
            "borderRadius": "8rpx",
            "padding": "12rpx 24rpx"
          },
          "events": {
            "tap": "loadMoreNotifications"
          }
        }
      ]
    }
  ],
  "data": {
    "unreadCount": 3,
    "totalCount": 15,
    "currentFilter": "all",
    "hasMore": true,
    "notificationFilters": [
      { "id": "all", "label": "全部", "active": true },
      { "id": "unread", "label": "未读", "active": false },
      { "id": "exam", "label": "考试", "active": false },
      { "id": "task", "label": "任务", "active": false },
      { "id": "system", "label": "系统", "active": false }
    ],
    "filteredNotifications": [],
    "notificationGroups": [
      {
        "date": "今天",
        "notifications": [
          {
            "id": "notif_001",
            "type": "exam_reminder",
            "icon": "📅",
            "iconBg": "#FFF2E8",
            "title": "考试提醒",
            "message": "距离2025年考研还有178天，记得按时复习哦！",
            "time": "09:00",
            "read": false,
            "actions": [
              { "id": "view_exam", "label": "查看考试", "type": "primary" },
              { "id": "dismiss", "label": "忽略", "type": "default" }
            ]
          },
          {
            "id": "notif_002",
            "type": "task_due",
            "icon": "📝",
            "iconBg": "#E6F7FF",
            "title": "任务截止提醒",
            "message": "任务"数学高数第一章复习"即将到期，请及时完成。",
            "time": "14:30",
            "read": false,
            "actions": [
              { "id": "view_task", "label": "查看任务", "type": "primary" }
            ]
          }
        ]
      },
      {
        "date": "昨天",
        "notifications": [
          {
            "id": "notif_003",
            "type": "pomodoro_complete",
            "icon": "🍅",
            "iconBg": "#F6FFED",
            "title": "专注完成",
            "message": "恭喜完成25分钟专注学习，今日已完成3个番茄钟！",
            "time": "20:15",
            "read": true,
            "actions": []
          },
          {
            "id": "notif_004",
            "type": "achievement",
            "icon": "🏆",
            "iconBg": "#FFF7E6",
            "title": "成就解锁",
            "message": "连续学习7天，获得"坚持不懈"徽章！",
            "time": "18:00",
            "read": true,
            "actions": [
              { "id": "view_achievement", "label": "查看成就", "type": "primary" }
            ]
          }
        ]
      },
      {
        "date": "前天",
        "notifications": [
          {
            "id": "notif_005",
            "type": "system",
            "icon": "⚙️",
            "iconBg": "#F9F0FF",
            "title": "系统更新",
            "message": "要考试啦已更新至v1.2.0，新增学习统计功能。",
            "time": "10:00",
            "read": true,
            "actions": [
              { "id": "view_changelog", "label": "查看更新", "type": "primary" }
            ]
          }
        ]
      }
    ]
  },
  "methods": {
    "markAllAsRead": {
      "type": "function",
      "description": "标记全部为已读",
      "implementation": "markAllNotificationsRead"
    },
    "openNotificationSettings": {
      "type": "navigate",
      "url": "/pages/settings/notification"
    },
    "switchFilter": {
      "type": "function",
      "description": "切换筛选器",
      "params": ["filterId"],
      "implementation": "changeNotificationFilter"
    },
    "openNotification": {
      "type": "function",
      "description": "打开通知详情",
      "params": ["notificationId"],
      "implementation": "handleNotificationTap"
    },
    "showNotificationActions": {
      "type": "function",
      "description": "显示通知操作菜单",
      "params": ["notificationId"],
      "implementation": "displayNotificationMenu"
    },
    "executeNotificationAction": {
      "type": "function",
      "description": "执行通知操作",
      "params": ["notificationId", "actionId"],
      "implementation": "handleNotificationAction"
    },
    "loadMoreNotifications": {
      "type": "function",
      "description": "加载更多通知",
      "implementation": "fetchMoreNotifications"
    }
  },
  "lifecycle": {
    "onLoad": ["loadNotifications"],
    "onShow": ["refreshNotifications", "updateUnreadCount"]
  },
  "interactions": {
    "pullToRefresh": {
      "enabled": true,
      "action": "refreshNotifications"
    }
  }
}
