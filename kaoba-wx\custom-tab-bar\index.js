// custom-tab-bar/index.js
Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#1890FF",
    list: [
      {
        pagePath: "/pages/home/<USER>",
        iconText: "🏠",
        text: "首页"
      },
      {
        pagePath: "/pages/exam-center/index",
        iconText: "📅",
        text: "考试"
      },
      {
        pagePath: "/pages/task-center/index",
        iconText: "📝",
        text: "任务"
      },
      {
        pagePath: "/pages/pomodoro/index",
        iconText: "🍅",
        text: "专注"
      },
      {
        pagePath: "/pages/profile/index",
        iconText: "👤",
        text: "我的"
      }
    ]
  },
  
  attached() {
    // 组件初始化时设置当前选中的tab
    this.setSelected()
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      const index = data.index
      
      // 更新选中状态
      this.setData({
        selected: index
      })
      
      // 跳转页面
      wx.switchTab({
        url
      })
    },

    setSelected() {
      // 获取当前页面路径
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentRoute = '/' + currentPage.route
      
      // 找到对应的tab索引
      const index = this.data.list.findIndex(item => item.pagePath === currentRoute)
      
      if (index !== -1) {
        this.setData({
          selected: index
        })
      }
    }
  }
})
