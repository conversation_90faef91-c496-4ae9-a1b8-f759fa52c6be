// 学习管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    switch (action) {
      case 'startStudySession':
        return await startStudySession(wxContext.OPENID, data)
      case 'endStudySession':
        return await endStudySession(wxContext.OPENID, data)
      case 'pauseStudySession':
        return await pauseStudySession(wxContext.OPENID, data)
      case 'resumeStudySession':
        return await resumeStudySession(wxContext.OPENID, data)
      case 'getStudySessions':
        return await getStudySessions(wxContext.OPENID, data)
      case 'getStudyStats':
        return await getStudyStats(wxContext.OPENID, data)
      case 'addPomodoroSession':
        return await addPomodoroSession(wxContext.OPENID, data)
      case 'getPomodoroStats':
        return await getPomodoroStats(wxContext.OPENID, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('学习管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 开始学习会话
async function startStudySession(openid, sessionData) {
  const session = {
    ...sessionData,
    _openid: openid,
    type: 'study_session',
    startTime: new Date(),
    status: 'active',
    pausedDuration: 0,
    createTime: new Date()
  }

  const result = await db.collection('study_sessions').add({ data: session })
  return { success: true, data: { _id: result._id, ...session } }
}

// 结束学习会话
async function endStudySession(openid, { sessionId, endTime, notes }) {
  const updateData = {
    endTime: endTime ? new Date(endTime) : new Date(),
    status: 'completed',
    updateTime: new Date()
  }
  
  if (notes) {
    updateData.notes = notes
  }
  
  const result = await db.collection('study_sessions')
    .where({ _id: sessionId, _openid: openid })
    .update({ data: updateData })
  
  // 获取完整的会话数据计算总时长
  const session = await db.collection('study_sessions').doc(sessionId).get()
  if (session.data) {
    const totalDuration = (updateData.endTime.getTime() - session.data.startTime.getTime()) / 1000 / 60 - (session.data.pausedDuration || 0)
    await db.collection('study_sessions').doc(sessionId).update({
      data: { totalDuration: Math.max(0, totalDuration) }
    })
  }
  
  return { success: true, data: result }
}

// 暂停学习会话
async function pauseStudySession(openid, { sessionId, pauseTime }) {
  const updateData = {
    status: 'paused',
    lastPauseTime: pauseTime ? new Date(pauseTime) : new Date(),
    updateTime: new Date()
  }
  
  const result = await db.collection('study_sessions')
    .where({ _id: sessionId, _openid: openid })
    .update({ data: updateData })
  
  return { success: true, data: result }
}

// 恢复学习会话
async function resumeStudySession(openid, { sessionId, resumeTime }) {
  const session = await db.collection('study_sessions').doc(sessionId).get()
  
  if (!session.data || session.data._openid !== openid) {
    return { success: false, error: '会话不存在或无权限' }
  }
  
  const resume = resumeTime ? new Date(resumeTime) : new Date()
  const pauseDuration = session.data.lastPauseTime ? 
    (resume.getTime() - session.data.lastPauseTime.getTime()) / 1000 / 60 : 0
  
  const updateData = {
    status: 'active',
    pausedDuration: (session.data.pausedDuration || 0) + pauseDuration,
    updateTime: new Date()
  }
  
  const result = await db.collection('study_sessions')
    .doc(sessionId)
    .update({ data: updateData })
  
  return { success: true, data: result }
}

// 获取学习会话列表
async function getStudySessions(openid, params) {
  const { filter, limit = 20, skip = 0 } = params || {}
  
  let query = db.collection('study_sessions').where({
    _openid: openid
  })
  
  if (filter) {
    if (filter.subject) {
      query = query.where({ subject: filter.subject })
    }
    if (filter.taskId) {
      query = query.where({ taskId: filter.taskId })
    }
    if (filter.dateRange) {
      const { start, end } = filter.dateRange
      query = query.where({
        startTime: _.gte(new Date(start)).and(_.lte(new Date(end)))
      })
    }
    if (filter.status) {
      query = query.where({ status: filter.status })
    }
  }
  
  const result = await query
    .orderBy('startTime', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return { success: true, data: result.data, total: result.data.length }
}

// 获取学习统计
async function getStudyStats(openid, params) {
  const { dateRange, groupBy = 'day' } = params || {}
  
  let query = db.collection('study_sessions').where({
    _openid: openid,
    status: 'completed'
  })
  
  if (dateRange) {
    const { start, end } = dateRange
    query = query.where({
      startTime: _.gte(new Date(start)).and(_.lte(new Date(end)))
    })
  }
  
  const sessions = await query.get()
  const data = sessions.data
  
  const stats = {
    totalSessions: data.length,
    totalDuration: data.reduce((sum, s) => sum + (s.totalDuration || 0), 0),
    averageDuration: data.length > 0 ? data.reduce((sum, s) => sum + (s.totalDuration || 0), 0) / data.length : 0,
    bySubject: {},
    byDate: {}
  }
  
  // 按科目统计
  data.forEach(session => {
    if (session.subject) {
      if (!stats.bySubject[session.subject]) {
        stats.bySubject[session.subject] = { count: 0, duration: 0 }
      }
      stats.bySubject[session.subject].count++
      stats.bySubject[session.subject].duration += session.totalDuration || 0
    }
  })
  
  // 按日期统计
  data.forEach(session => {
    const date = new Date(session.startTime).toISOString().split('T')[0]
    if (!stats.byDate[date]) {
      stats.byDate[date] = { count: 0, duration: 0 }
    }
    stats.byDate[date].count++
    stats.byDate[date].duration += session.totalDuration || 0
  })
  
  return { success: true, data: stats }
}

// 添加番茄钟会话
async function addPomodoroSession(openid, pomodoroData) {
  const pomodoro = {
    ...pomodoroData,
    _openid: openid,
    type: 'pomodoro_session',
    createTime: new Date()
  }

  const result = await db.collection('pomodoro_sessions').add({ data: pomodoro })
  return { success: true, data: { _id: result._id, ...pomodoro } }
}

// 获取番茄钟统计
async function getPomodoroStats(openid, params) {
  const { dateRange } = params || {}
  
  let query = db.collection('pomodoro_sessions').where({
    _openid: openid
  })
  
  if (dateRange) {
    const { start, end } = dateRange
    query = query.where({
      createTime: _.gte(new Date(start)).and(_.lte(new Date(end)))
    })
  }
  
  const sessions = await query.get()
  const data = sessions.data
  
  const stats = {
    totalSessions: data.length,
    completedSessions: data.filter(s => s.completed).length,
    totalFocusTime: data.filter(s => s.completed).reduce((sum, s) => sum + (s.duration || 25), 0),
    averageSessionsPerDay: 0,
    bySubject: {}
  }
  
  // 按科目统计
  data.forEach(session => {
    if (session.subject) {
      if (!stats.bySubject[session.subject]) {
        stats.bySubject[session.subject] = { count: 0, completed: 0 }
      }
      stats.bySubject[session.subject].count++
      if (session.completed) {
        stats.bySubject[session.subject].completed++
      }
    }
  })
  
  return { success: true, data: stats }
}
