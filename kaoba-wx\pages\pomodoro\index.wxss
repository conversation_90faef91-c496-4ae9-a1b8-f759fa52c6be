/* pages/pomodoro/index.wxss - Enhanced Version */

/* 容器布局 */
.container {
  background-color: #F5F5F5;
  min-height: 100vh;
  padding: 20rpx;
}

.container.focus-mode {
  background-color: #1A1A1A;
  color: #FFFFFF;
  padding: 0;
}

.normal-layout {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.focus-layout {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #1A1A1A;
  color: #FFFFFF;
  z-index: 9999;
}

/* 通用容器样式 */
.mode-selector-container,
.task-association-container,
.sound-settings-container,
.timer-container,
.quick-actions-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

/* 学习模式选择 */
.mode-options {
  display: flex;
  gap: 16rpx;
}

.mode-option {
  flex: 1;
  background-color: #F8F9FA;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.mode-option.active {
  background-color: #E6F7FF;
  color: #1890FF;
  border-color: #1890FF;
}

/* 任务关联 */
.selected-task-display {
  background-color: #F6FFED;
  border: 1rpx solid #B7EB8F;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-details {
  flex: 1;
}

.task-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
  display: block;
}

.task-progress {
  font-size: 22rpx;
  color: #52C41A;
  display: block;
}

.change-task-btn {
  background-color: transparent;
  color: #1890FF;
  border: 1rpx solid #1890FF;
  border-radius: 4rpx;
  padding: 4rpx 12rpx;
  font-size: 22rpx;
}

.task-breakdown {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-top: 16rpx;
}

.breakdown-title {
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.breakdown-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.breakdown-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.pomodoro-icon {
  font-size: 20rpx;
}

.breakdown-text {
  font-size: 22rpx;
  color: #333333;
}

.breakdown-text.completed {
  color: #999999;
  text-decoration: line-through;
}

.select-task-btn {
  width: 100%;
  background-color: #F8F9FA;
  color: #666666;
  border: 1rpx dashed #D9D9D9;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 26rpx;
}

/* 声音设置 */
.background-sound {
  margin-bottom: 20rpx;
}

.setting-label {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
  display: block;
}

.sound-options {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.sound-option {
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 16rpx;
  padding: 6rpx 12rpx;
  font-size: 22rpx;
  transition: all 0.3s ease;
}

.sound-option.active {
  background-color: #E6F7FF;
  color: #1890FF;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.volume-slider {
  flex: 1;
}

.volume-value {
  font-size: 22rpx;
  color: #666666;
  min-width: 60rpx;
}

/* 番茄钟主体 */
.timer-container {
  text-align: center;
  min-height: 600rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.current-session {
  margin-bottom: 40rpx;
}

.session-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.session-subtitle {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

.timer-display {
  margin-bottom: 60rpx;
}

.timer-circle {
  width: 320rpx;
  height: 320rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.timer-circle.breathing {
  animation: breathe 2s ease-in-out infinite;
}

.timer-inner {
  width: 280rpx;
  height: 280rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.time-text {
  font-size: 64rpx;
  font-weight: 700;
  color: #333333;
  font-family: monospace;
  margin-bottom: 8rpx;
}

.session-type {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.timer-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32rpx;
}

.control-btn {
  font-size: 40rpx;
  color: #FFFFFF;
  border-radius: 50%;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.start-btn {
  width: 120rpx;
  height: 120rpx;
  background-color: #52C41A;
}

.start-btn.pause {
  background-color: #FA8C16;
}

.stop-btn {
  width: 80rpx;
  height: 80rpx;
  font-size: 32rpx;
  background-color: #F5F5F5;
  color: #666666;
}

/* 快捷操作 */
.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.focus-mode-btn {
  background-color: #F6FFED;
  color: #52C41A;
}

.sound-btn {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.stats-btn {
  background-color: #E6F7FF;
  color: #1890FF;
}

.task-btn {
  background-color: #F9F0FF;
  color: #722ED1;
}

.action-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 专注模式 */
.focus-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: rgba(26,26,26,0.9);
}

.exit-focus-btn {
  font-size: 36rpx;
  color: #FFFFFF;
  background-color: transparent;
  border: none;
  padding: 8rpx;
}

.focus-title {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.focus-placeholder {
  width: 52rpx;
}

.focus-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 40rpx 40rpx;
  text-align: center;
}

.focus-timer-display {
  margin-bottom: 80rpx;
}

.focus-timer-circle {
  width: 400rpx;
  height: 400rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  animation: breathe 3s ease-in-out infinite;
}

.focus-timer-inner {
  width: 340rpx;
  height: 340rpx;
  background-color: #1A1A1A;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.focus-time-text {
  font-size: 80rpx;
  font-weight: 300;
  color: #FFFFFF;
  font-family: monospace;
  margin-bottom: 16rpx;
  letter-spacing: 4rpx;
}

.focus-session-indicator {
  font-size: 24rpx;
  color: rgba(255,255,255,0.6);
  margin-bottom: 8rpx;
}

.focus-task-info {
  margin-bottom: 80rpx;
  text-align: center;
}

.focus-task-title {
  font-size: 36rpx;
  font-weight: 400;
  color: #FFFFFF;
  margin-bottom: 12rpx;
  display: block;
}

.focus-task-subtitle {
  font-size: 26rpx;
  color: rgba(255,255,255,0.7);
  display: block;
}

.focus-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 60rpx;
}

.focus-pause-btn,
.focus-stop-btn {
  font-size: 48rpx;
  color: rgba(255,255,255,0.8);
  background-color: rgba(255,255,255,0.1);
  border-radius: 50%;
  border: 2rpx solid rgba(255,255,255,0.2);
}

.focus-pause-btn {
  width: 100rpx;
  height: 100rpx;
}

.focus-stop-btn {
  width: 80rpx;
  height: 80rpx;
  font-size: 40rpx;
  color: rgba(255,255,255,0.6);
  background-color: rgba(255,255,255,0.05);
  border: 2rpx solid rgba(255,255,255,0.1);
}

/* 弹窗样式 */
.task-modal-mask,
.breakdown-modal-mask,
.sound-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.task-modal,
.breakdown-modal,
.sound-modal {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.modal-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 任务选择弹窗 */
.task-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}

.task-option:active {
  background-color: #F8F9FA;
}

.task-option.selected {
  background-color: #E6F7FF;
  border: 1rpx solid #91D5FF;
}

.task-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.task-subject {
  font-size: 22rpx;
  color: #666666;
  display: block;
}

.task-check {
  font-size: 20rpx;
  color: #1890FF;
}

/* 拆解建议弹窗 */
.breakdown-info {
  text-align: center;
  margin-bottom: 24rpx;
}

.breakdown-task-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.breakdown-description {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
  display: block;
}

.breakdown-plan {
  margin-bottom: 32rpx;
}

.breakdown-stage {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 12rpx;
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.stage-icon {
  font-size: 20rpx;
}

.stage-title {
  flex: 1;
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
}

.stage-duration {
  font-size: 22rpx;
  color: #1890FF;
}

.stage-description {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.4;
  margin-left: 28rpx;
  display: block;
}

.breakdown-actions {
  display: flex;
  gap: 16rpx;
}

.breakdown-btn {
  flex: 1;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  border: none;
}

.breakdown-btn.primary {
  background-color: #1890FF;
  color: #FFFFFF;
}

.breakdown-btn.secondary {
  background-color: #F5F5F5;
  color: #666666;
}

/* 声音设置弹窗 */
.sound-section {
  margin-bottom: 32rpx;
}

.sound-section:last-child {
  margin-bottom: 0;
}

.sound-section-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.sound-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.sound-card {
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.sound-card.active {
  background-color: #E6F7FF;
  border-color: #1890FF;
}

.sound-card-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  display: block;
}

.sound-card-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
  display: block;
}

.sound-card-desc {
  font-size: 20rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
}

.sound-preview-btn {
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 4rpx;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
}

.notification-sounds {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
}

.notification-name {
  font-size: 24rpx;
  color: #333333;
}

.volume-controls {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.volume-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.volume-label {
  font-size: 24rpx;
  color: #333333;
  min-width: 120rpx;
}

.volume-text {
  font-size: 22rpx;
  color: #666666;
  min-width: 60rpx;
}

/* 动画效果 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
