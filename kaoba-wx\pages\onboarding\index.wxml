<!--pages/onboarding/index.wxml-->
<view class="container">
  <!-- 引导内容 -->
  <swiper class="onboarding-swiper"
          indicator-dots="{{true}}"
          indicator-color="rgba(255,255,255,0.3)"
          indicator-active-color="#FFFFFF"
          current="{{currentStep}}"
          bindchange="onSwiperChange">

    <!-- 第一步：欢迎 -->
    <swiper-item class="step-item step-welcome">
      <view class="step-content">
        <view class="step-icon">🎓</view>
        <text class="step-title">欢迎使用要考试啦！</text>
        <text class="step-desc">专为学生设计的学习管理工具\n帮您高效管理考试、任务和学习时间</text>
        <view class="step-features">
          <view class="feature-item">
            <text class="feature-icon">📅</text>
            <text class="feature-text">考试管理</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">📝</text>
            <text class="feature-text">任务规划</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">🍅</text>
            <text class="feature-text">专注学习</text>
          </view>
        </view>
      </view>
    </swiper-item>

    <!-- 第二步：考试管理 -->
    <swiper-item class="step-item step-exam">
      <view class="step-content">
        <view class="step-icon">📅</view>
        <text class="step-title">智能考试管理</text>
        <text class="step-desc">轻松管理所有考试信息\n实时倒计时，不错过任何重要考试</text>
        <view class="demo-phone">
          <view class="demo-screen">
            <view class="demo-exam-item">
              <view class="demo-exam-info">
                <text class="demo-exam-name">高等数学期末考试</text>
                <text class="demo-exam-time">2024-07-15 09:00</text>
              </view>
              <view class="demo-countdown">
                <text class="demo-countdown-text">17天</text>
              </view>
            </view>
            <view class="demo-exam-item">
              <view class="demo-exam-info">
                <text class="demo-exam-name">英语四级考试</text>
                <text class="demo-exam-time">2024-07-20 14:30</text>
              </view>
              <view class="demo-countdown">
                <text class="demo-countdown-text">22天</text>
              </view>
            </view>
          </view>
        </view>
        <view class="step-tips">
          <text class="tip-item">• 设置考试提醒，不错过重要时间</text>
          <text class="tip-item">• 重要程度分级，合理安排复习</text>
          <text class="tip-item">• 准备度跟踪，掌握复习进度</text>
        </view>
      </view>
    </swiper-item>

    <!-- 第三步：任务管理 -->
    <swiper-item class="step-item step-task">
      <view class="step-content">
        <view class="step-icon">📝</view>
        <text class="step-title">科学任务规划</text>
        <text class="step-desc">将学习目标分解为可执行的任务\n优先级管理，提升学习效率</text>
        <view class="demo-phone">
          <view class="demo-screen">
            <view class="demo-task-item high">
              <view class="demo-task-priority">🔴</view>
              <view class="demo-task-info">
                <text class="demo-task-name">数学第三章习题</text>
                <text class="demo-task-progress">进度: 60%</text>
              </view>
            </view>
            <view class="demo-task-item medium">
              <view class="demo-task-priority">🟡</view>
              <view class="demo-task-info">
                <text class="demo-task-name">英语单词背诵</text>
                <text class="demo-task-progress">进度: 80%</text>
              </view>
            </view>
            <view class="demo-task-item low">
              <view class="demo-task-priority">🟢</view>
              <view class="demo-task-info">
                <text class="demo-task-name">政治知识点整理</text>
                <text class="demo-task-progress">进度: 30%</text>
              </view>
            </view>
          </view>
        </view>
        <view class="step-tips">
          <text class="tip-item">• 优先级分级，重要任务优先处理</text>
          <text class="tip-item">• 子任务分解，大目标变小步骤</text>
          <text class="tip-item">• 进度可视化，成就感满满</text>
        </view>
      </view>
    </swiper-item>

    <!-- 第四步：番茄钟 -->
    <swiper-item class="step-item step-pomodoro">
      <view class="step-content">
        <view class="step-icon">🍅</view>
        <text class="step-title">专注学习利器</text>
        <text class="step-desc">番茄工作法 + 专注模式\n科学学习，效率翻倍</text>
        <view class="demo-phone">
          <view class="demo-screen">
            <view class="demo-timer">
              <view class="demo-timer-circle">
                <text class="demo-timer-time">25:00</text>
                <text class="demo-timer-status">专注中</text>
              </view>
              <view class="demo-timer-controls">
                <view class="demo-control-btn">⏸️</view>
                <view class="demo-control-btn small">🛑</view>
              </view>
            </view>
            <view class="demo-sound-options">
              <text class="demo-sound-item active">🌧️ 雨声</text>
              <text class="demo-sound-item">🌊 海浪</text>
              <text class="demo-sound-item">☕ 咖啡厅</text>
            </view>
          </view>
        </view>
        <view class="step-tips">
          <text class="tip-item">• 25分钟专注 + 5分钟休息</text>
          <text class="tip-item">• 6种背景音，营造专注环境</text>
          <text class="tip-item">• 全屏专注模式，沉浸式学习</text>
        </view>
      </view>
    </swiper-item>

    <!-- 第五步：数据分析 -->
    <swiper-item class="step-item step-data">
      <view class="step-content">
        <view class="step-icon">📊</view>
        <text class="step-title">学习数据分析</text>
        <text class="step-desc">详细的学习统计和分析\n了解学习习惯，持续改进</text>
        <view class="demo-phone">
          <view class="demo-screen">
            <view class="demo-stats">
              <view class="demo-stat-item">
                <text class="demo-stat-label">今日专注</text>
                <text class="demo-stat-value">3.2h</text>
              </view>
              <view class="demo-stat-item">
                <text class="demo-stat-label">完成任务</text>
                <text class="demo-stat-value">5个</text>
              </view>
              <view class="demo-stat-item">
                <text class="demo-stat-label">学习效率</text>
                <text class="demo-stat-value">92%</text>
              </view>
            </view>
            <view class="demo-chart">
              <view class="demo-chart-bar" style="height: 60%"></view>
              <view class="demo-chart-bar" style="height: 80%"></view>
              <view class="demo-chart-bar" style="height: 45%"></view>
              <view class="demo-chart-bar" style="height: 90%"></view>
              <view class="demo-chart-bar" style="height: 70%"></view>
            </view>
          </view>
        </view>
        <view class="step-tips">
          <text class="tip-item">• 学习时长统计，量化学习成果</text>
          <text class="tip-item">• 效率分析，找到最佳学习时间</text>
          <text class="tip-item">• 数据导出，备份学习记录</text>
        </view>
      </view>
    </swiper-item>

    <!-- 第六步：开始使用 -->
    <swiper-item class="step-item step-start">
      <view class="step-content">
        <view class="step-icon">🚀</view>
        <text class="step-title">开始您的学习之旅！</text>
        <text class="step-desc">一切准备就绪\n让我们开始高效学习吧</text>
        <view class="start-checklist">
          <view class="checklist-item">
            <text class="check-icon">✅</text>
            <text class="check-text">了解核心功能</text>
          </view>
          <view class="checklist-item">
            <text class="check-icon">✅</text>
            <text class="check-text">掌握使用方法</text>
          </view>
          <view class="checklist-item">
            <text class="check-icon">✅</text>
            <text class="check-text">准备开始学习</text>
          </view>
        </view>
        <button class="start-btn" bindtap="startUsingApp">
          立即开始使用
        </button>
        <text class="skip-text" bindtap="skipOnboarding">跳过引导</text>
      </view>
    </swiper-item>

  </swiper>

  <!-- 底部导航 -->
  <view class="bottom-nav">
    <button class="nav-btn prev"
            wx:if="{{currentStep > 0}}"
            bindtap="prevStep">
      上一步
    </button>
    <view class="nav-dots">
      <view class="dot {{index === currentStep ? 'active' : ''}}"
            wx:for="{{6}}"
            wx:key="index"
            bindtap="goToStep"
            data-step="{{index}}">
      </view>
    </view>
    <button class="nav-btn next"
            wx:if="{{currentStep < 5}}"
            bindtap="nextStep">
      下一步
    </button>
  </view>
</view>