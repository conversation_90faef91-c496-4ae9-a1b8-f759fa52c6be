// pages/onboarding/index.js
Page({
  data: {
    currentStep: 0,
    totalSteps: 6
  },

  onLoad(options) {
    // 检查是否已经完成过引导
    const hasCompletedOnboarding = wx.getStorageSync('hasCompletedOnboarding')
    if (hasCompletedOnboarding && !options.force) {
      // 如果已经完成引导且不是强制显示，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      })
      return
    }
  },

  // Swiper 变化事件
  onSwiperChange(e) {
    this.setData({
      currentStep: e.detail.current
    })
  },

  // 导航方法
  prevStep() {
    if (this.data.currentStep > 0) {
      this.setData({
        currentStep: this.data.currentStep - 1
      })
    }
  },

  nextStep() {
    if (this.data.currentStep < this.data.totalSteps - 1) {
      this.setData({
        currentStep: this.data.currentStep + 1
      })
    }
  },

  goToStep(e) {
    const step = e.currentTarget.dataset.step
    this.setData({
      currentStep: step
    })
  },

  // 开始使用应用
  startUsingApp() {
    this.completeOnboarding()
  },

  // 跳过引导
  skipOnboarding() {
    wx.showModal({
      title: '跳过引导',
      content: '确定要跳过新手引导吗？您可以稍后在设置中重新查看。',
      success: (res) => {
        if (res.confirm) {
          this.completeOnboarding()
        }
      }
    })
  },

  // 完成引导
  completeOnboarding() {
    try {
      // 标记已完成引导
      wx.setStorageSync('hasCompletedOnboarding', true)

      // 设置默认配置
      this.setDefaultSettings()

      // 显示欢迎提示
      wx.showToast({
        title: '欢迎使用！',
        icon: 'success',
        duration: 2000
      })

      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 2000)

    } catch (error) {
      console.error('完成引导失败:', error)
      wx.showToast({
        title: '设置失败，请重试',
        icon: 'none'
      })
    }
  },

  // 设置默认配置
  setDefaultSettings() {
    try {
      // 应用设置
      const defaultAppSettings = {
        workDuration: 25,
        shortBreakDuration: 5,
        longBreakDuration: 15,
        autoStartBreak: true,
        examReminder: true,
        taskReminder: true,
        studyReminder: false,
        reminderTime: '20:00',
        soundEnabled: true,
        vibrationEnabled: true,
        volume: 70,
        darkMode: false,
        animationEnabled: true
      }

      // 番茄钟设置
      const defaultPomodoroSettings = {
        workDuration: 25,
        shortBreakDuration: 5,
        longBreakDuration: 15,
        selectedBgSound: 'silent',
        volume: 70,
        bgVolume: 70,
        notificationVolume: 80,
        notificationSounds: [
          { id: 'start', name: '开始提示', enabled: true },
          { id: 'pause', name: '暂停提示', enabled: true },
          { id: 'complete', name: '完成提示', enabled: true },
          { id: 'warning', name: '时间警告', enabled: true }
        ]
      }

      // 用户信息
      const defaultUserInfo = {
        nickname: '学习达人',
        avatar: '🎓',
        level: 1,
        exp: 0,
        totalStudyTime: 0,
        totalPomodoros: 0,
        joinDate: new Date().toISOString()
      }

      // 保存默认设置
      wx.setStorageSync('appSettings', defaultAppSettings)
      wx.setStorageSync('pomodoroSettings', defaultPomodoroSettings)
      wx.setStorageSync('userInfo', defaultUserInfo)

      // 创建示例数据
      this.createSampleData()

    } catch (error) {
      console.error('设置默认配置失败:', error)
    }
  },

  // 创建示例数据
  createSampleData() {
    try {
      // 示例考试
      const sampleExams = [
        {
          id: 'sample_exam_1',
          title: '高等数学期末考试',
          subject: '数学',
          date: this.getDateAfterDays(15),
          time: '09:00',
          location: '教学楼A101',
          importance: 'high',
          type: 'final',
          notes: '重点复习微积分和线性代数部分',
          reminders: ['1day', '3days'],
          createdAt: new Date().toISOString(),
          status: 'upcoming'
        },
        {
          id: 'sample_exam_2',
          title: '英语四级考试',
          subject: '英语',
          date: this.getDateAfterDays(25),
          time: '14:30',
          location: '教学楼B203',
          importance: 'medium',
          type: 'certificate',
          notes: '重点练习听力和阅读理解',
          reminders: ['1week'],
          createdAt: new Date().toISOString(),
          status: 'upcoming'
        }
      ]

      // 示例任务
      const sampleTasks = [
        {
          id: 'sample_task_1',
          title: '数学第三章习题练习',
          subject: '数学',
          description: '完成教材第三章所有习题，重点掌握积分计算方法',
          priority: 'high',
          dueDate: this.getDateAfterDays(7),
          estimatedTime: 120,
          actualTime: 0,
          progress: 0,
          status: 'pending',
          examId: 'sample_exam_1',
          subtasks: [
            { id: 'sub1', title: '理论知识复习', completed: false },
            { id: 'sub2', title: '基础题练习', completed: false },
            { id: 'sub3', title: '难题攻克', completed: false }
          ],
          createdAt: new Date().toISOString()
        },
        {
          id: 'sample_task_2',
          title: '英语单词背诵',
          subject: '英语',
          description: '背诵四级核心词汇500个，每天复习巩固',
          priority: 'medium',
          dueDate: this.getDateAfterDays(20),
          estimatedTime: 300,
          actualTime: 0,
          progress: 0,
          status: 'pending',
          examId: 'sample_exam_2',
          subtasks: [
            { id: 'sub1', title: '词汇表A', completed: false },
            { id: 'sub2', title: '词汇表B', completed: false },
            { id: 'sub3', title: '综合复习', completed: false }
          ],
          createdAt: new Date().toISOString()
        }
      ]

      // 保存示例数据
      wx.setStorageSync('exams', sampleExams)
      wx.setStorageSync('tasks', sampleTasks)

    } catch (error) {
      console.error('创建示例数据失败:', error)
    }
  },

  // 获取指定天数后的日期
  getDateAfterDays(days) {
    const date = new Date()
    date.setDate(date.getDate() + days)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
})