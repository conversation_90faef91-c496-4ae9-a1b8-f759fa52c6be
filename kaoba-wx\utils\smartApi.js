// 智能API选择器 - 自动选择云开发或本地存储
const CloudApi = require('./cloudApi')
const LocalStorageApi = require('./localStorageApi')

class SmartApi {
  // 检查云开发是否可用
  static async isCloudAvailable() {
    try {
      if (!wx.cloud) {
        return false
      }

      // 尝试调用一个简单的云函数来测试连接
      const result = await wx.cloud.callFunction({
        name: 'login'
      })

      return result && result.result
    } catch (error) {
      console.log('云开发不可用，使用本地存储:', error.message)
      return false
    }
  }

  // 获取当前使用的API类型
  static async getApiType() {
    const cloudAvailable = await this.isCloudAvailable()
    return cloudAvailable ? 'cloud' : 'local'
  }

  // 任务管理API
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API获取任务')
      return await CloudApi.getTasks(filter, limit, skip)
    } else {
      console.log('使用本地存储API获取任务')
      return await LocalStorageApi.getTasks(filter, limit, skip)
    }
  }

  static async addTask(taskData) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API添加任务')
      return await CloudApi.addTask(taskData)
    } else {
      console.log('使用本地存储API添加任务')
      return await LocalStorageApi.addTask(taskData)
    }
  }

  static async updateTask(taskId, updates) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API更新任务')
      return await CloudApi.updateTask(taskId, updates)
    } else {
      console.log('使用本地存储API更新任务')
      return await LocalStorageApi.updateTask(taskId, updates)
    }
  }

  static async deleteTask(taskId) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API删除任务')
      return await CloudApi.deleteTask(taskId)
    } else {
      console.log('使用本地存储API删除任务')
      return await LocalStorageApi.deleteTask(taskId)
    }
  }

  static async completeTask(taskId, completed = true) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API完成任务')
      return await CloudApi.completeTask(taskId, completed)
    } else {
      console.log('使用本地存储API完成任务')
      return await LocalStorageApi.completeTask(taskId, completed)
    }
  }

  static async getTaskStats(dateRange = null) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API获取任务统计')
      return await CloudApi.getTaskStats(dateRange)
    } else {
      console.log('使用本地存储API获取任务统计')
      return await LocalStorageApi.getTaskStats(dateRange)
    }
  }

  // 考试管理API
  static async getExams(filter = {}, limit = 20, skip = 0) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API获取考试')
      return await CloudApi.getExams(filter, limit, skip)
    } else {
      console.log('使用本地存储API获取考试')
      return await LocalStorageApi.getExams(filter, limit, skip)
    }
  }

  static async addExam(examData) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API添加考试')
      return await CloudApi.addExam(examData)
    } else {
      console.log('使用本地存储API添加考试')
      // 本地存储版本的addExam方法需要实现
      return { success: false, error: '本地存储暂不支持添加考试' }
    }
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API获取即将到来的考试')
      return await CloudApi.getUpcomingExams(days, limit)
    } else {
      console.log('使用本地存储API获取即将到来的考试')
      return await LocalStorageApi.getUpcomingExams(days, limit)
    }
  }

  static async getExamStats(dateRange = null) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API获取考试统计')
      return await CloudApi.getExamStats(dateRange)
    } else {
      console.log('使用本地存储API获取考试统计')
      // 本地存储版本需要实现
      return { success: true, data: { total: 0, upcoming: 0, past: 0, thisWeek: 0, bySubject: {} } }
    }
  }

  // 学习管理API
  static async getStudyStats(dateRange = null, groupBy = 'day') {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API获取学习统计')
      return await CloudApi.getStudyStats(dateRange, groupBy)
    } else {
      console.log('使用本地存储API获取学习统计')
      return await LocalStorageApi.getStudyStats(dateRange)
    }
  }

  static async getPomodoroStats(dateRange = null) {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发API获取番茄钟统计')
      return await CloudApi.getPomodoroStats(dateRange)
    } else {
      console.log('使用本地存储API获取番茄钟统计')
      return await LocalStorageApi.getPomodoroStats(dateRange)
    }
  }

  // 初始化数据
  static async initData() {
    const apiType = await this.getApiType()
    
    if (apiType === 'cloud') {
      console.log('使用云开发初始化数据')
      const DatabaseInit = require('./dbInit')
      return await DatabaseInit.checkAndInit()
    } else {
      console.log('使用本地存储初始化数据')
      return LocalStorageApi.initSampleData()
    }
  }

  // 获取API状态信息
  static async getApiStatus() {
    const cloudAvailable = await this.isCloudAvailable()
    
    return {
      cloudAvailable,
      currentApi: cloudAvailable ? 'cloud' : 'local',
      message: cloudAvailable ? '云开发可用' : '使用本地存储'
    }
  }
}

module.exports = SmartApi
