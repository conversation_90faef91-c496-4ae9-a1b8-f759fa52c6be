{"pageInfo": {"pageName": "底部导航", "pageId": "tabNavigation", "pageType": "component", "description": "应用底部Tab导航组件", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "view", "position": "fixed", "bottom": "0", "left": "0", "right": "0", "zIndex": "1000"}, "components": [{"id": "tab-bar", "type": "view", "className": "tab-bar-container", "style": {"backgroundColor": "#FFFFFF", "borderTop": "1rpx solid #E9ECEF", "padding": "12rpx 0 24rpx", "display": "flex", "justifyContent": "space-around", "alignItems": "center", "boxShadow": "0 -2rpx 8rpx rgba(0,0,0,0.1)"}, "children": [{"id": "tab-item", "type": "view", "forEach": "{{tabList}}", "forItem": "tab", "forIndex": "index", "className": "tab-item", "style": {"display": "flex", "flexDirection": "column", "alignItems": "center", "justifyContent": "center", "padding": "8rpx 16rpx", "minWidth": "80rpx", "position": "relative"}, "events": {"tap": "switchTab"}, "children": [{"id": "tab-icon-container", "type": "view", "style": {"position": "relative", "marginBottom": "6rpx"}, "children": [{"id": "tab-icon", "type": "text", "content": "{{currentTab === tab.key ? tab.activeIcon : tab.icon}}", "style": {"fontSize": "44rpx", "color": "{{currentTab === tab.key ? '#1890FF' : '#999999'}}", "transition": "color 0.3s ease"}}, {"id": "tab-badge", "type": "view", "condition": "{{tab.badge && tab.badge > 0}}", "style": {"position": "absolute", "top": "-8rpx", "right": "-8rpx", "backgroundColor": "#FF4D4F", "color": "#FFFFFF", "fontSize": "20rpx", "fontWeight": "600", "borderRadius": "50%", "minWidth": "32rpx", "height": "32rpx", "display": "flex", "alignItems": "center", "justifyContent": "center", "padding": "0 6rpx"}, "children": [{"type": "text", "content": "{{tab.badge > 99 ? '99+' : tab.badge}}"}]}, {"id": "tab-dot", "type": "view", "condition": "{{tab.showDot && !tab.badge}}", "style": {"position": "absolute", "top": "-4rpx", "right": "-4rpx", "backgroundColor": "#FF4D4F", "width": "16rpx", "height": "16rpx", "borderRadius": "50%"}}]}, {"id": "tab-text", "type": "text", "content": "{{tab.text}}", "style": {"fontSize": "22rpx", "color": "{{currentTab === tab.key ? '#1890FF' : '#999999'}}", "fontWeight": "{{currentTab === tab.key ? '500' : '400'}}", "transition": "color 0.3s ease"}}, {"id": "tab-active-indicator", "type": "view", "condition": "{{currentTab === tab.key}}", "style": {"position": "absolute", "bottom": "-12rpx", "width": "32rpx", "height": "4rpx", "backgroundColor": "#1890FF", "borderRadius": "2rpx"}}]}]}, {"id": "floating-action-button", "type": "view", "condition": "{{showFloatingButton}}", "className": "fab-container", "style": {"position": "absolute", "bottom": "80rpx", "right": "32rpx", "width": "112rpx", "height": "112rpx", "backgroundColor": "#1890FF", "borderRadius": "50%", "display": "flex", "alignItems": "center", "justifyContent": "center", "boxShadow": "0 8rpx 24rpx rgba(24, 144, 255, 0.3)", "zIndex": "1001"}, "events": {"tap": "triggerFloatingAction"}, "children": [{"id": "fab-icon", "type": "text", "content": "{{floatingButtonIcon}}", "style": {"fontSize": "48rpx", "color": "#FFFFFF"}}]}], "data": {"currentTab": "home", "showFloatingButton": true, "floatingButtonIcon": "🍅", "tabList": [{"key": "home", "text": "首页", "icon": "🏠", "activeIcon": "🏠", "url": "/pages/home/<USER>", "badge": 0, "showDot": false}, {"key": "exam", "text": "考试", "icon": "📚", "activeIcon": "📚", "url": "/pages/exam-center/index", "badge": 0, "showDot": false}, {"key": "task", "text": "任务", "icon": "📝", "activeIcon": "📝", "url": "/pages/task-center/index", "badge": 3, "showDot": false}, {"key": "pomodoro", "text": "专注", "icon": "🍅", "activeIcon": "🍅", "url": "/pages/pomodoro/index", "badge": 0, "showDot": false}, {"key": "data", "text": "数据", "icon": "📊", "activeIcon": "📊", "url": "/pages/data-center/index", "badge": 0, "showDot": true}, {"key": "profile", "text": "我的", "icon": "👤", "activeIcon": "👤", "url": "/pages/profile/index", "badge": 0, "showDot": false}]}, "methods": {"switchTab": {"type": "function", "description": "切换Tab页面", "params": ["tabKey"], "implementation": "navigateToTab"}, "triggerFloatingAction": {"type": "function", "description": "触发悬浮按钮动作", "implementation": "startQuickPomodoro"}, "updateTabBadge": {"type": "function", "description": "更新Tab徽章", "params": ["tabKey", "count"], "implementation": "setTabBadge"}, "showTabDot": {"type": "function", "description": "显示Tab红点", "params": ["tabKey", "show"], "implementation": "setTabDot"}}, "lifecycle": {"onLoad": ["initTabNavigation"], "onShow": ["updateTabStatus", "refreshBadges"]}, "interactions": {"tabSwitchAnimation": {"enabled": true, "duration": 300, "easing": "ease-out"}, "fabPulseAnimation": {"enabled": true, "duration": 2000, "repeat": true}, "hapticFeedback": {"enabled": true, "type": "light", "events": ["tabSwitch", "fabTap"]}}, "styles": {"tabBarHeight": "120rpx", "safeAreaBottom": "env(safe-area-inset-bottom)", "animations": {"tabSwitch": {"transform": "scale(0.95)", "duration": "0.1s", "timingFunction": "ease-out"}, "fabPulse": {"transform": "scale(1.1)", "duration": "1s", "iterationCount": "infinite", "direction": "alternate"}}}, "accessibility": {"tabRole": "tab", "tabListRole": "tablist", "ariaLabels": {"home": "首页导航", "exam": "考试管理", "task": "任务中心", "pomodoro": "番茄钟", "data": "数据统计", "profile": "个人中心"}}}