<!--pages/edit-exam/index.wxml-->
<view class="container">
  <form bindsubmit="updateExam">
    <!-- 考试基本信息 -->
    <view class="form-section">
      <text class="section-title">基本信息</text>

      <view class="form-item">
        <text class="form-label">考试名称 *</text>
        <input class="form-input"
               placeholder="请输入考试名称"
               value="{{examForm.name}}"
               bindinput="updateName"
               maxlength="30"/>
      </view>

      <view class="form-item">
        <text class="form-label">考试类型</text>
        <view class="type-options">
          <view class="type-option {{examForm.type === item.value ? 'active' : ''}}"
                wx:for="{{typeOptions}}"
                wx:key="value"
                bindtap="selectType"
                data-type="{{item.value}}">
            <text class="type-icon">{{item.icon}}</text>
            <text class="type-text">{{item.label}}</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">考试描述</text>
        <textarea class="form-textarea"
                  placeholder="简要描述考试内容和要求..."
                  value="{{examForm.description}}"
                  bindinput="updateDescription"
                  maxlength="200"/>
      </view>
    </view>

    <!-- 考试时间 -->
    <view class="form-section">
      <text class="section-title">考试时间</text>

      <view class="form-item">
        <text class="form-label">考试日期 *</text>
        <picker mode="date"
                value="{{examForm.date}}"
                bindchange="selectDate">
          <view class="picker-item">
            <text>{{examForm.date || '选择考试日期'}}</text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">考试时间</text>
        <view class="time-range">
          <picker mode="time"
                  value="{{examForm.startTime}}"
                  bindchange="selectStartTime">
            <view class="time-picker">
              <text>{{examForm.startTime || '开始时间'}}</text>
              <text class="picker-icon">⏰</text>
            </view>
          </picker>
          <text class="time-separator">至</text>
          <picker mode="time"
                  value="{{examForm.endTime}}"
                  bindchange="selectEndTime">
            <view class="time-picker">
              <text>{{examForm.endTime || '结束时间'}}</text>
              <text class="picker-icon">⏰</text>
            </view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 考试科目 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">考试科目</text>
        <button class="add-subject-btn" bindtap="addSubject">+ 添加</button>
      </view>

      <view class="subjects-list" wx:if="{{examForm.subjects.length > 0}}">
        <view class="subject-item" wx:for="{{examForm.subjects}}" wx:key="index">
          <input class="subject-input"
                 placeholder="科目名称"
                 value="{{item.name}}"
                 bindinput="updateSubjectName"
                 data-index="{{index}}"
                 maxlength="20"/>
          <input class="subject-score"
                 placeholder="满分"
                 type="number"
                 value="{{item.totalScore}}"
                 bindinput="updateSubjectScore"
                 data-index="{{index}}"/>
          <input class="subject-target"
                 placeholder="目标分"
                 type="number"
                 value="{{item.targetScore}}"
                 bindinput="updateSubjectTarget"
                 data-index="{{index}}"/>
          <button class="remove-subject-btn" bindtap="removeSubject" data-index="{{index}}">×</button>
        </view>
      </view>

      <view class="empty-subjects" wx:else>
        <text class="empty-text">暂无科目</text>
      </view>
    </view>

    <!-- 目标设置 -->
    <view class="form-section">
      <text class="section-title">目标设置</text>

      <view class="form-item">
        <text class="form-label">总目标分数</text>
        <input class="form-input"
               placeholder="请输入目标分数"
               type="number"
               value="{{examForm.targetScore}}"
               bindinput="updateTargetScore"/>
      </view>

      <view class="form-item">
        <text class="form-label">重要程度</text>
        <view class="importance-options">
          <view class="importance-option {{examForm.importance === item.value ? 'active' : ''}}"
                wx:for="{{importanceOptions}}"
                wx:key="value"
                bindtap="selectImportance"
                data-importance="{{item.value}}">
            <text class="importance-icon" style="color: {{item.color}}">{{item.icon}}</text>
            <text class="importance-text">{{item.label}}</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="switch-item">
          <text class="switch-label">设为当前考试</text>
          <switch checked="{{examForm.isActive}}" bindchange="toggleActive"/>
        </view>
      </view>
    </view>

    <!-- 考试状态 -->
    <view class="form-section">
      <text class="section-title">考试状态</text>

      <view class="form-item">
        <text class="form-label">当前状态</text>
        <view class="status-options">
          <view class="status-option {{examForm.status === item.value ? 'active' : ''}}"
                wx:for="{{statusOptions}}"
                wx:key="value"
                bindtap="selectStatus"
                data-status="{{item.value}}">
            <text class="status-icon">{{item.icon}}</text>
            <text class="status-text">{{item.label}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提醒设置 -->
    <view class="form-section">
      <text class="section-title">提醒设置</text>

      <view class="form-item">
        <view class="switch-item">
          <text class="switch-label">开启考试提醒</text>
          <switch checked="{{examForm.reminderEnabled}}" bindchange="toggleReminder"/>
        </view>
      </view>

      <view class="form-item" wx:if="{{examForm.reminderEnabled}}">
        <text class="form-label">提醒频率</text>
        <view class="reminder-frequency">
          <view class="frequency-option {{examForm.reminderFrequency === item.value ? 'active' : ''}}"
                wx:for="{{frequencyOptions}}"
                wx:key="value"
                bindtap="selectFrequency"
                data-frequency="{{item.value}}">
            {{item.label}}
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button class="cancel-btn" bindtap="cancelEdit">取消</button>
      <button class="delete-btn" bindtap="deleteExam">删除</button>
      <button class="submit-btn" formType="submit">保存修改</button>
    </view>
  </form>
</view>