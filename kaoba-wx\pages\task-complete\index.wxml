<!--pages/task-complete/index.wxml-->
<view class="container">
  <!-- 完成庆祝 -->
  <view class="celebration-container">
    <view class="celebration-animation">
      <text class="celebration-icon">🎉</text>
      <view class="confetti">
        <text>✨🎊✨</text>
      </view>
    </view>
    
    <text class="celebration-title">任务完成！</text>
    <text class="task-title">{{completedTask.title}}</text>
    <text class="completion-message">恭喜你完成了这个任务！{{completionMessage}}</text>
    
    <view class="task-summary">
      <view class="summary-stats">
        <view class="summary-stat" wx:for="{{taskSummaryStats}}" wx:key="label">
          <text class="stat-value" style="color: {{item.color}}">{{item.value}}</text>
          <text class="stat-label">{{item.label}}</text>
        </view>
      </view>
      <text class="completion-time">完成时间：{{completedTask.completionTime}}</text>
    </view>
    
    <view class="achievement-notification" wx:if="{{newAchievements.length > 0}}">
      <text class="achievement-title">🏆 解锁新成就！</text>
      <view class="achievement-list">
        <view class="achievement-badge" wx:for="{{newAchievements}}" wx:key="id">
          <text class="achievement-icon">{{item.icon}}</text>
          <text class="achievement-name">{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习反思 -->
  <view class="reflection-container">
    <text class="reflection-title">学习反思</text>
    
    <view class="difficulty-rating">
      <text class="difficulty-label">任务难度评价：</text>
      <view class="difficulty-stars">
        <button class="difficulty-star" 
                wx:for="{{difficultyStars}}" 
                wx:key="value" 
                bindtap="setDifficultyRating" 
                data-rating="{{item.value}}">
          {{item.filled ? '⭐' : '☆'}}
        </button>
      </view>
    </view>
    
    <view class="satisfaction-rating">
      <text class="satisfaction-label">完成满意度：</text>
      <view class="satisfaction-options">
        <button class="satisfaction-option {{selectedSatisfaction === item.value ? 'active' : ''}}" 
                wx:for="{{satisfactionOptions}}" 
                wx:key="value" 
                bindtap="setSatisfactionRating" 
                data-satisfaction="{{item.value}}">
          {{item.emoji}}
        </button>
      </view>
    </view>
    
    <view class="reflection-notes">
      <text class="notes-label">学习收获和感想：</text>
      <textarea class="notes-input" 
                placeholder="记录这次学习的收获、遇到的问题和解决方法..." 
                value="{{reflectionNotes}}" 
                bindinput="updateReflectionNotes" 
                maxlength="300"/>
      <text class="notes-counter">{{reflectionNotes.length}}/300</text>
    </view>
  </view>

  <!-- 学习建议 -->
  <view class="suggestions-container">
    <text class="suggestions-title">接下来建议</text>
    
    <view class="suggestions-list">
      <view class="suggestion-item" 
            wx:for="{{suggestions}}" 
            wx:key="id" 
            style="background-color: {{item.bgColor}}; border-color: {{item.borderColor}}" 
            bindtap="executeSuggestion" 
            data-suggestion="{{item.action}}">
        <text class="suggestion-icon">{{item.icon}}</text>
        <view class="suggestion-content">
          <text class="suggestion-title">{{item.title}}</text>
          <text class="suggestion-description">{{item.description}}</text>
        </view>
        <text class="suggestion-arrow">›</text>
      </view>
    </view>
  </view>

  <!-- 今日进度 -->
  <view class="progress-container">
    <text class="progress-title">今日学习进度</text>
    
    <view class="progress-stats">
      <view class="progress-item">
        <text class="progress-icon">✅</text>
        <view class="progress-content">
          <text class="progress-value">{{todayProgress.completedTasks}}</text>
          <text class="progress-label">已完成任务</text>
        </view>
      </view>
      
      <view class="progress-item">
        <text class="progress-icon">⏱️</text>
        <view class="progress-content">
          <text class="progress-value">{{todayProgress.studyTime}}</text>
          <text class="progress-label">学习时长</text>
        </view>
      </view>
      
      <view class="progress-item">
        <text class="progress-icon">🎯</text>
        <view class="progress-content">
          <text class="progress-value">{{todayProgress.goalProgress}}%</text>
          <text class="progress-label">目标完成度</text>
        </view>
      </view>
    </view>
    
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{todayProgress.goalProgress}}%"></view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-container">
    <view class="actions-grid">
      <button class="action-btn continue-btn" bindtap="continueLearning">
        <text class="action-icon">📚</text>
        <text class="action-text">继续学习</text>
      </button>
      
      <button class="action-btn break-btn" bindtap="takeBreak">
        <text class="action-icon">☕</text>
        <text class="action-text">休息一下</text>
      </button>
      
      <button class="action-btn progress-btn" bindtap="viewProgress">
        <text class="action-icon">📊</text>
        <text class="action-text">查看进度</text>
      </button>
      
      <button class="action-btn share-btn" bindtap="shareAchievement">
        <text class="action-icon">📤</text>
        <text class="action-text">分享成果</text>
      </button>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="finish-btn" bindtap="finishAndReturn">完成并返回</button>
  </view>
</view>
