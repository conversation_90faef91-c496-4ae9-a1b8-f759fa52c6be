/* pages/onboarding/index.wxss */

/* 容器 */
.container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* Swiper */
.onboarding-swiper {
  height: calc(100vh - 120rpx);
}

.step-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
}

.step-content {
  text-align: center;
  color: #FFFFFF;
  max-width: 600rpx;
}

/* 步骤图标 */
.step-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  display: block;
  animation: bounce 2s ease-in-out infinite;
}

/* 步骤标题 */
.step-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  display: block;
  line-height: 1.3;
}

/* 步骤描述 */
.step-desc {
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 60rpx;
  display: block;
  opacity: 0.9;
  white-space: pre-line;
}

/* 功能特色 */
.step-features {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.feature-icon {
  font-size: 48rpx;
}

.feature-text {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 演示手机 */
.demo-phone {
  width: 280rpx;
  height: 500rpx;
  background-color: #2C3E50;
  border-radius: 40rpx;
  margin: 0 auto 40rpx auto;
  padding: 40rpx 20rpx;
  box-shadow: 0 20rpx 40rpx rgba(0,0,0,0.3);
}

.demo-screen {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 20rpx;
  overflow: hidden;
}

/* 演示考试项 */
.demo-exam-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  margin-bottom: 12rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  border-left: 4rpx solid #3498DB;
}

.demo-exam-info {
  flex: 1;
}

.demo-exam-name {
  font-size: 20rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.demo-exam-time {
  font-size: 16rpx;
  color: #7F8C8D;
}

.demo-countdown {
  background-color: #E74C3C;
  color: #FFFFFF;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.demo-countdown-text {
  font-size: 16rpx;
  font-weight: 500;
}

/* 演示任务项 */
.demo-task-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  margin-bottom: 12rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
}

.demo-task-item.high {
  border-left: 4rpx solid #E74C3C;
}

.demo-task-item.medium {
  border-left: 4rpx solid #F39C12;
}

.demo-task-item.low {
  border-left: 4rpx solid #27AE60;
}

.demo-task-priority {
  font-size: 16rpx;
}

.demo-task-info {
  flex: 1;
}

.demo-task-name {
  font-size: 20rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.demo-task-progress {
  font-size: 16rpx;
  color: #7F8C8D;
}

/* 演示计时器 */
.demo-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.demo-timer-circle {
  width: 120rpx;
  height: 120rpx;
  border: 4rpx solid #3498DB;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.demo-timer-time {
  font-size: 20rpx;
  color: #2C3E50;
  font-weight: 600;
}

.demo-timer-status {
  font-size: 14rpx;
  color: #7F8C8D;
}

.demo-timer-controls {
  display: flex;
  gap: 12rpx;
}

.demo-control-btn {
  width: 32rpx;
  height: 32rpx;
  background-color: #3498DB;
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
}

.demo-control-btn.small {
  width: 24rpx;
  height: 24rpx;
  background-color: #E74C3C;
  font-size: 12rpx;
}

/* 演示声音选项 */
.demo-sound-options {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.demo-sound-item {
  font-size: 14rpx;
  color: #7F8C8D;
  background-color: #F8F9FA;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  border: 1rpx solid #E9ECEF;
}

.demo-sound-item.active {
  background-color: #E3F2FD;
  color: #2196F3;
  border-color: #2196F3;
}

/* 演示统计 */
.demo-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.demo-stat-item {
  text-align: center;
}

.demo-stat-label {
  font-size: 14rpx;
  color: #7F8C8D;
  display: block;
  margin-bottom: 4rpx;
}

.demo-stat-value {
  font-size: 18rpx;
  color: #2C3E50;
  font-weight: 600;
}

/* 演示图表 */
.demo-chart {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 80rpx;
  padding: 0 20rpx;
}

.demo-chart-bar {
  width: 16rpx;
  background-color: #3498DB;
  border-radius: 2rpx;
  min-height: 20rpx;
}

/* 提示列表 */
.step-tips {
  text-align: left;
}

.tip-item {
  font-size: 24rpx;
  line-height: 1.8;
  opacity: 0.9;
  display: block;
  margin-bottom: 8rpx;
}

/* 开始清单 */
.start-checklist {
  margin-bottom: 60rpx;
}

.checklist-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
  text-align: left;
}

.check-icon {
  font-size: 32rpx;
}

.check-text {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 开始按钮 */
.start-btn {
  background-color: #FFFFFF;
  color: #667eea;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(255,255,255,0.3);
}

.start-btn:active {
  transform: scale(0.98);
}

.skip-text {
  font-size: 24rpx;
  opacity: 0.7;
  text-decoration: underline;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10rpx);
}

.nav-btn {
  background-color: rgba(255,255,255,0.2);
  color: #FFFFFF;
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}

.nav-btn:active {
  background-color: rgba(255,255,255,0.3);
}

.nav-dots {
  display: flex;
  gap: 12rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: rgba(255,255,255,0.3);
  transition: all 0.3s ease;
}

.dot.active {
  background-color: #FFFFFF;
  transform: scale(1.2);
}

/* 动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}
