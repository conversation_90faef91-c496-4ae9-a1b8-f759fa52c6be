/* pages/home/<USER>/

/* API状态指示器 */
.api-status {
  padding: 16rpx 32rpx;
  margin: 16rpx;
  border-radius: 8rpx;
  text-align: center;
}

.api-status-text {
  font-size: 24rpx;
  font-weight: 500;
}

.api-status-text.cloud {
  color: #52c41a;
  background-color: #f6ffed;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  border: 1rpx solid #b7eb8f;
}

.api-status-text.local {
  color: #fa8c16;
  background-color: #fff7e6;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  border: 1rpx solid #ffd591;
}

/* 问候区域 */
.greeting-section {
  margin-bottom: 24rpx;
}

.greeting-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  color: #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.greeting-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.greeting-text {
  flex: 1;
}

.greeting-time {
  font-size: 36rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.greeting-message {
  font-size: 26rpx;
  opacity: 0.9;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.weather-icon {
  font-size: 32rpx;
}

.weather-temp {
  font-size: 28rpx;
  font-weight: 600;
}

.date-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.current-date {
  font-size: 24rpx;
  opacity: 0.8;
}

.lunar-date {
  font-size: 22rpx;
  opacity: 0.7;
}

/* 倒计时区域 */
.countdown-section {
  margin-bottom: 24rpx;
}

.countdown-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.countdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.exam-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.exam-date {
  font-size: 22rpx;
  color: #666666;
}

.countdown-status {
  background-color: #E6F7FF;
  color: #1890FF;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.countdown-display {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.countdown-item {
  text-align: center;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  min-width: 80rpx;
}

.countdown-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #FF4D4F;
  display: block;
  margin-bottom: 4rpx;
}

.countdown-unit {
  font-size: 20rpx;
  color: #666666;
}

.countdown-progress {
  margin-bottom: 24rpx;
}

.progress-bar {
  background-color: #F0F0F0;
  border-radius: 8rpx;
  height: 8rpx;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.progress-fill {
  background-color: #52C41A;
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: #666666;
  text-align: center;
  display: block;
}

.countdown-actions {
  display: flex;
  gap: 16rpx;
}

.countdown-actions .btn {
  flex: 1;
}

/* 任务区域 */
.tasks-section {
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.task-progress {
  font-size: 24rpx;
  color: #1890FF;
  font-weight: 500;
}

.view-all {
  font-size: 24rpx;
  color: #1890FF;
}

.tasks-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.task-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #F0F0F0;
  gap: 16rpx;
}

.task-item:last-child {
  border-bottom: none;
}

.task-checkbox {
  flex-shrink: 0;
}

.checkbox-icon {
  font-size: 32rpx;
}

.task-content {
  flex: 1;
}

.task-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.task-title.completed {
  text-decoration: line-through;
  color: #999999;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.task-subject {
  font-size: 22rpx;
  color: #1890FF;
  background-color: #E6F7FF;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.task-priority {
  font-size: 20rpx;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

.priority-high {
  background-color: #FFF1F0;
  color: #FF4D4F;
}

.priority-medium {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.priority-low {
  background-color: #F6FFED;
  color: #52C41A;
}

.task-time {
  font-size: 22rpx;
  color: #666666;
}

.task-actions {
  flex-shrink: 0;
}

.task-status {
  font-size: 22rpx;
  color: #999999;
}

.empty-tasks {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.empty-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 24rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.stat-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

.stat-icon {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  color: #FFFFFF;
}

/* 快捷操作区域 */
.quick-actions-section {
  margin-bottom: 24rpx;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16rpx;
}

.quick-action-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12rpx;
  font-size: 32rpx;
}

.action-label {
  font-size: 22rpx;
  color: #333333;
}

/* 最近活动区域 */
.recent-section {
  margin-bottom: 24rpx;
}

.recent-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #F0F0F0;
  gap: 16rpx;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #F8F9FA;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  flex-shrink: 0;
}

.recent-content {
  flex: 1;
}

.recent-title {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.recent-time {
  font-size: 22rpx;
  color: #999999;
}

.recent-status {
  flex-shrink: 0;
}

.status-badge {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.status-success {
  background-color: #F6FFED;
  color: #52C41A;
}

.status-warning {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.status-danger {
  background-color: #FFF1F0;
  color: #FF4D4F;
}

/* 悬浮按钮 */
.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  z-index: 999;
}

.fab-menu {
  position: absolute;
  bottom: 100rpx;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  animation: slideUp 0.3s ease-out;
}

.fab-menu-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  animation: fadeIn 0.3s ease-out;
}

.fab-item-label {
  background-color: rgba(0,0,0,0.8);
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

.fab-item-button {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.2);
}

.fab-main-button {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: #1890FF;
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(24,144,255,0.4);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-main-button.expanded {
  transform: rotate(45deg);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
