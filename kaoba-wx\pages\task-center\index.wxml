<!--pages/task-center/index.wxml-->
<view class="container">
  <!-- 顶部统计和筛选 -->
  <view class="header-section">
    <view class="stats-overview">
      <view class="stats-item" wx:for="{{taskStats}}" wx:key="label">
        <text class="stats-value">{{item.value}}</text>
        <text class="stats-label">{{item.label}}</text>
      </view>
    </view>

    <!-- 筛选器 -->
    <view class="filter-bar">
      <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
        <view class="filter-item {{currentFilter === item.value ? 'active' : ''}}"
              wx:for="{{filterOptions}}"
              wx:key="value"
              bindtap="switchFilter"
              data-filter="{{item.value}}">
          <text>{{item.label}}</text>
          <text class="filter-count" wx:if="{{item.count > 0}}">{{item.count}}</text>
        </view>
      </scroll-view>
      <view class="filter-actions">
        <text class="filter-icon" bindtap="showFilterMenu">⚙️</text>
        <text class="search-icon" bindtap="openSearch">🔍</text>
      </view>
    </view>
  </view>

  <!-- 任务列表 -->
  <view class="tasks-section" wx:if="{{filteredTasks.length > 0}}">
    <!-- 按日期分组 -->
    <view class="task-group" wx:for="{{groupedTasks}}" wx:key="date" wx:for-item="group">
      <view class="group-header">
        <text class="group-date">{{group.date}}</text>
        <text class="group-count">{{group.tasks.length}}个任务</text>
      </view>

      <view class="task-list">
        <view class="task-item"
              wx:for="{{group.tasks}}"
              wx:key="id"
              bindtap="viewTaskDetail"
              data-id="{{item.id}}"
              bindlongpress="showTaskActions"
              data-task="{{item}}">

          <!-- 任务状态指示器 -->
          <view class="task-status-indicator status-{{item.status}}"></view>

          <view class="task-content">
            <!-- 任务头部 -->
            <view class="task-header">
              <view class="task-checkbox" bindtap="toggleTask" data-id="{{item.id}}" catchtap="true">
                <text class="checkbox-icon">{{item.completed ? '✅' : '⭕'}}</text>
              </view>
              <view class="task-info">
                <text class="task-title {{item.completed ? 'completed' : ''}}">{{item.title}}</text>
                <view class="task-meta">
                  <text class="task-subject" wx:if="{{item.subject}}">{{item.subject}}</text>
                  <text class="task-exam" wx:if="{{item.examName}}">{{item.examName}}</text>
                  <text class="task-priority priority-{{item.priority}}">{{item.priorityText}}</text>
                </view>
              </view>
              <view class="task-actions">
                <text class="task-status-text status-{{item.status}}">{{item.statusText}}</text>
                <text class="more-icon" bindtap="showTaskActions" data-task="{{item}}" catchtap="true">⋯</text>
              </view>
            </view>

            <!-- 任务详情 -->
            <view class="task-details" wx:if="{{!item.completed}}">
              <!-- 时间信息 -->
              <view class="task-time-info" wx:if="{{item.dueDate || item.estimatedTime}}">
                <view class="time-item" wx:if="{{item.dueDate}}">
                  <text class="time-icon">📅</text>
                  <text class="time-text">{{item.dueDate}}</text>
                </view>
                <view class="time-item" wx:if="{{item.estimatedTime}}">
                  <text class="time-icon">⏱️</text>
                  <text class="time-text">{{item.estimatedTime}}</text>
                </view>
              </view>

              <!-- 进度条 -->
              <view class="task-progress" wx:if="{{item.progress !== undefined}}">
                <view class="progress-header">
                  <text class="progress-label">完成进度</text>
                  <text class="progress-percentage">{{item.progress}}%</text>
                </view>
                <view class="progress-bar">
                  <view class="progress-fill" style="width: {{item.progress}}%"></view>
                </view>
              </view>

              <!-- 子任务 -->
              <view class="subtasks" wx:if="{{item.subtasks && item.subtasks.length > 0}}">
                <view class="subtasks-header">
                  <text class="subtasks-title">子任务 ({{item.completedSubtasks}}/{{item.subtasks.length}})</text>
                  <text class="subtasks-toggle" bindtap="toggleSubtasks" data-id="{{item.id}}" catchtap="true">
                    {{item.showSubtasks ? '收起' : '展开'}}
                  </text>
                </view>
                <view class="subtasks-list" wx:if="{{item.showSubtasks}}">
                  <view class="subtask-item" wx:for="{{item.subtasks}}" wx:key="id" wx:for-item="subtask">
                    <view class="subtask-checkbox" bindtap="toggleSubtask" data-task-id="{{item.id}}" data-subtask-id="{{subtask.id}}" catchtap="true">
                      <text class="checkbox-icon">{{subtask.completed ? '✅' : '⭕'}}</text>
                    </view>
                    <text class="subtask-title {{subtask.completed ? 'completed' : ''}}">{{subtask.title}}</text>
                  </view>
                </view>
              </view>

              <!-- 快捷操作 -->
              <view class="task-quick-actions">
                <button class="quick-action-btn" bindtap="startTask" data-id="{{item.id}}" catchtap="true">
                  <text class="action-icon">▶️</text>
                  <text class="action-text">开始</text>
                </button>
                <button class="quick-action-btn" bindtap="editTask" data-id="{{item.id}}" catchtap="true">
                  <text class="action-icon">✏️</text>
                  <text class="action-text">编辑</text>
                </button>
                <button class="quick-action-btn" bindtap="startPomodoro" data-id="{{item.id}}" catchtap="true">
                  <text class="action-icon">🍅</text>
                  <text class="action-text">专注</text>
                </button>
              </view>
            </view>

            <!-- 已完成任务的简化显示 -->
            <view class="completed-task-info" wx:if="{{item.completed}}">
              <view class="completion-info">
                <text class="completion-time">完成于 {{item.completionTime}}</text>
                <text class="completion-duration" wx:if="{{item.actualTime}}">用时 {{item.actualTime}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <text class="empty-icon">📝</text>
    <text class="empty-title">{{getEmptyTitle()}}</text>
    <text class="empty-message">{{getEmptyMessage()}}</text>
    <button class="btn btn-primary" bindtap="addTask">添加任务</button>
  </view>

  <!-- 添加任务按钮 -->
  <view class="add-task-section" wx:if="{{filteredTasks.length > 0}}">
    <button class="add-task-btn" bindtap="addTask">
      <text class="add-icon">+</text>
      <text class="add-text">添加任务</text>
    </button>
  </view>
</view>

<!-- 任务操作菜单 -->
<view class="action-sheet-mask" wx:if="{{showActionSheet}}" bindtap="hideActionSheet">
  <view class="action-sheet" catchtap="stopPropagation">
    <view class="action-sheet-header">
      <text class="action-sheet-title">{{selectedTask.title}}</text>
      <text class="action-sheet-close" bindtap="hideActionSheet">×</text>
    </view>
    <view class="action-sheet-body">
      <view class="action-item" wx:for="{{taskActions}}" wx:key="id" bindtap="executeTaskAction" data-action="{{item.action}}">
        <text class="action-icon">{{item.icon}}</text>
        <text class="action-text">{{item.text}}</text>
        <text class="action-arrow">›</text>
      </view>
    </view>
  </view>
</view>

<!-- 筛选菜单 -->
<view class="filter-menu-mask" wx:if="{{showFilterMenu}}" bindtap="hideFilterMenu">
  <view class="filter-menu" catchtap="stopPropagation">
    <view class="filter-menu-header">
      <text class="filter-menu-title">筛选和排序</text>
      <text class="filter-menu-close" bindtap="hideFilterMenu">×</text>
    </view>
    <view class="filter-menu-body">
      <!-- 排序选项 -->
      <view class="filter-section">
        <text class="filter-section-title">排序方式</text>
        <view class="filter-options">
          <view class="filter-option {{currentSort === item.value ? 'active' : ''}}"
                wx:for="{{sortOptions}}"
                wx:key="value"
                bindtap="changeSort"
                data-sort="{{item.value}}">
            <text>{{item.label}}</text>
            <text class="check-icon" wx:if="{{currentSort === item.value}}">✓</text>
          </view>
        </view>
      </view>

      <!-- 优先级筛选 -->
      <view class="filter-section">
        <text class="filter-section-title">优先级</text>
        <view class="filter-options">
          <view class="filter-option {{selectedPriorities.includes(item.value) ? 'active' : ''}}"
                wx:for="{{priorityOptions}}"
                wx:key="value"
                bindtap="togglePriority"
                data-priority="{{item.value}}">
            <text>{{item.label}}</text>
            <text class="check-icon" wx:if="{{selectedPriorities.includes(item.value)}}">✓</text>
          </view>
        </view>
      </view>
    </view>
    <view class="filter-menu-footer">
      <button class="filter-reset-btn" bindtap="resetFilters">重置</button>
      <button class="filter-apply-btn" bindtap="applyFilters">应用</button>
    </view>
  </view>
</view>