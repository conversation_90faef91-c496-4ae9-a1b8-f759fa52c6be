<!--pages/settings/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <text class="page-title">⚙️ 设置</text>
    <text class="page-subtitle">个性化配置您的学习体验</text>
  </view>

  <!-- 设置内容 -->
  <view class="settings-content">

    <!-- 学习设置 -->
    <view class="setting-section">
      <text class="section-title">📚 学习设置</text>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">番茄钟时长</text>
          <text class="setting-desc">设置专注学习的时间长度</text>
        </view>
        <view class="setting-control">
          <button class="time-btn" bindtap="adjustTime" data-type="work" data-action="decrease">-</button>
          <text class="time-value">{{settings.workDuration}}分钟</text>
          <button class="time-btn" bindtap="adjustTime" data-type="work" data-action="increase">+</button>
        </view>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">短休息时长</text>
          <text class="setting-desc">番茄钟间隔的短休息时间</text>
        </view>
        <view class="setting-control">
          <button class="time-btn" bindtap="adjustTime" data-type="shortBreak" data-action="decrease">-</button>
          <text class="time-value">{{settings.shortBreakDuration}}分钟</text>
          <button class="time-btn" bindtap="adjustTime" data-type="shortBreak" data-action="increase">+</button>
        </view>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">长休息时长</text>
          <text class="setting-desc">完成4个番茄钟后的长休息</text>
        </view>
        <view class="setting-control">
          <button class="time-btn" bindtap="adjustTime" data-type="longBreak" data-action="decrease">-</button>
          <text class="time-value">{{settings.longBreakDuration}}分钟</text>
          <button class="time-btn" bindtap="adjustTime" data-type="longBreak" data-action="increase">+</button>
        </view>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">自动开始休息</text>
          <text class="setting-desc">专注结束后自动开始休息</text>
        </view>
        <switch checked="{{settings.autoStartBreak}}" bindchange="toggleAutoStartBreak"/>
      </view>
    </view>

    <!-- 提醒设置 -->
    <view class="setting-section">
      <text class="section-title">🔔 提醒设置</text>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">考试提醒</text>
          <text class="setting-desc">开启考试时间提醒通知</text>
        </view>
        <switch checked="{{settings.examReminder}}" bindchange="toggleExamReminder"/>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">任务提醒</text>
          <text class="setting-desc">开启任务截止时间提醒</text>
        </view>
        <switch checked="{{settings.taskReminder}}" bindchange="toggleTaskReminder"/>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">学习提醒</text>
          <text class="setting-desc">每日学习时间提醒</text>
        </view>
        <switch checked="{{settings.studyReminder}}" bindchange="toggleStudyReminder"/>
      </view>

      <view class="setting-item" wx:if="{{settings.studyReminder}}">
        <view class="setting-info">
          <text class="setting-name">提醒时间</text>
          <text class="setting-desc">每日学习提醒的时间</text>
        </view>
        <picker mode="time" value="{{settings.reminderTime}}" bindchange="onReminderTimeChange">
          <view class="time-picker">
            <text>{{settings.reminderTime}}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 声音设置 -->
    <view class="setting-section">
      <text class="section-title">🔊 声音设置</text>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">提示音</text>
          <text class="setting-desc">开启操作提示音效</text>
        </view>
        <switch checked="{{settings.soundEnabled}}" bindchange="toggleSound"/>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">震动反馈</text>
          <text class="setting-desc">开启触觉震动反馈</text>
        </view>
        <switch checked="{{settings.vibrationEnabled}}" bindchange="toggleVibration"/>
      </view>

      <view class="setting-item" wx:if="{{settings.soundEnabled}}">
        <view class="setting-info">
          <text class="setting-name">音量</text>
          <text class="setting-desc">调节提示音音量大小</text>
        </view>
        <view class="volume-control">
          <slider value="{{settings.volume}}" min="0" max="100" bindchange="adjustVolume"/>
          <text class="volume-text">{{settings.volume}}%</text>
        </view>
      </view>
    </view>

    <!-- 显示设置 -->
    <view class="setting-section">
      <text class="section-title">🎨 显示设置</text>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">深色模式</text>
          <text class="setting-desc">使用深色主题界面</text>
        </view>
        <switch checked="{{settings.darkMode}}" bindchange="toggleDarkMode"/>
      </view>

      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">动画效果</text>
          <text class="setting-desc">开启界面动画效果</text>
        </view>
        <switch checked="{{settings.animationEnabled}}" bindchange="toggleAnimation"/>
      </view>
    </view>

    <!-- 数据设置 -->
    <view class="setting-section">
      <text class="section-title">💾 数据设置</text>

      <view class="setting-item" bindtap="exportData">
        <view class="setting-info">
          <text class="setting-name">导出数据</text>
          <text class="setting-desc">导出学习数据到文件</text>
        </view>
        <text class="setting-arrow">></text>
      </view>

      <view class="setting-item" bindtap="clearData">
        <view class="setting-info">
          <text class="setting-name">清除数据</text>
          <text class="setting-desc">清除所有本地数据</text>
        </view>
        <text class="setting-arrow">></text>
      </view>
    </view>

    <!-- 关于设置 -->
    <view class="setting-section">
      <text class="section-title">ℹ️ 关于</text>

      <view class="setting-item" bindtap="showAbout">
        <view class="setting-info">
          <text class="setting-name">关于应用</text>
          <text class="setting-desc">版本信息和开发团队</text>
        </view>
        <text class="setting-arrow">></text>
      </view>

      <view class="setting-item" bindtap="showHelp">
        <view class="setting-info">
          <text class="setting-name">帮助与反馈</text>
          <text class="setting-desc">使用帮助和问题反馈</text>
        </view>
        <text class="setting-arrow">></text>
      </view>

      <view class="setting-item" bindtap="checkUpdate">
        <view class="setting-info">
          <text class="setting-name">检查更新</text>
          <text class="setting-desc">检查应用最新版本</text>
        </view>
        <text class="setting-arrow">></text>
      </view>
    </view>

  </view>
</view>