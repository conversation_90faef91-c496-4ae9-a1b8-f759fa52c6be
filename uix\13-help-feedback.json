{
  "pageInfo": {
    "pageName": "帮助与反馈",
    "pageId": "helpFeedback",
    "pageType": "page",
    "description": "帮助文档和用户反馈页面",
    "version": "1.0.0",
    "lastModified": "2025-06-27"
  },
  "layout": {
    "type": "scroll-view",
    "direction": "vertical",
    "backgroundColor": "#F5F5F5",
    "padding": "20rpx"
  },
  "components": [
    {
      "id": "header",
      "type": "view",
      "className": "header-container",
      "style": {
        "backgroundColor": "#FFFFFF",
        "borderRadius": "16rpx",
        "padding": "32rpx",
        "marginBottom": "24rpx",
        "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
      },
      "children": [
        {
          "id": "page-title",
          "type": "text",
          "content": "帮助与反馈",
          "style": {
            "fontSize": "36rpx",
            "fontWeight": "600",
            "color": "#333333",
            "marginBottom": "8rpx"
          }
        },
        {
          "id": "page-subtitle",
          "type": "text",
          "content": "遇到问题？我们来帮助你",
          "style": {
            "fontSize": "28rpx",
            "color": "#666666"
          }
        }
      ]
    },
    {
      "id": "quick-help",
      "type": "view",
      "className": "quick-help-container",
      "style": {
        "backgroundColor": "#FFFFFF",
        "borderRadius": "16rpx",
        "padding": "32rpx",
        "marginBottom": "24rpx",
        "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
      },
      "children": [
        {
          "id": "quick-help-title",
          "type": "text",
          "content": "快速帮助",
          "style": {
            "fontSize": "32rpx",
            "fontWeight": "600",
            "color": "#333333",
            "marginBottom": "24rpx"
          }
        },
        {
          "id": "help-cards",
          "type": "view",
          "style": {
            "display": "grid",
            "gridTemplateColumns": "1fr 1fr",
            "gap": "16rpx"
          },
          "children": [
            {
              "id": "help-card",
              "type": "button",
              "forEach": "{{quickHelpItems}}",
              "forItem": "item",
              "forIndex": "index",
              "style": {
                "backgroundColor": "#F8F9FA",
                "border": "1rpx solid #E9ECEF",
                "borderRadius": "12rpx",
                "padding": "20rpx",
                "textAlign": "center"
              },
              "events": {
                "tap": "openHelpTopic"
              },
              "children": [
                {
                  "type": "text",
                  "content": "{{item.icon}}",
                  "style": {
                    "fontSize": "32rpx",
                    "display": "block",
                    "marginBottom": "8rpx"
                  }
                },
                {
                  "type": "text",
                  "content": "{{item.title}}",
                  "style": {
                    "fontSize": "26rpx",
                    "color": "#333333",
                    "fontWeight": "500"
                  }
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "id": "faq-section",
      "type": "view",
      "className": "faq-container",
      "style": {
        "backgroundColor": "#FFFFFF",
        "borderRadius": "16rpx",
        "padding": "32rpx",
        "marginBottom": "24rpx",
        "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
      },
      "children": [
        {
          "id": "faq-title",
          "type": "text",
          "content": "常见问题",
          "style": {
            "fontSize": "32rpx",
            "fontWeight": "600",
            "color": "#333333",
            "marginBottom": "24rpx"
          }
        },
        {
          "id": "faq-list",
          "type": "view",
          "children": [
            {
              "id": "faq-item",
              "type": "view",
              "forEach": "{{faqItems}}",
              "forItem": "faq",
              "forIndex": "index",
              "style": {
                "marginBottom": "20rpx",
                "border": "1rpx solid #F0F0F0",
                "borderRadius": "8rpx"
              },
              "children": [
                {
                  "id": "faq-question",
                  "type": "button",
                  "style": {
                    "width": "100%",
                    "textAlign": "left",
                    "backgroundColor": "transparent",
                    "border": "none",
                    "padding": "20rpx",
                    "display": "flex",
                    "justifyContent": "space-between",
                    "alignItems": "center"
                  },
                  "events": {
                    "tap": "toggleFaq"
                  },
                  "children": [
                    {
                      "type": "text",
                      "content": "{{faq.question}}",
                      "style": {
                        "fontSize": "28rpx",
                        "color": "#333333",
                        "fontWeight": "500",
                        "flex": "1"
                      }
                    },
                    {
                      "type": "text",
                      "content": "{{faq.expanded ? '−' : '+'}}",
                      "style": {
                        "fontSize": "24rpx",
                        "color": "#999999",
                        "marginLeft": "16rpx"
                      }
                    }
                  ]
                },
                {
                  "id": "faq-answer",
                  "type": "view",
                  "condition": "{{faq.expanded}}",
                  "style": {
                    "padding": "0 20rpx 20rpx",
                    "borderTop": "1rpx solid #F0F0F0"
                  },
                  "children": [
                    {
                      "type": "text",
                      "content": "{{faq.answer}}",
                      "style": {
                        "fontSize": "26rpx",
                        "color": "#666666",
                        "lineHeight": "1.5"
                      }
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "id": "feedback-section",
      "type": "view",
      "className": "feedback-container",
      "style": {
        "backgroundColor": "#FFFFFF",
        "borderRadius": "16rpx",
        "padding": "32rpx",
        "marginBottom": "24rpx",
        "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
      },
      "children": [
        {
          "id": "feedback-title",
          "type": "text",
          "content": "意见反馈",
          "style": {
            "fontSize": "32rpx",
            "fontWeight": "600",
            "color": "#333333",
            "marginBottom": "16rpx"
          }
        },
        {
          "id": "feedback-subtitle",
          "type": "text",
          "content": "你的建议对我们很重要",
          "style": {
            "fontSize": "26rpx",
            "color": "#666666",
            "marginBottom": "24rpx"
          }
        },
        {
          "id": "feedback-type",
          "type": "view",
          "style": {
            "marginBottom": "24rpx"
          },
          "children": [
            {
              "id": "feedback-type-label",
              "type": "text",
              "content": "反馈类型",
              "style": {
                "fontSize": "28rpx",
                "color": "#333333",
                "marginBottom": "12rpx"
              }
            },
            {
              "id": "feedback-type-options",
              "type": "view",
              "style": {
                "display": "flex",
                "gap": "12rpx",
                "flexWrap": "wrap"
              },
              "children": [
                {
                  "id": "feedback-type-option",
                  "type": "button",
                  "forEach": "{{feedbackTypes}}",
                  "forItem": "type",
                  "forIndex": "index",
                  "content": "{{type.label}}",
                  "style": {
                    "fontSize": "26rpx",
                    "padding": "8rpx 16rpx",
                    "borderRadius": "6rpx",
                    "border": "1rpx solid #D9D9D9",
                    "backgroundColor": "{{feedbackForm.type === type.value ? '#1890FF' : '#FFFFFF'}}",
                    "color": "{{feedbackForm.type === type.value ? '#FFFFFF' : '#333333'}}"
                  },
                  "events": {
                    "tap": "selectFeedbackType"
                  }
                }
              ]
            }
          ]
        },
        {
          "id": "feedback-content",
          "type": "view",
          "style": {
            "marginBottom": "24rpx"
          },
          "children": [
            {
              "id": "feedback-content-label",
              "type": "text",
              "content": "详细描述",
              "style": {
                "fontSize": "28rpx",
                "color": "#333333",
                "marginBottom": "12rpx"
              }
            },
            {
              "id": "feedback-textarea",
              "type": "textarea",
              "placeholder": "请详细描述你遇到的问题或建议...",
              "value": "{{feedbackForm.content}}",
              "maxlength": "500",
              "style": {
                "backgroundColor": "#F8F9FA",
                "borderRadius": "8rpx",
                "padding": "16rpx",
                "fontSize": "28rpx",
                "border": "1rpx solid #E9ECEF",
                "minHeight": "200rpx"
              },
              "events": {
                "input": "updateFeedbackContent"
              }
            }
          ]
        },
        {
          "id": "feedback-contact",
          "type": "view",
          "style": {
            "marginBottom": "24rpx"
          },
          "children": [
            {
              "id": "feedback-contact-label",
              "type": "text",
              "content": "联系方式（可选）",
              "style": {
                "fontSize": "28rpx",
                "color": "#333333",
                "marginBottom": "12rpx"
              }
            },
            {
              "id": "feedback-contact-input",
              "type": "input",
              "placeholder": "请输入你的邮箱或微信号，方便我们回复",
              "value": "{{feedbackForm.contact}}",
              "style": {
                "backgroundColor": "#F8F9FA",
                "borderRadius": "8rpx",
                "padding": "16rpx",
                "fontSize": "28rpx",
                "border": "1rpx solid #E9ECEF"
              },
              "events": {
                "input": "updateFeedbackContact"
              }
            }
          ]
        },
        {
          "id": "feedback-submit",
          "type": "button",
          "content": "提交反馈",
          "disabled": "{{!feedbackForm.content}}",
          "style": {
            "fontSize": "28rpx",
            "color": "#FFFFFF",
            "backgroundColor": "{{feedbackForm.content ? '#1890FF' : '#D9D9D9'}}",
            "borderRadius": "8rpx",
            "padding": "16rpx",
            "border": "none",
            "width": "100%"
          },
          "events": {
            "tap": "submitFeedback"
          }
        }
      ]
    },
    {
      "id": "contact-section",
      "type": "view",
      "className": "contact-container",
      "style": {
        "backgroundColor": "#FFFFFF",
        "borderRadius": "16rpx",
        "padding": "32rpx",
        "marginBottom": "24rpx",
        "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"
      },
      "children": [
        {
          "id": "contact-title",
          "type": "text",
          "content": "联系我们",
          "style": {
            "fontSize": "32rpx",
            "fontWeight": "600",
            "color": "#333333",
            "marginBottom": "24rpx"
          }
        },
        {
          "id": "contact-methods",
          "type": "view",
          "children": [
            {
              "id": "contact-item",
              "type": "view",
              "forEach": "{{contactMethods}}",
              "forItem": "method",
              "forIndex": "index",
              "style": {
                "display": "flex",
                "alignItems": "center",
                "gap": "16rpx",
                "padding": "16rpx 0",
                "borderBottom": "1rpx solid #F0F0F0"
              },
              "children": [
                {
                  "id": "contact-icon",
                  "type": "text",
                  "content": "{{method.icon}}",
                  "style": {
                    "fontSize": "32rpx"
                  }
                },
                {
                  "id": "contact-info",
                  "type": "view",
                  "style": {
                    "flex": "1"
                  },
                  "children": [
                    {
                      "id": "contact-name",
                      "type": "text",
                      "content": "{{method.name}}",
                      "style": {
                        "fontSize": "28rpx",
                        "color": "#333333",
                        "marginBottom": "4rpx"
                      }
                    },
                    {
                      "id": "contact-value",
                      "type": "text",
                      "content": "{{method.value}}",
                      "style": {
                        "fontSize": "24rpx",
                        "color": "#666666"
                      }
                    }
                  ]
                },
                {
                  "id": "contact-action",
                  "type": "button",
                  "content": "{{method.action}}",
                  "style": {
                    "fontSize": "24rpx",
                    "color": "#1890FF",
                    "backgroundColor": "transparent",
                    "border": "1rpx solid #1890FF",
                    "borderRadius": "6rpx",
                    "padding": "6rpx 12rpx"
                  },
                  "events": {
                    "tap": "contactAction"
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  "data": {
    "quickHelpItems": [
      {
        "id": "getting_started",
        "title": "快速上手",
        "icon": "🚀",
        "url": "/pages/help/getting-started"
      },
      {
        "id": "pomodoro_guide",
        "title": "番茄钟使用",
        "icon": "🍅",
        "url": "/pages/help/pomodoro-guide"
      },
      {
        "id": "task_management",
        "title": "任务管理",
        "icon": "📝",
        "url": "/pages/help/task-management"
      },
      {
        "id": "data_analysis",
        "title": "数据分析",
        "icon": "📊",
        "url": "/pages/help/data-analysis"
      }
    ],
    "faqItems": [
      {
        "id": "faq_1",
        "question": "如何设置考试倒计时？",
        "answer": "在考试中心页面点击"添加考试"，填写考试名称和日期即可。系统会自动计算倒计时并在首页显示。",
        "expanded": false
      },
      {
        "id": "faq_2",
        "question": "番茄钟的时间可以自定义吗？",
        "answer": "可以的。在番茄钟页面或设置中，你可以选择15分钟、25分钟、45分钟或60分钟的专注时长。",
        "expanded": false
      },
      {
        "id": "faq_3",
        "question": "如何查看学习统计数据？",
        "answer": "在数据中心页面可以查看详细的学习统计，包括学习时长、完成任务数、各科目时间分配等。",
        "expanded": false
      },
      {
        "id": "faq_4",
        "question": "数据会丢失吗？",
        "answer": "所有数据都保存在本地，不会上传到服务器。建议定期在设置中导出数据备份。",
        "expanded": false
      },
      {
        "id": "faq_5",
        "question": "如何删除不需要的考试或任务？",
        "answer": "在编辑考试或任务页面，滑动到底部有删除选项。删除后数据无法恢复，请谨慎操作。",
        "expanded": false
      }
    ],
    "feedbackForm": {
      "type": "",
      "content": "",
      "contact": ""
    },
    "feedbackTypes": [
      { "value": "bug", "label": "问题反馈" },
      { "value": "feature", "label": "功能建议" },
      { "value": "improvement", "label": "体验优化" },
      { "value": "other", "label": "其他" }
    ],
    "contactMethods": [
      {
        "id": "email",
        "name": "邮箱",
        "icon": "📧",
        "value": "<EMAIL>",
        "action": "复制"
      },
      {
        "id": "wechat",
        "name": "微信群",
        "icon": "💬",
        "value": "扫码加入用户交流群",
        "action": "查看"
      },
      {
        "id": "qq",
        "name": "QQ群",
        "icon": "🐧",
        "value": "123456789",
        "action": "加入"
      }
    ]
  },
  "methods": {
    "openHelpTopic": {
      "type": "navigate",
      "url": "{{item.url}}",
      "params": ["topicId"]
    },
    "toggleFaq": {
      "type": "function",
      "description": "展开/收起FAQ",
      "params": ["faqId"],
      "implementation": "toggleFaqExpansion"
    },
    "selectFeedbackType": {
      "type": "function",
      "description": "选择反馈类型",
      "params": ["type"],
      "implementation": "setFeedbackType"
    },
    "updateFeedbackContent": {
      "type": "function",
      "description": "更新反馈内容",
      "params": ["content"],
      "implementation": "setFeedbackContent"
    },
    "updateFeedbackContact": {
      "type": "function",
      "description": "更新联系方式",
      "params": ["contact"],
      "implementation": "setFeedbackContact"
    },
    "submitFeedback": {
      "type": "function",
      "description": "提交反馈",
      "implementation": "sendFeedback"
    },
    "contactAction": {
      "type": "function",
      "description": "联系方式操作",
      "params": ["methodId"],
      "implementation": "handleContactAction"
    }
  },
  "lifecycle": {
    "onLoad": ["loadHelpData"],
    "onShow": ["refreshFeedbackForm"]
  }
}
