{"pageInfo": {"pageName": "数据中心", "pageId": "dataCenter", "pageType": "tabPage", "description": "复习数据统计和分析页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "page-title", "type": "text", "content": "数据中心", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "page-subtitle", "type": "text", "content": "了解你的复习效果", "style": {"fontSize": "28rpx", "color": "#666666"}}]}, {"id": "exam-preparation-score", "type": "view", "className": "preparation-score-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "score-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "24rpx"}, "children": [{"id": "score-title", "type": "text", "content": "考试准备度", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333"}}, {"id": "score-info-btn", "type": "button", "content": "ℹ️", "style": {"fontSize": "24rpx", "backgroundColor": "transparent", "border": "none", "color": "#999999"}, "events": {"tap": "showScoreInfo"}}]}, {"id": "score-display", "type": "view", "style": {"textAlign": "center", "marginBottom": "24rpx"}, "children": [{"id": "score-circle", "type": "view", "style": {"width": "200rpx", "height": "200rpx", "borderRadius": "50%", "border": "12rpx solid #F0F0F0", "position": "relative", "margin": "0 auto 20rpx", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "children": [{"id": "score-progress", "type": "view", "style": {"position": "absolute", "top": "-12rpx", "left": "-12rpx", "width": "200rpx", "height": "200rpx", "borderRadius": "50%", "border": "12rpx solid transparent", "borderTopColor": "{{preparationScore >= 80 ? '#52C41A' : preparationScore >= 60 ? '#FAAD14' : '#FF4D4F'}}", "transform": "rotate({{preparationScore * 3.6}}deg)"}}, {"id": "score-text", "type": "text", "content": "{{preparationScore}}%", "style": {"fontSize": "48rpx", "fontWeight": "700", "color": "{{preparationScore >= 80 ? '#52C41A' : preparationScore >= 60 ? '#FAAD14' : '#FF4D4F'}}"}}]}, {"id": "score-level", "type": "text", "content": "{{preparationLevel}}", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "score-description", "type": "text", "content": "{{preparationDescription}}", "style": {"fontSize": "26rpx", "color": "#666666", "lineHeight": "1.4"}}]}, {"id": "score-factors", "type": "view", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "12rpx", "padding": "20rpx"}, "children": [{"id": "factors-title", "type": "text", "content": "影响因素", "style": {"fontSize": "26rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "factors-list", "type": "view", "children": [{"id": "factor-item", "type": "view", "forEach": "{{scoreFactors}}", "forItem": "factor", "forIndex": "index", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "8rpx"}, "children": [{"id": "factor-name", "type": "text", "content": "{{factor.name}}", "style": {"fontSize": "24rpx", "color": "#666666"}}, {"id": "factor-score", "type": "text", "content": "{{factor.score}}%", "style": {"fontSize": "24rpx", "fontWeight": "500", "color": "{{factor.score >= 80 ? '#52C41A' : factor.score >= 60 ? '#FAAD14' : '#FF4D4F'}}"}}]}]}]}]}, {"id": "time-period-selector", "type": "view", "className": "period-selector-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "24rpx 32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "period-tabs", "type": "view", "style": {"display": "flex", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "4rpx"}, "children": [{"id": "period-tab", "type": "button", "forEach": "{{periodTabs}}", "forItem": "tab", "forIndex": "index", "content": "{{tab.name}}", "style": {"flex": "1", "fontSize": "26rpx", "padding": "12rpx", "borderRadius": "6rpx", "border": "none", "backgroundColor": "{{currentPeriod === tab.key ? '#1890FF' : 'transparent'}}", "color": "{{currentPeriod === tab.key ? '#FFFFFF' : '#666666'}}"}, "events": {"tap": "switchPeriod"}}]}]}, {"id": "study-overview", "type": "view", "className": "study-overview-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "overview-title", "type": "text", "content": "复习概览", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "overview-stats", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr", "gap": "20rpx", "marginBottom": "24rpx"}, "children": [{"id": "total-study-time", "type": "view", "style": {"backgroundColor": "#E6F7FF", "borderRadius": "12rpx", "padding": "24rpx", "textAlign": "center"}, "children": [{"type": "text", "content": "{{studyStats.totalTime}}", "style": {"fontSize": "36rpx", "fontWeight": "700", "color": "#1890FF", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "总复习时长", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "completed-tasks", "type": "view", "style": {"backgroundColor": "#F6FFED", "borderRadius": "12rpx", "padding": "24rpx", "textAlign": "center"}, "children": [{"type": "text", "content": "{{studyStats.completedTasks}}", "style": {"fontSize": "36rpx", "fontWeight": "700", "color": "#52C41A", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "完成任务", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "pomodoro-sessions", "type": "view", "style": {"backgroundColor": "#FFF2E8", "borderRadius": "12rpx", "padding": "24rpx", "textAlign": "center"}, "children": [{"type": "text", "content": "{{studyStats.pomodoroSessions}}", "style": {"fontSize": "36rpx", "fontWeight": "700", "color": "#FA8C16", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "番茄钟", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "study-days", "type": "view", "style": {"backgroundColor": "#F9F0FF", "borderRadius": "12rpx", "padding": "24rpx", "textAlign": "center"}, "children": [{"type": "text", "content": "{{studyStats.studyDays}}", "style": {"fontSize": "36rpx", "fontWeight": "700", "color": "#722ED1", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "学习天数", "style": {"fontSize": "24rpx", "color": "#666666"}}]}]}, {"id": "daily-average", "type": "view", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "12rpx", "padding": "20rpx", "textAlign": "center"}, "children": [{"type": "text", "content": "日均复习时长：{{studyStats.dailyAverage}}", "style": {"fontSize": "26rpx", "color": "#333333", "fontWeight": "500"}}]}]}, {"id": "subject-analysis", "type": "view", "className": "subject-analysis-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "subject-title", "type": "text", "content": "科目分析", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "subject-chart", "type": "view", "style": {"height": "300rpx", "backgroundColor": "#F8F9FA", "borderRadius": "12rpx", "marginBottom": "24rpx", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "children": [{"type": "text", "content": "📊 科目时间分布图", "style": {"fontSize": "28rpx", "color": "#999999"}}]}, {"id": "subject-list", "type": "view", "children": [{"id": "subject-item", "type": "view", "forEach": "{{subjectStats}}", "forItem": "subject", "forIndex": "index", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "16rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "subject-info", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "12rpx"}, "children": [{"id": "subject-color", "type": "view", "style": {"width": "16rpx", "height": "16rpx", "borderRadius": "50%", "backgroundColor": "{{subject.color}}"}}, {"id": "subject-name", "type": "text", "content": "{{subject.name}}", "style": {"fontSize": "28rpx", "color": "#333333"}}]}, {"id": "subject-stats", "type": "view", "style": {"textAlign": "right"}, "children": [{"id": "subject-time", "type": "text", "content": "{{subject.time}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "display": "block"}}, {"id": "subject-percentage", "type": "text", "content": "{{subject.percentage}}%", "style": {"fontSize": "24rpx", "color": "#666666"}}]}]}]}]}, {"id": "efficiency-analysis", "type": "view", "className": "efficiency-analysis-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "efficiency-title", "type": "text", "content": "效率分析", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "efficiency-insights", "type": "view", "children": [{"id": "insight-item", "type": "view", "forEach": "{{efficiencyInsights}}", "forItem": "insight", "forIndex": "index", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "12rpx", "padding": "20rpx", "marginBottom": "16rpx", "borderLeft": "4rpx solid {{insight.color}}"}, "children": [{"id": "insight-title", "type": "text", "content": "{{insight.title}}", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "insight-description", "type": "text", "content": "{{insight.description}}", "style": {"fontSize": "26rpx", "color": "#666666", "lineHeight": "1.4"}}]}]}]}, {"id": "study-calendar", "type": "view", "className": "study-calendar-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "calendar-title", "type": "text", "content": "学习日历", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "calendar-view", "type": "view", "style": {"height": "400rpx", "backgroundColor": "#F8F9FA", "borderRadius": "12rpx", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "children": [{"type": "text", "content": "📅 学习热力图", "style": {"fontSize": "28rpx", "color": "#999999"}}]}]}], "data": {"preparationScore": 68, "preparationLevel": "良好", "preparationDescription": "复习进度良好，继续保持当前节奏", "scoreFactors": [{"name": "复习时长", "score": 75}, {"name": "任务完成率", "score": 82}, {"name": "学习连续性", "score": 65}, {"name": "科目均衡性", "score": 58}], "currentPeriod": "week", "periodTabs": [{"key": "week", "name": "本周"}, {"key": "month", "name": "本月"}, {"key": "quarter", "name": "本季度"}, {"key": "all", "name": "全部"}], "studyStats": {"totalTime": "45.5h", "completedTasks": 28, "pomodoroSessions": 67, "studyDays": 12, "dailyAverage": "3.8h"}, "subjectStats": [{"name": "数学", "time": "18.5h", "percentage": 41, "color": "#1890FF"}, {"name": "英语", "time": "12.3h", "percentage": 27, "color": "#52C41A"}, {"name": "政治", "time": "8.7h", "percentage": 19, "color": "#FA8C16"}, {"name": "专业课", "time": "6.0h", "percentage": 13, "color": "#722ED1"}], "efficiencyInsights": [{"title": "最佳学习时段", "description": "你在上午9-11点的学习效率最高，建议安排重要任务", "color": "#52C41A"}, {"title": "科目建议", "description": "政治和专业课的复习时间偏少，建议适当增加", "color": "#FAAD14"}, {"title": "学习连续性", "description": "最近3天没有学习记录，建议保持每日学习习惯", "color": "#FF4D4F"}]}, "methods": {"showScoreInfo": {"type": "modal", "title": "准备度说明", "content": "准备度基于复习时长、任务完成率、学习连续性和科目均衡性综合计算"}, "switchPeriod": {"type": "function", "description": "切换时间周期", "params": ["<PERSON><PERSON><PERSON>"], "implementation": "updateStatsPeriod"}}, "lifecycle": {"onLoad": ["loadStudyStats", "calculatePreparationScore"], "onShow": ["refreshStats"]}, "interactions": {"pullToRefresh": {"enabled": true, "action": "refreshAllStats"}}}