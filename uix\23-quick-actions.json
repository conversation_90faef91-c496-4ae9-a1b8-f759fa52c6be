{"pageInfo": {"pageName": "快捷操作", "pageId": "quickActions", "pageType": "component", "description": "快捷操作面板和悬浮按钮组件", "version": "1.0.0", "lastModified": "2025-06-27"}, "components": {"floatingActionButton": {"id": "floating-action-button", "type": "view", "className": "fab-container", "style": {"position": "fixed", "bottom": "120rpx", "right": "40rpx", "zIndex": "999"}, "children": [{"id": "fab-menu", "type": "view", "condition": "{{fabExpanded}}", "className": "fab-menu-container", "style": {"position": "absolute", "bottom": "100rpx", "right": "0", "display": "flex", "flexDirection": "column", "gap": "16rpx", "animation": "slideUp 0.3s ease-out"}, "children": [{"id": "fab-menu-item", "type": "view", "forEach": "{{fabMenuItems}}", "forItem": "item", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "gap": "16rpx", "animation": "fadeIn 0.3s ease-out {{index * 0.1}}s both"}, "children": [{"id": "fab-item-label", "type": "view", "style": {"backgroundColor": "rgba(0,0,0,0.8)", "color": "#FFFFFF", "fontSize": "24rpx", "padding": "8rpx 12rpx", "borderRadius": "6rpx", "whiteSpace": "nowrap"}, "children": [{"type": "text", "content": "{{item.label}}"}]}, {"id": "fab-item-button", "type": "button", "style": {"width": "96rpx", "height": "96rpx", "borderRadius": "50%", "backgroundColor": "{{item.color}}", "border": "none", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "32rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.2)"}, "events": {"tap": "executeFabAction"}, "children": [{"type": "text", "content": "{{item.icon}}", "style": {"color": "#FFFFFF"}}]}]}]}, {"id": "fab-main-button", "type": "button", "style": {"width": "112rpx", "height": "112rpx", "borderRadius": "50%", "backgroundColor": "#1890FF", "border": "none", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "36rpx", "color": "#FFFFFF", "boxShadow": "0 8rpx 24rpx rgba(24,144,255,0.4)", "transform": "{{fabExpanded ? 'rotate(45deg)' : 'rotate(0deg)'}}", "transition": "transform 0.3s ease"}, "events": {"tap": "toggleFabMenu"}, "children": [{"type": "text", "content": "+"}]}]}, "quickActionsPanel": {"id": "quick-actions-panel", "type": "view", "condition": "{{showQuickActions}}", "className": "quick-actions-overlay", "style": {"position": "fixed", "top": "0", "left": "0", "right": "0", "bottom": "0", "backgroundColor": "rgba(0,0,0,0.5)", "zIndex": "9998", "display": "flex", "alignItems": "flex-end", "justifyContent": "center"}, "events": {"tap": "closeQuickActions"}, "children": [{"id": "quick-actions-content", "type": "view", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "24rpx 24rpx 0 0", "width": "100%", "maxHeight": "80vh", "padding": "40rpx 32rpx", "animation": "slideUp 0.3s ease-out"}, "events": {"tap": "stopPropagation"}, "children": [{"id": "panel-header", "type": "view", "style": {"textAlign": "center", "marginBottom": "32rpx"}, "children": [{"id": "panel-handle", "type": "view", "style": {"width": "60rpx", "height": "6rpx", "backgroundColor": "#E9ECEF", "borderRadius": "3rpx", "margin": "0 auto 20rpx"}}, {"id": "panel-title", "type": "text", "content": "快捷操作", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333"}}]}, {"id": "quick-actions-grid", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr 1fr", "gap": "24rpx", "marginBottom": "32rpx"}, "children": [{"id": "quick-action-item", "type": "button", "forEach": "{{quickActionItems}}", "forItem": "action", "forIndex": "index", "style": {"backgroundColor": "{{action.bgColor}}", "border": "none", "borderRadius": "16rpx", "padding": "32rpx 16rpx", "textAlign": "center", "display": "flex", "flexDirection": "column", "alignItems": "center", "gap": "12rpx"}, "events": {"tap": "executeQuickAction"}, "children": [{"id": "action-icon", "type": "text", "content": "{{action.icon}}", "style": {"fontSize": "48rpx"}}, {"id": "action-label", "type": "text", "content": "{{action.label}}", "style": {"fontSize": "24rpx", "color": "#333333", "fontWeight": "500"}}]}]}, {"id": "recent-actions", "type": "view", "condition": "{{recentActions.length > 0}}", "children": [{"id": "recent-title", "type": "text", "content": "最近使用", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "recent-actions-list", "type": "view", "children": [{"id": "recent-action-item", "type": "button", "forEach": "{{recentActions}}", "forItem": "action", "forIndex": "index", "style": {"width": "100%", "textAlign": "left", "backgroundColor": "transparent", "border": "none", "padding": "16rpx 0", "borderBottom": "1rpx solid #F0F0F0", "display": "flex", "alignItems": "center", "gap": "16rpx"}, "events": {"tap": "executeRecentAction"}, "children": [{"id": "recent-action-icon", "type": "view", "style": {"width": "48rpx", "height": "48rpx", "borderRadius": "50%", "backgroundColor": "{{action.bgColor}}", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "children": [{"type": "text", "content": "{{action.icon}}", "style": {"fontSize": "24rpx"}}]}, {"id": "recent-action-info", "type": "view", "style": {"flex": "1"}, "children": [{"id": "recent-action-title", "type": "text", "content": "{{action.title}}", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "recent-action-time", "type": "text", "content": "{{action.time}}", "style": {"fontSize": "22rpx", "color": "#999999"}}]}, {"id": "recent-action-arrow", "type": "text", "content": "›", "style": {"fontSize": "24rpx", "color": "#999999"}}]}]}]}]}]}, "contextMenu": {"id": "context-menu", "type": "view", "condition": "{{showContextMenu}}", "className": "context-menu-overlay", "style": {"position": "fixed", "top": "0", "left": "0", "right": "0", "bottom": "0", "zIndex": "9999"}, "events": {"tap": "closeContextMenu"}, "children": [{"id": "context-menu-content", "type": "view", "style": {"position": "absolute", "top": "{{contextMenuPosition.y}}rpx", "left": "{{contextMenuPosition.x}}rpx", "backgroundColor": "#FFFFFF", "borderRadius": "12rpx", "boxShadow": "0 8rpx 24rpx rgba(0,0,0,0.2)", "overflow": "hidden", "minWidth": "200rpx", "animation": "scaleIn 0.2s ease-out"}, "events": {"tap": "stopPropagation"}, "children": [{"id": "context-menu-item", "type": "button", "forEach": "{{contextMenuItems}}", "forItem": "item", "forIndex": "index", "style": {"width": "100%", "textAlign": "left", "backgroundColor": "transparent", "border": "none", "padding": "16rpx 20rpx", "borderBottom": "{{index < contextMenuItems.length - 1 ? '1rpx solid #F0F0F0' : 'none'}}", "display": "flex", "alignItems": "center", "gap": "12rpx", "color": "{{item.type === 'danger' ? '#FF4D4F' : '#333333'}}"}, "events": {"tap": "executeContextAction"}, "children": [{"id": "context-item-icon", "type": "text", "content": "{{item.icon}}", "style": {"fontSize": "24rpx"}}, {"id": "context-item-label", "type": "text", "content": "{{item.label}}", "style": {"fontSize": "28rpx", "flex": "1"}}, {"id": "context-item-shortcut", "type": "text", "condition": "{{item.shortcut}}", "content": "{{item.shortcut}}", "style": {"fontSize": "22rpx", "color": "#999999"}}]}]}]}}, "data": {"fabExpanded": false, "showQuickActions": false, "showContextMenu": false, "contextMenuPosition": {"x": 0, "y": 0}, "fabMenuItems": [{"id": "add_task", "label": "添加任务", "icon": "📝", "color": "#52C41A", "action": "addTask"}, {"id": "add_exam", "label": "添加考试", "icon": "📅", "color": "#1890FF", "action": "addExam"}, {"id": "start_pomodoro", "label": "开始专注", "icon": "🍅", "color": "#FA8C16", "action": "startPomodoro"}, {"id": "quick_note", "label": "快速笔记", "icon": "📄", "color": "#722ED1", "action": "quickNote"}], "quickActionItems": [{"id": "add_task", "label": "添加任务", "icon": "📝", "bgColor": "#F6FFED", "action": "addTask"}, {"id": "add_exam", "label": "添加考试", "icon": "📅", "bgColor": "#E6F7FF", "action": "addExam"}, {"id": "start_pomodoro", "label": "开始专注", "icon": "🍅", "bgColor": "#FFF2E8", "action": "startPomodoro"}, {"id": "view_stats", "label": "查看统计", "icon": "📊", "bgColor": "#F9F0FF", "action": "viewStats"}, {"id": "search", "label": "搜索", "icon": "🔍", "bgColor": "#F0F9FF", "action": "openSearch"}, {"id": "settings", "label": "设置", "icon": "⚙️", "bgColor": "#F5F5F5", "action": "openSettings"}], "recentActions": [{"id": "recent_task_1", "title": "数学高数第一章复习", "icon": "📝", "bgColor": "#F6FFED", "time": "2分钟前", "action": "openTask", "params": {"taskId": "task_001"}}, {"id": "recent_pomodoro_1", "title": "25分钟专注学习", "icon": "🍅", "bgColor": "#FFF2E8", "time": "10分钟前", "action": "startPomodoro", "params": {"duration": 25}}], "contextMenuItems": [{"id": "edit", "label": "编辑", "icon": "✏️", "type": "default", "action": "editItem"}, {"id": "duplicate", "label": "复制", "icon": "📋", "type": "default", "action": "duplicateItem"}, {"id": "share", "label": "分享", "icon": "📤", "type": "default", "action": "shareItem"}, {"id": "delete", "label": "删除", "icon": "🗑️", "type": "danger", "action": "deleteItem"}]}, "methods": {"toggleFabMenu": {"type": "function", "description": "切换悬浮按钮菜单", "implementation": "toggleFabExpansion"}, "executeFabAction": {"type": "function", "description": "执行悬浮按钮操作", "params": ["actionId"], "implementation": "handleFabAction"}, "showQuickActionsPanel": {"type": "function", "description": "显示快捷操作面板", "implementation": "displayQuickActions"}, "closeQuickActions": {"type": "function", "description": "关闭快捷操作面板", "implementation": "hideQuickActions"}, "executeQuickAction": {"type": "function", "description": "执行快捷操作", "params": ["actionId"], "implementation": "handleQuickAction"}, "executeRecentAction": {"type": "function", "description": "执行最近操作", "params": ["actionId"], "implementation": "handleRecentAction"}, "showContextMenu": {"type": "function", "description": "显示上下文菜单", "params": ["x", "y", "targetId"], "implementation": "displayContextMenu"}, "closeContextMenu": {"type": "function", "description": "关闭上下文菜单", "implementation": "hideContextMenu"}, "executeContextAction": {"type": "function", "description": "执行上下文操作", "params": ["actionId"], "implementation": "handleContextAction"}, "stopPropagation": {"type": "function", "description": "阻止事件冒泡", "implementation": "preventEventBubbling"}}, "animations": {"slideUp": {"keyframes": {"0%": {"transform": "translateY(100%)"}, "100%": {"transform": "translateY(0)"}}, "duration": "0.3s", "timingFunction": "ease-out"}, "fadeIn": {"keyframes": {"0%": {"opacity": "0", "transform": "translateY(20rpx)"}, "100%": {"opacity": "1", "transform": "translateY(0)"}}, "duration": "0.3s", "timingFunction": "ease-out"}, "scaleIn": {"keyframes": {"0%": {"opacity": "0", "transform": "scale(0.8)"}, "100%": {"opacity": "1", "transform": "scale(1)"}}, "duration": "0.2s", "timingFunction": "ease-out"}}}