// pages/exam-detail/index.js
Page({
  data: {
    examId: '',
    exam: {},
    countdown: {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0
    },
    relatedTasks: [],
    countdownTimer: null
  },

  onLoad(options) {
    const examId = options.id
    if (examId) {
      this.setData({ examId })
      this.loadExamDetail(examId)
    }
  },

  onShow() {
    // 页面显示时刷新数据和启动倒计时
    if (this.data.examId) {
      this.loadExamDetail(this.data.examId)
      this.startCountdown()
    }
  },

  onHide() {
    // 页面隐藏时停止倒计时
    this.stopCountdown()
  },

  onUnload() {
    // 页面卸载时停止倒计时
    this.stopCountdown()
  },

  // 加载考试详情
  loadExamDetail(examId) {
    // 模拟数据，实际应该从存储或服务器获取
    const exam = {
      id: examId,
      name: '2025年考研',
      type: '研究生入学考试',
      description: '全国硕士研究生统一招生考试，包含数学、英语、政治、专业课四门科目',
      date: '2025-12-23',
      time: '08:30',
      importance: 'high',
      importanceText: '非常重要',
      status: 'active',
      statusText: '当前考试',
      isActive: true,
      targetScore: 380,
      overallProgress: 68,
      timeProgress: 45,
      remainingDays: 156,
      totalTasks: 24,
      completedTasks: 16,
      totalStudyTime: '128.5h',
      subjects: [
        {
          id: 'math',
          name: '数学一',
          targetScore: 120,
          totalScore: 150,
          progress: 75,
          progressColor: '#52C41A',
          studyTime: '45.2h',
          completedTasks: 8,
          avgEfficiency: '88%'
        },
        {
          id: 'english',
          name: '英语一',
          targetScore: 70,
          totalScore: 100,
          progress: 82,
          progressColor: '#52C41A',
          studyTime: '32.8h',
          completedTasks: 6,
          avgEfficiency: '91%'
        },
        {
          id: 'politics',
          name: '思想政治理论',
          targetScore: 70,
          totalScore: 100,
          progress: 45,
          progressColor: '#FA8C16',
          studyTime: '18.5h',
          completedTasks: 2,
          avgEfficiency: '72%'
        },
        {
          id: 'major',
          name: '计算机专业基础',
          targetScore: 120,
          totalScore: 150,
          progress: 60,
          progressColor: '#1890FF',
          studyTime: '32.0h',
          completedTasks: 4,
          avgEfficiency: '85%'
        }
      ]
    }

    const relatedTasks = [
      {
        id: 'task_001',
        title: '数学高数第一章复习',
        subject: '数学',
        dueDate: '01-28',
        priority: 'high',
        priorityText: '高',
        progress: 60,
        completed: false
      },
      {
        id: 'task_002',
        title: '英语单词背诵',
        subject: '英语',
        dueDate: '01-25',
        priority: 'medium',
        priorityText: '中',
        progress: 100,
        completed: true
      },
      {
        id: 'task_003',
        title: '政治马原理论学习',
        subject: '政治',
        dueDate: '01-30',
        priority: 'medium',
        priorityText: '中',
        progress: 30,
        completed: false
      }
    ]

    this.setData({ exam, relatedTasks })
    this.calculateCountdown()
  },

  // 计算倒计时
  calculateCountdown() {
    const examDate = new Date(`${this.data.exam.date} ${this.data.exam.time}`)
    const now = new Date()
    const diff = examDate.getTime() - now.getTime()

    if (diff > 0) {
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)

      this.setData({
        countdown: {
          days: days.toString().padStart(2, '0'),
          hours: hours.toString().padStart(2, '0'),
          minutes: minutes.toString().padStart(2, '0'),
          seconds: seconds.toString().padStart(2, '0')
        }
      })
    }
  },

  // 启动倒计时
  startCountdown() {
    this.stopCountdown() // 先停止之前的计时器
    this.countdownTimer = setInterval(() => {
      this.calculateCountdown()
    }, 1000)
  },

  // 停止倒计时
  stopCountdown() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
      this.countdownTimer = null
    }
  },

  // 设为当前考试
  setAsActiveExam() {
    wx.showModal({
      title: '设为当前考试',
      content: '确定要将此考试设为当前考试吗？',
      success: (res) => {
        if (res.confirm) {
          const exam = { ...this.data.exam }
          exam.isActive = true
          exam.status = 'active'
          exam.statusText = '当前考试'

          this.setData({ exam })

          wx.showToast({
            title: '设置成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 编辑考试
  editExam() {
    wx.navigateTo({
      url: `/pages/edit-exam/index?id=${this.data.examId}`
    })
  },

  // 显示更多操作
  showMoreActions() {
    wx.showActionSheet({
      itemList: ['复制考试', '删除考试', '导出数据'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.duplicateExam()
            break
          case 1:
            this.deleteExam()
            break
          case 2:
            this.exportExamData()
            break
        }
      }
    })
  },

  // 查看任务详情
  viewTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/task-detail/index?id=${taskId}`
    })
  },

  // 查看所有任务
  viewAllTasks() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 添加任务
  addTask() {
    wx.navigateTo({
      url: `/pages/add-task/index?examId=${this.data.examId}`
    })
  },

  // 开始学习
  startStudy() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  // 设置提醒
  setReminder() {
    wx.showModal({
      title: '设置提醒',
      content: '是否开启考试提醒？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '提醒设置成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 分享考试
  shareExam() {
    return {
      title: `我正在准备${this.data.exam.name}`,
      path: `/pages/exam-detail/index?id=${this.data.examId}`
    }
  },

  // 复制考试
  duplicateExam() {
    wx.navigateTo({
      url: `/pages/add-exam/index?duplicate=${this.data.examId}`
    })
  },

  // 删除考试
  deleteExam() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除此考试吗？相关任务也将被删除，此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '考试已删除',
            icon: 'success'
          })

          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      }
    })
  },

  // 导出考试数据
  exportExamData() {
    wx.showToast({
      title: '数据导出中...',
      icon: 'loading'
    })

    setTimeout(() => {
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      })
    }, 2000)
  },

  // 分享功能
  onShareAppMessage() {
    return this.shareExam()
  }
})