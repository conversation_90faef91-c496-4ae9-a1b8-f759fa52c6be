<!--pages/exam-detail/index.wxml-->
<view class="container">
  <!-- 考试头部信息 -->
  <view class="exam-header-container">
    <view class="exam-status-bar">
      <view class="exam-status">
        <view class="status-indicator status-{{exam.status}}"></view>
        <text class="status-text">{{exam.statusText}}</text>
      </view>
      <view class="exam-actions">
        <button class="set-active-btn" wx:if="{{!exam.isActive}}" bindtap="setAsActiveExam">设为当前</button>
        <button class="edit-btn" bindtap="editExam">✏️</button>
        <button class="more-btn" bindtap="showMoreActions">⋯</button>
      </view>
    </view>

    <view class="exam-title-section">
      <text class="exam-name">{{exam.name}}</text>
      <view class="exam-meta">
        <text class="exam-type">{{exam.type}}</text>
        <text class="exam-importance importance-{{exam.importance}}">{{exam.importanceText}}</text>
      </view>
    </view>

    <view class="exam-description" wx:if="{{exam.description}}">
      <text class="description-text">{{exam.description}}</text>
    </view>
  </view>

  <!-- 倒计时信息 -->
  <view class="countdown-container">
    <view class="section-header">
      <text class="section-title">考试倒计时</text>
      <text class="exam-date">{{exam.date}}</text>
    </view>

    <view class="countdown-display">
      <view class="countdown-item">
        <text class="countdown-number">{{countdown.days}}</text>
        <text class="countdown-label">天</text>
      </view>
      <view class="countdown-separator">:</view>
      <view class="countdown-item">
        <text class="countdown-number">{{countdown.hours}}</text>
        <text class="countdown-label">时</text>
      </view>
      <view class="countdown-separator">:</view>
      <view class="countdown-item">
        <text class="countdown-number">{{countdown.minutes}}</text>
        <text class="countdown-label">分</text>
      </view>
      <view class="countdown-separator">:</view>
      <view class="countdown-item">
        <text class="countdown-number">{{countdown.seconds}}</text>
        <text class="countdown-label">秒</text>
      </view>
    </view>

    <view class="countdown-progress">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{exam.timeProgress}}%"></view>
      </view>
      <text class="progress-text">距离考试还有 {{exam.remainingDays}} 天</text>
    </view>
  </view>

  <!-- 考试科目 -->
  <view class="subjects-container">
    <view class="section-header">
      <text class="section-title">考试科目</text>
      <text class="subjects-count">{{exam.subjects.length}}科</text>
    </view>

    <view class="subjects-list">
      <view class="subject-item" wx:for="{{exam.subjects}}" wx:key="id">
        <view class="subject-header">
          <text class="subject-name">{{item.name}}</text>
          <view class="subject-score">
            <text class="target-score">目标: {{item.targetScore}}</text>
            <text class="total-score">/{{item.totalScore}}</text>
          </view>
        </view>

        <view class="subject-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%; background-color: {{item.progressColor}}"></view>
          </view>
          <text class="progress-text">准备进度 {{item.progress}}%</text>
        </view>

        <view class="subject-stats">
          <view class="stat-item">
            <text class="stat-label">学习时长</text>
            <text class="stat-value">{{item.studyTime}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">完成任务</text>
            <text class="stat-value">{{item.completedTasks}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">平均效率</text>
            <text class="stat-value">{{item.avgEfficiency}}%</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 准备进度 -->
  <view class="preparation-container">
    <view class="section-header">
      <text class="section-title">准备进度</text>
      <text class="overall-progress">{{exam.overallProgress}}%</text>
    </view>

    <view class="progress-circle">
      <view class="circle-progress" style="background: conic-gradient(#52C41A 0deg {{exam.overallProgress * 3.6}}deg, #F0F0F0 {{exam.overallProgress * 3.6}}deg 360deg)">
        <view class="circle-inner">
          <text class="progress-value">{{exam.overallProgress}}%</text>
          <text class="progress-label">整体进度</text>
        </view>
      </view>
    </view>

    <view class="preparation-stats">
      <view class="prep-stat-item">
        <text class="prep-stat-icon">📚</text>
        <view class="prep-stat-content">
          <text class="prep-stat-value">{{exam.totalTasks}}</text>
          <text class="prep-stat-label">总任务数</text>
        </view>
      </view>

      <view class="prep-stat-item">
        <text class="prep-stat-icon">✅</text>
        <view class="prep-stat-content">
          <text class="prep-stat-value">{{exam.completedTasks}}</text>
          <text class="prep-stat-label">已完成</text>
        </view>
      </view>

      <view class="prep-stat-item">
        <text class="prep-stat-icon">⏰</text>
        <view class="prep-stat-content">
          <text class="prep-stat-value">{{exam.totalStudyTime}}</text>
          <text class="prep-stat-label">学习时长</text>
        </view>
      </view>

      <view class="prep-stat-item">
        <text class="prep-stat-icon">🎯</text>
        <view class="prep-stat-content">
          <text class="prep-stat-value">{{exam.targetScore}}</text>
          <text class="prep-stat-label">目标分数</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 相关任务 -->
  <view class="related-tasks-container">
    <view class="section-header">
      <text class="section-title">相关任务</text>
      <text class="view-all" bindtap="viewAllTasks">查看全部</text>
    </view>

    <view class="tasks-list" wx:if="{{relatedTasks.length > 0}}">
      <view class="task-item" wx:for="{{relatedTasks}}" wx:key="id" bindtap="viewTaskDetail" data-id="{{item.id}}">
        <view class="task-checkbox">
          <text class="checkbox-icon">{{item.completed ? '✅' : '⭕'}}</text>
        </view>
        <view class="task-content">
          <text class="task-title {{item.completed ? 'completed' : ''}}">{{item.title}}</text>
          <view class="task-meta">
            <text class="task-subject">{{item.subject}}</text>
            <text class="task-due">{{item.dueDate}}</text>
            <text class="task-priority priority-{{item.priority}}">{{item.priorityText}}</text>
          </view>
        </view>
        <view class="task-progress">
          <text class="progress-text">{{item.progress}}%</text>
        </view>
      </view>
    </view>

    <view class="empty-tasks" wx:else>
      <text class="empty-text">暂无相关任务</text>
      <button class="add-task-btn" bindtap="addTask">添加任务</button>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-container">
    <view class="action-grid">
      <button class="quick-action-btn add-task-btn" bindtap="addTask">
        <text class="action-icon">📝</text>
        <text class="action-text">添加任务</text>
      </button>

      <button class="quick-action-btn study-btn" bindtap="startStudy">
        <text class="action-icon">🍅</text>
        <text class="action-text">开始学习</text>
      </button>

      <button class="quick-action-btn reminder-btn" bindtap="setReminder">
        <text class="action-icon">⏰</text>
        <text class="action-text">设置提醒</text>
      </button>

      <button class="quick-action-btn share-btn" bindtap="shareExam">
        <text class="action-icon">📤</text>
        <text class="action-text">分享</text>
      </button>
    </view>
  </view>
</view>