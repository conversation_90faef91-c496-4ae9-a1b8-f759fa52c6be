/* pages/edit-exam/index.wxss */
/* 复用添加考试页面的样式，并添加编辑特有的样式 */

/* 表单区域 */
.form-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.add-subject-btn {
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
  font-weight: 500;
}

.form-input {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.form-input:focus {
  border-color: #1890FF;
  background-color: #FFFFFF;
}

.form-textarea {
  width: 100%;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
  min-height: 120rpx;
}

.form-textarea:focus {
  border-color: #1890FF;
  background-color: #FFFFFF;
}

/* 考试类型选项 */
.type-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 16rpx;
  transition: all 0.3s ease;
}

.type-option.active {
  background-color: #E6F7FF;
  border-color: #1890FF;
}

.type-icon {
  font-size: 32rpx;
}

.type-text {
  font-size: 22rpx;
  color: #333333;
}

/* 时间选择器 */
.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.picker-icon {
  font-size: 24rpx;
  color: #999999;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time-picker {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  color: #333333;
}

.time-separator {
  font-size: 24rpx;
  color: #666666;
}

/* 科目列表 */
.subjects-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.subject-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.subject-input {
  flex: 2;
  background-color: #FFFFFF;
  border: 1rpx solid #E9ECEF;
  border-radius: 6rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  color: #333333;
}

.subject-score, .subject-target {
  flex: 1;
  background-color: #FFFFFF;
  border: 1rpx solid #E9ECEF;
  border-radius: 6rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

.remove-subject-btn {
  width: 48rpx;
  height: 48rpx;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border: none;
  border-radius: 50%;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.empty-subjects {
  text-align: center;
  padding: 40rpx 0;
}

.empty-text {
  font-size: 24rpx;
  color: #999999;
}

/* 重要程度选项 */
.importance-options {
  display: flex;
  gap: 16rpx;
}

.importance-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 16rpx;
  transition: all 0.3s ease;
}

.importance-option.active {
  background-color: #E6F7FF;
  border-color: #1890FF;
}

.importance-icon {
  font-size: 32rpx;
}

.importance-text {
  font-size: 22rpx;
  color: #333333;
}

/* 状态选项 */
.status-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #F5F5F5;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.status-option.active {
  background-color: #52C41A;
  color: #FFFFFF;
  border-color: #52C41A;
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-size: 22rpx;
}

/* 开关项 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-label {
  font-size: 26rpx;
  color: #333333;
}

/* 提醒频率 */
.reminder-frequency {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.frequency-option {
  background-color: #F5F5F5;
  color: #666666;
  border: 1rpx solid #E9ECEF;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.frequency-option.active {
  background-color: #1890FF;
  color: #FFFFFF;
  border-color: #1890FF;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  gap: 12rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  position: sticky;
  bottom: 0;
  border-top: 1rpx solid #F0F0F0;
}

.cancel-btn {
  flex: 1;
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 26rpx;
}

.delete-btn {
  flex: 1;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 26rpx;
}

.submit-btn {
  flex: 2;
  background-color: #52C41A;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx;
  font-size: 26rpx;
}

.cancel-btn:active {
  background-color: #E6E6E6;
}

.delete-btn:active {
  background-color: #D9363E;
}

.submit-btn:active {
  background-color: #389E0D;
}
