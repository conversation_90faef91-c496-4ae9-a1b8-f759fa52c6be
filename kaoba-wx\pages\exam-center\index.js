// pages/exam-center/index.js
Page({
  data: {
    examStats: [],
    filterTabs: [],
    currentFilter: 'all',
    exams: [],
    filteredExams: [],
    showActionSheet: false,
    selectedExam: null,
    examActions: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadData()
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }
  },

  onPullDownRefresh() {
    this.loadData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 初始化页面
  initPage() {
    this.initFilterTabs()
    this.initExamActions()
    this.loadData()
  },

  // 初始化筛选标签
  initFilterTabs() {
    const filterTabs = [
      { value: 'all', label: '全部', count: 0 },
      { value: 'upcoming', label: '即将到来', count: 0 },
      { value: 'preparing', label: '备考中', count: 0 },
      { value: 'completed', label: '已完成', count: 0 }
    ]

    this.setData({ filterTabs })
  },

  // 初始化考试操作
  initExamActions() {
    const examActions = [
      { id: 'view', icon: '👁️', text: '查看详情', action: 'viewDetail' },
      { id: 'edit', icon: '✏️', text: '编辑考试', action: 'edit' },
      { id: 'study', icon: '📚', text: '开始学习', action: 'startStudy' },
      { id: 'tasks', icon: '📝', text: '查看任务', action: 'viewTasks' },
      { id: 'share', icon: '📤', text: '分享考试', action: 'share' },
      { id: 'delete', icon: '🗑️', text: '删除考试', action: 'delete' }
    ]

    this.setData({ examActions })
  },

  // 加载数据
  loadData() {
    this.loadExamStats()
    this.loadExams()
  },

  // 加载考试统计
  loadExamStats() {
    // 模拟数据
    const examStats = [
      { label: '总考试', value: '8' },
      { label: '备考中', value: '3' },
      { label: '已完成', value: '2' },
      { label: '平均准备度', value: '72%' }
    ]

    this.setData({ examStats })
  },

  // 加载考试列表
  loadExams() {
    // 模拟数据
    const exams = [
      {
        id: 'exam_001',
        name: '2025年考研',
        type: '研究生入学考试',
        date: '2025-12-23',
        status: 'preparing',
        statusText: '备考中',
        preparationProgress: 65,
        progressColor: '#1890FF',
        countdown: [
          { value: 178, unit: '天' },
          { value: 12, unit: '时' },
          { value: 35, unit: '分' }
        ],
        subjects: [
          { name: '数学', progress: 70 },
          { name: '英语', progress: 80 },
          { name: '政治', progress: 45 },
          { name: '专业课', progress: 60 }
        ]
      },
      {
        id: 'exam_002',
        name: '英语四级',
        type: '大学英语等级考试',
        date: '2025-06-15',
        status: 'upcoming',
        statusText: '即将到来',
        preparationProgress: 85,
        progressColor: '#52C41A',
        countdown: [
          { value: 45, unit: '天' },
          { value: 8, unit: '时' },
          { value: 20, unit: '分' }
        ],
        subjects: [
          { name: '听力', progress: 80 },
          { name: '阅读', progress: 90 },
          { name: '写作', progress: 75 },
          { name: '翻译', progress: 85 }
        ]
      },
      {
        id: 'exam_003',
        name: '计算机二级',
        type: '全国计算机等级考试',
        date: '2025-03-28',
        status: 'preparing',
        statusText: '备考中',
        preparationProgress: 40,
        progressColor: '#FA8C16',
        countdown: [
          { value: 89, unit: '天' },
          { value: 15, unit: '时' },
          { value: 42, unit: '分' }
        ],
        subjects: [
          { name: 'Office应用', progress: 50 },
          { name: '理论知识', progress: 30 }
        ]
      },
      {
        id: 'exam_004',
        name: '驾照考试',
        type: '机动车驾驶证考试',
        date: '2024-12-15',
        status: 'completed',
        statusText: '已完成',
        preparationProgress: 100,
        progressColor: '#52C41A',
        subjects: [
          { name: '科目一', progress: 100 },
          { name: '科目二', progress: 100 },
          { name: '科目三', progress: 100 },
          { name: '科目四', progress: 100 }
        ]
      }
    ]

    this.setData({ exams })
    this.filterExams()
    this.updateFilterCounts()
  },

  // 更新筛选计数
  updateFilterCounts() {
    const { exams, filterTabs } = this.data
    const updatedTabs = filterTabs.map(tab => {
      let count = 0
      if (tab.value === 'all') {
        count = exams.length
      } else {
        count = exams.filter(exam => exam.status === tab.value).length
      }
      return { ...tab, count }
    })

    this.setData({ filterTabs: updatedTabs })
  },

  // 筛选考试
  filterExams() {
    const { exams, currentFilter } = this.data
    let filteredExams = exams

    if (currentFilter !== 'all') {
      filteredExams = exams.filter(exam => exam.status === currentFilter)
    }

    // 按日期排序
    filteredExams.sort((a, b) => new Date(a.date) - new Date(b.date))

    this.setData({ filteredExams })
  },

  // 切换筛选
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterExams()
  },

  // 获取空状态标题
  getEmptyTitle() {
    const { currentFilter } = this.data
    const titles = {
      all: '还没有考试',
      upcoming: '暂无即将到来的考试',
      preparing: '暂无备考中的考试',
      completed: '暂无已完成的考试'
    }
    return titles[currentFilter] || '暂无数据'
  },

  // 获取空状态消息
  getEmptyMessage() {
    const { currentFilter } = this.data
    const messages = {
      all: '添加你的第一个考试，开始高效备考',
      upcoming: '所有考试都在准备中',
      preparing: '没有正在备考的考试',
      completed: '还没有完成的考试'
    }
    return messages[currentFilter] || '暂无相关数据'
  },

  // 显示考试操作菜单
  showExamActions(e) {
    const exam = e.currentTarget.dataset.exam
    this.setData({
      selectedExam: exam,
      showActionSheet: true
    })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedExam: null
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 执行考试操作
  executeExamAction(e) {
    const action = e.currentTarget.dataset.action
    const exam = this.data.selectedExam

    this.hideActionSheet()

    switch (action) {
      case 'viewDetail':
        this.viewExamDetail({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'edit':
        this.editExam({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'startStudy':
        this.startStudyForExam({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'viewTasks':
        this.viewExamTasks({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'share':
        this.shareExam(exam)
        break
      case 'delete':
        this.deleteExam(exam)
        break
    }
  },

  // 页面跳转和操作方法
  openSearch() {
    wx.navigateTo({
      url: '/pages/search/index?type=exam'
    })
  },

  viewExamDetail(e) {
    const examId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/exam-detail/index?id=' + examId
    })
  },

  addExam() {
    wx.navigateTo({
      url: '/pages/add-exam/index'
    })
  },

  editExam(e) {
    const examId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/edit-exam/index?id=' + examId
    })
  },

  startStudyForExam(e) {
    const examId = e.currentTarget.dataset.id
    // 跳转到任务中心，筛选该考试的任务
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewExamTasks(e) {
    const examId = e.currentTarget.dataset.id
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  shareExam(exam) {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    })
  },

  deleteExam(exam) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除考试"${exam.name}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          // 执行删除操作
          const exams = this.data.exams.filter(e => e.id !== exam.id)
          this.setData({ exams })
          this.filterExams()
          this.updateFilterCounts()
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  }
})
