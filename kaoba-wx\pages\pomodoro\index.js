// pages/pomodoro/index.js
Page({
  data: {
    // 计时器状态
    timerState: 'work', // work, shortBreak, longBreak
    timerStateText: '工作时间',
    isRunning: false,
    isPaused: false,

    // 时间设置
    workDuration: 25, // 分钟
    shortBreakDuration: 5,
    longBreakDuration: 15,

    // 当前时间
    currentTime: 25 * 60, // 秒
    totalTime: 25 * 60,
    displayTime: '25:00',
    progressDegree: 0,

    // 会话统计
    completedSessions: 0,
    totalSessions: 4,
    todayFocus: '0h',

    // 当前任务
    currentTask: null,
    availableTasks: [],

    // 设置
    showSettings: false,
    selectedSound: 'none',
    soundOptions: [],
    autoStartBreak: true,
    vibrationEnabled: true,
    soundEnabled: true,

    // 统计数据
    todayStats: [],
    recentSessions: [],

    // 弹窗状态
    showTaskModal: false,

    // 计时器
    timer: null
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadData()
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      })
    }
  },

  onHide() {
    // 页面隐藏时暂停计时器
    if (this.data.isRunning) {
      this.pauseTimer()
    }
  },

  onUnload() {
    // 页面卸载时清除计时器
    this.clearTimer()
  },

  // 初始化页面
  initPage() {
    this.initSoundOptions()
    this.initTimer()
    this.loadSettings()
  },

  // 初始化音效选项
  initSoundOptions() {
    const soundOptions = [
      { value: 'none', name: '无音效', icon: '🔇' },
      { value: 'rain', name: '雨声', icon: '🌧️' },
      { value: 'forest', name: '森林', icon: '🌲' },
      { value: 'ocean', name: '海浪', icon: '🌊' },
      { value: 'cafe', name: '咖啡厅', icon: '☕' },
      { value: 'white_noise', name: '白噪音', icon: '📻' }
    ]

    this.setData({ soundOptions })
  },

  // 初始化计时器
  initTimer() {
    const { workDuration } = this.data
    const totalTime = workDuration * 60

    this.setData({
      currentTime: totalTime,
      totalTime: totalTime,
      displayTime: this.formatTime(totalTime),
      progressDegree: 0
    })
  },

  // 加载设置
  loadSettings() {
    const settings = wx.getStorageSync('pomodoroSettings') || {}

    this.setData({
      workDuration: settings.workDuration || 25,
      shortBreakDuration: settings.shortBreakDuration || 5,
      longBreakDuration: settings.longBreakDuration || 15,
      selectedSound: settings.selectedSound || 'none',
      autoStartBreak: settings.autoStartBreak !== false,
      vibrationEnabled: settings.vibrationEnabled !== false,
      soundEnabled: settings.soundEnabled !== false
    })

    this.initTimer()
  },

  // 保存设置
  saveSettings() {
    const { workDuration, shortBreakDuration, longBreakDuration, selectedSound, autoStartBreak, vibrationEnabled, soundEnabled } = this.data

    wx.setStorageSync('pomodoroSettings', {
      workDuration,
      shortBreakDuration,
      longBreakDuration,
      selectedSound,
      autoStartBreak,
      vibrationEnabled,
      soundEnabled
    })
  },

  // 加载数据
  loadData() {
    this.loadAvailableTasks()
    this.loadTodayStats()
    this.loadRecentSessions()
  },

  // 加载可用任务
  loadAvailableTasks() {
    // 模拟数据
    const availableTasks = [
      {
        id: 'task_001',
        title: '数学高数第一章复习',
        subject: '数学'
      },
      {
        id: 'task_002',
        title: '英语单词背诵',
        subject: '英语'
      },
      {
        id: 'task_003',
        title: '政治马原理论学习',
        subject: '政治'
      }
    ]

    this.setData({ availableTasks })
  },

  // 加载今日统计
  loadTodayStats() {
    const todayStats = [
      { icon: '🍅', label: '专注次数', value: '8' },
      { icon: '⏰', label: '专注时长', value: '3.2h' },
      { icon: '📈', label: '效率', value: '92%' },
      { icon: '🎯', label: '完成任务', value: '5' }
    ]

    this.setData({ todayStats })
  },

  // 加载最近会话
  loadRecentSessions() {
    const recentSessions = [
      {
        id: 'session_001',
        type: 'work',
        taskName: '数学复习',
        duration: '25分钟',
        time: '2小时前',
        status: 'completed',
        statusText: '已完成'
      },
      {
        id: 'session_002',
        type: 'break',
        duration: '5分钟',
        time: '2.5小时前',
        status: 'completed',
        statusText: '已完成'
      }
    ]

    this.setData({ recentSessions })
  },

  // 格式化时间显示
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // 计算进度角度
  calculateProgress() {
    const { currentTime, totalTime } = this.data
    const progress = (totalTime - currentTime) / totalTime
    return progress * 360
  },

  // 开始/暂停计时器
  toggleTimer() {
    if (this.data.isRunning) {
      this.pauseTimer()
    } else {
      this.startTimer()
    }
  },

  // 开始计时器
  startTimer() {
    this.setData({
      isRunning: true,
      isPaused: false
    })

    this.timer = setInterval(() => {
      let { currentTime } = this.data

      if (currentTime > 0) {
        currentTime--
        this.setData({
          currentTime,
          displayTime: this.formatTime(currentTime),
          progressDegree: this.calculateProgress()
        })
      } else {
        this.completeSession()
      }
    }, 1000)
  },

  // 暂停计时器
  pauseTimer() {
    this.clearTimer()
    this.setData({
      isRunning: false,
      isPaused: true
    })
  },

  // 清除计时器
  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },

  // 重置计时器
  resetTimer() {
    this.clearTimer()
    this.setData({
      isRunning: false,
      isPaused: false
    })
    this.initTimer()
  },

  // 跳过当前会话
  skipSession() {
    this.clearTimer()
    this.completeSession()
  },

  // 完成会话
  completeSession() {
    this.clearTimer()

    const { timerState, completedSessions } = this.data

    // 播放提醒
    this.playNotification()

    // 更新统计
    if (timerState === 'work') {
      this.setData({ completedSessions: completedSessions + 1 })
    }

    // 切换到下一个状态
    this.switchToNextState()
  },

  // 切换到下一个状态
  switchToNextState() {
    const { timerState, completedSessions } = this.data
    let nextState, nextStateText, nextDuration

    if (timerState === 'work') {
      // 工作完成，进入休息
      if (completedSessions % 4 === 0) {
        nextState = 'longBreak'
        nextStateText = '长休息'
        nextDuration = this.data.longBreakDuration
      } else {
        nextState = 'shortBreak'
        nextStateText = '短休息'
        nextDuration = this.data.shortBreakDuration
      }
    } else {
      // 休息完成，进入工作
      nextState = 'work'
      nextStateText = '工作时间'
      nextDuration = this.data.workDuration
    }

    const totalTime = nextDuration * 60

    this.setData({
      timerState: nextState,
      timerStateText: nextStateText,
      currentTime: totalTime,
      totalTime: totalTime,
      displayTime: this.formatTime(totalTime),
      progressDegree: 0,
      isRunning: false,
      isPaused: false
    })

    // 如果开启自动开始，则自动开始下一个会话
    if (this.data.autoStartBreak) {
      setTimeout(() => {
        this.startTimer()
      }, 1000)
    }
  },

  // 播放通知
  playNotification() {
    // 振动提醒
    if (this.data.vibrationEnabled) {
      wx.vibrateShort()
    }

    // 声音提醒
    if (this.data.soundEnabled) {
      // 这里可以播放音效
      console.log('播放提醒音效')
    }

    // 显示完成提示
    wx.showToast({
      title: this.data.timerState === 'work' ? '专注完成！' : '休息结束！',
      icon: 'success'
    })
  },

  // 调整时间设置
  adjustTime(e) {
    const { type, action } = e.currentTarget.dataset
    const { workDuration, shortBreakDuration, longBreakDuration } = this.data

    let newValue
    if (type === 'work') {
      newValue = action === 'increase' ? workDuration + 5 : workDuration - 5
      newValue = Math.max(5, Math.min(60, newValue))
      this.setData({ workDuration: newValue })
    } else if (type === 'shortBreak') {
      newValue = action === 'increase' ? shortBreakDuration + 1 : shortBreakDuration - 1
      newValue = Math.max(1, Math.min(30, newValue))
      this.setData({ shortBreakDuration: newValue })
    } else if (type === 'longBreak') {
      newValue = action === 'increase' ? longBreakDuration + 5 : longBreakDuration - 5
      newValue = Math.max(5, Math.min(60, newValue))
      this.setData({ longBreakDuration: newValue })
    }

    this.saveSettings()

    // 如果当前没有运行，更新计时器
    if (!this.data.isRunning && !this.data.isPaused) {
      this.initTimer()
    }
  },

  // 切换设置显示
  toggleSettings() {
    this.setData({
      showSettings: !this.data.showSettings
    })
  },

  // 选择任务
  selectTask() {
    this.setData({ showTaskModal: true })
  },

  // 隐藏任务选择弹窗
  hideTaskModal() {
    this.setData({ showTaskModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 选择任务选项
  selectTaskOption(e) {
    const task = e.currentTarget.dataset.task
    this.setData({
      currentTask: task || null,
      showTaskModal: false
    })
  },

  // 选择音效
  selectSound(e) {
    const sound = e.currentTarget.dataset.sound
    this.setData({ selectedSound: sound })
    this.saveSettings()
  },

  // 切换自动开始休息
  toggleAutoStartBreak(e) {
    this.setData({ autoStartBreak: e.detail.value })
    this.saveSettings()
  },

  // 切换振动
  toggleVibration(e) {
    this.setData({ vibrationEnabled: e.detail.value })
    this.saveSettings()
  },

  // 切换声音
  toggleSound(e) {
    this.setData({ soundEnabled: e.detail.value })
    this.saveSettings()
  },

  // 查看详细统计
  viewDetailStats() {
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  },

  // 查看所有会话
  viewAllSessions() {
    wx.navigateTo({
      url: '/pages/study-session-detail/index'
    })
  }
})