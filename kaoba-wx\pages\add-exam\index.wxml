<!--pages/add-exam/index.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <text class="page-title">📅 添加考试</text>
    <text class="page-subtitle">记录重要考试，合理安排复习时间</text>
  </view>

  <!-- 表单内容 -->
  <view class="form-container">
    <!-- 考试名称 -->
    <view class="form-group">
      <text class="form-label">考试名称 *</text>
      <input class="form-input"
             placeholder="请输入考试名称，如：期末考试、四级考试"
             value="{{examForm.title}}"
             bindinput="onTitleInput"
             maxlength="50"/>
      <text class="char-count">{{examForm.title.length}}/50</text>
    </view>

    <!-- 考试科目 -->
    <view class="form-group">
      <text class="form-label">考试科目 *</text>
      <input class="form-input"
             placeholder="请输入科目名称，如：高等数学、英语"
             value="{{examForm.subject}}"
             bindinput="onSubjectInput"
             maxlength="30"/>
    </view>

    <!-- 考试时间 -->
    <view class="form-group">
      <text class="form-label">考试时间 *</text>
      <view class="datetime-container">
        <picker mode="date"
                value="{{examForm.date}}"
                start="{{todayDate}}"
                bindchange="onDateChange">
          <view class="datetime-picker">
            <text class="datetime-text">{{examForm.date || '选择日期'}}</text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>

        <picker mode="time"
                value="{{examForm.time}}"
                bindchange="onTimeChange">
          <view class="datetime-picker">
            <text class="datetime-text">{{examForm.time || '选择时间'}}</text>
            <text class="picker-icon">⏰</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 考试地点 -->
    <view class="form-group">
      <text class="form-label">考试地点</text>
      <input class="form-input"
             placeholder="请输入考试地点，如：教学楼A101"
             value="{{examForm.location}}"
             bindinput="onLocationInput"
             maxlength="50"/>
    </view>

    <!-- 重要程度 -->
    <view class="form-group">
      <text class="form-label">重要程度</text>
      <view class="importance-options">
        <button class="importance-btn {{examForm.importance === 'low' ? 'active low' : ''}}"
                bindtap="selectImportance"
                data-level="low">
          🟢 一般
        </button>
        <button class="importance-btn {{examForm.importance === 'medium' ? 'active medium' : ''}}"
                bindtap="selectImportance"
                data-level="medium">
          🟡 重要
        </button>
        <button class="importance-btn {{examForm.importance === 'high' ? 'active high' : ''}}"
                bindtap="selectImportance"
                data-level="high">
          🔴 非常重要
        </button>
      </view>
    </view>

    <!-- 考试类型 -->
    <view class="form-group">
      <text class="form-label">考试类型</text>
      <view class="type-options">
        <button class="type-btn {{examForm.type === 'final' ? 'active' : ''}}"
                bindtap="selectType"
                data-type="final">
          📚 期末考试
        </button>
        <button class="type-btn {{examForm.type === 'midterm' ? 'active' : ''}}"
                bindtap="selectType"
                data-type="midterm">
          📖 期中考试
        </button>
        <button class="type-btn {{examForm.type === 'quiz' ? 'active' : ''}}"
                bindtap="selectType"
                data-type="quiz">
          📝 小测验
        </button>
        <button class="type-btn {{examForm.type === 'certificate' ? 'active' : ''}}"
                bindtap="selectType"
                data-type="certificate">
          🏆 资格考试
        </button>
        <button class="type-btn {{examForm.type === 'entrance' ? 'active' : ''}}"
                bindtap="selectType"
                data-type="entrance">
          🎓 入学考试
        </button>
        <button class="type-btn {{examForm.type === 'other' ? 'active' : ''}}"
                bindtap="selectType"
                data-type="other">
          📋 其他
        </button>
      </view>
    </view>

    <!-- 备注 -->
    <view class="form-group">
      <text class="form-label">备注</text>
      <textarea class="form-textarea"
                placeholder="可以添加考试相关的备注信息，如：考试范围、注意事项等"
                value="{{examForm.notes}}"
                bindinput="onNotesInput"
                maxlength="200"
                auto-height/>
      <text class="char-count">{{examForm.notes.length}}/200</text>
    </view>

    <!-- 提醒设置 -->
    <view class="form-group">
      <text class="form-label">考试提醒</text>
      <view class="reminder-options">
        <view class="reminder-item" wx:for="{{reminderOptions}}" wx:key="value">
          <checkbox-group bindchange="onReminderChange" data-value="{{item.value}}">
            <checkbox value="{{item.value}}" checked="{{item.checked}}"/>
          </checkbox-group>
          <text class="reminder-text">{{item.label}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="cancel-btn" bindtap="cancelAdd">取消</button>
    <button class="save-btn {{canSave ? 'enabled' : 'disabled'}}"
            bindtap="saveExam"
            disabled="{{!canSave}}">
      保存考试
    </button>
  </view>
</view>

<!-- 保存成功提示 -->
<view class="success-modal" wx:if="{{showSuccessModal}}">
  <view class="success-content">
    <text class="success-icon">✅</text>
    <text class="success-title">考试添加成功！</text>
    <text class="success-desc">已为您创建考试提醒</text>
    <button class="success-btn" bindtap="goToExamCenter">查看考试</button>
  </view>
</view>