{"pageInfo": {"pageName": "学习统计详情", "pageId": "statisticsDetail", "pageType": "page", "description": "各科目详细学习数据统计页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "header-top", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "16rpx"}, "children": [{"id": "subject-info", "type": "view", "children": [{"id": "subject-name", "type": "text", "content": "{{currentSubject.name}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "subject-description", "type": "text", "content": "{{currentSubject.description}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "time-range-selector", "type": "button", "content": "{{selectedTimeRange.label}}", "style": {"fontSize": "24rpx", "color": "#1890FF", "backgroundColor": "#E6F7FF", "border": "1rpx solid #91D5FF", "borderRadius": "6rpx", "padding": "6rpx 12rpx"}, "events": {"tap": "showTimeRangeSelector"}}]}, {"id": "overview-stats", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr 1fr", "gap": "16rpx"}, "children": [{"id": "overview-stat", "type": "view", "forEach": "{{overviewStats}}", "forItem": "stat", "forIndex": "index", "style": {"textAlign": "center", "backgroundColor": "{{stat.bgColor}}", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{stat.value}}", "style": {"fontSize": "24rpx", "fontWeight": "600", "color": "{{stat.color}}", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{stat.label}}", "style": {"fontSize": "20rpx", "color": "#666666"}}]}]}]}, {"id": "study-trend-chart", "type": "view", "className": "chart-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "chart-title", "type": "text", "content": "学习趋势", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "20rpx"}}, {"id": "chart-placeholder", "type": "view", "style": {"height": "300rpx", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "display": "flex", "alignItems": "center", "justifyContent": "center", "marginBottom": "16rpx"}, "children": [{"type": "text", "content": "📊 学习时长趋势图", "style": {"fontSize": "26rpx", "color": "#999999"}}]}, {"id": "chart-legend", "type": "view", "style": {"display": "flex", "justifyContent": "center", "gap": "24rpx"}, "children": [{"id": "legend-item", "type": "view", "forEach": "{{chartLegend}}", "forItem": "item", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "gap": "6rpx"}, "children": [{"type": "view", "style": {"width": "12rpx", "height": "12rpx", "borderRadius": "50%", "backgroundColor": "{{item.color}}"}}, {"type": "text", "content": "{{item.label}}", "style": {"fontSize": "22rpx", "color": "#666666"}}]}]}]}, {"id": "efficiency-analysis", "type": "view", "className": "efficiency-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "efficiency-title", "type": "text", "content": "效率分析", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "20rpx"}}, {"id": "efficiency-score", "type": "view", "style": {"textAlign": "center", "marginBottom": "24rpx"}, "children": [{"id": "efficiency-circle", "type": "view", "style": {"width": "120rpx", "height": "120rpx", "borderRadius": "50%", "border": "8rpx solid #F0F0F0", "borderTop": "8rpx solid #52C41A", "margin": "0 auto 16rpx", "position": "relative", "transform": "rotate({{efficiencyScore * 3.6}}deg)"}, "children": [{"id": "efficiency-text", "type": "text", "content": "{{efficiencyScore}}%", "style": {"position": "absolute", "top": "50%", "left": "50%", "transform": "translate(-50%, -50%) rotate(-{{efficiencyScore * 3.6}}deg)", "fontSize": "24rpx", "fontWeight": "600", "color": "#52C41A"}}]}, {"id": "efficiency-label", "type": "text", "content": "平均学习效率", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "efficiency-factors", "type": "view", "children": [{"id": "factor-item", "type": "view", "forEach": "{{efficiencyFactors}}", "forItem": "factor", "forIndex": "index", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "12rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "factor-name", "type": "text", "content": "{{factor.name}}", "style": {"fontSize": "26rpx", "color": "#333333"}}, {"id": "factor-value", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "8rpx"}, "children": [{"id": "factor-bar", "type": "view", "style": {"width": "80rpx", "height": "6rpx", "backgroundColor": "#F0F0F0", "borderRadius": "3rpx", "position": "relative"}, "children": [{"type": "view", "style": {"width": "{{factor.percentage}}%", "height": "100%", "backgroundColor": "{{factor.color}}", "borderRadius": "3rpx"}}]}, {"id": "factor-score", "type": "text", "content": "{{factor.score}}", "style": {"fontSize": "22rpx", "color": "#666666"}}]}]}]}]}, {"id": "time-distribution", "type": "view", "className": "distribution-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "distribution-title", "type": "text", "content": "时间分布", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "20rpx"}}, {"id": "time-periods", "type": "view", "children": [{"id": "period-item", "type": "view", "forEach": "{{timePeriods}}", "forItem": "period", "forIndex": "index", "style": {"marginBottom": "16rpx"}, "children": [{"id": "period-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "8rpx"}, "children": [{"id": "period-name", "type": "text", "content": "{{period.name}}", "style": {"fontSize": "24rpx", "color": "#333333"}}, {"id": "period-time", "type": "text", "content": "{{period.time}}", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "period-bar", "type": "view", "style": {"backgroundColor": "#F0F0F0", "borderRadius": "4rpx", "height": "8rpx", "position": "relative"}, "children": [{"type": "view", "style": {"width": "{{period.percentage}}%", "height": "100%", "backgroundColor": "{{period.color}}", "borderRadius": "4rpx"}}]}]}]}]}, {"id": "study-sessions", "type": "view", "className": "sessions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "sessions-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "20rpx"}, "children": [{"id": "sessions-title", "type": "text", "content": "最近学习记录", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333"}}, {"id": "view-all-btn", "type": "button", "content": "查看全部", "style": {"fontSize": "24rpx", "color": "#1890FF", "backgroundColor": "transparent", "border": "none"}, "events": {"tap": "viewAllSessions"}}]}, {"id": "sessions-list", "type": "view", "children": [{"id": "session-item", "type": "view", "forEach": "{{recentSessions}}", "forItem": "session", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "padding": "16rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "events": {"tap": "viewSessionDetail"}, "children": [{"id": "session-icon", "type": "view", "style": {"width": "48rpx", "height": "48rpx", "borderRadius": "50%", "backgroundColor": "{{session.typeColor}}", "display": "flex", "alignItems": "center", "justifyContent": "center", "marginRight": "16rpx"}, "children": [{"type": "text", "content": "{{session.typeIcon}}", "style": {"fontSize": "24rpx"}}]}, {"id": "session-info", "type": "view", "style": {"flex": "1"}, "children": [{"id": "session-title", "type": "text", "content": "{{session.title}}", "style": {"fontSize": "26rpx", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "session-meta", "type": "text", "content": "{{session.date}} · {{session.duration}}", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "session-efficiency", "type": "text", "content": "{{session.efficiency}}%", "style": {"fontSize": "22rpx", "color": "#52C41A", "fontWeight": "500"}}]}]}]}, {"id": "insights", "type": "view", "className": "insights-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "insights-title", "type": "text", "content": "💡 学习洞察", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "20rpx"}}, {"id": "insights-list", "type": "view", "children": [{"id": "insight-item", "type": "view", "forEach": "{{studyInsights}}", "forItem": "insight", "forIndex": "index", "style": {"backgroundColor": "{{insight.bgColor}}", "borderRadius": "8rpx", "padding": "16rpx", "marginBottom": "12rpx", "border": "1rpx solid {{insight.borderColor}}"}, "children": [{"id": "insight-header", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "8rpx", "marginBottom": "8rpx"}, "children": [{"id": "insight-icon", "type": "text", "content": "{{insight.icon}}", "style": {"fontSize": "20rpx"}}, {"id": "insight-title", "type": "text", "content": "{{insight.title}}", "style": {"fontSize": "24rpx", "fontWeight": "500", "color": "{{insight.titleColor}}"}}]}, {"id": "insight-content", "type": "text", "content": "{{insight.content}}", "style": {"fontSize": "22rpx", "color": "#666666", "lineHeight": "1.4"}}]}]}]}], "data": {"subjectId": "", "selectedTimeRange": {"value": "week", "label": "最近一周"}, "currentSubject": {"name": "数学", "description": "高等数学、线性代数、概率论"}, "efficiencyScore": 85, "overviewStats": [{"label": "总时长", "value": "28.5h", "color": "#1890FF", "bgColor": "#E6F7FF"}, {"label": "学习次数", "value": "42次", "color": "#52C41A", "bgColor": "#F6FFED"}, {"label": "平均效率", "value": "85%", "color": "#FA8C16", "bgColor": "#FFF2E8"}], "chartLegend": [{"label": "学习时长", "color": "#1890FF"}, {"label": "目标时长", "color": "#E9ECEF"}], "efficiencyFactors": [{"name": "专注度", "score": "92%", "percentage": 92, "color": "#52C41A"}, {"name": "完成度", "score": "88%", "percentage": 88, "color": "#1890FF"}, {"name": "时间利用", "score": "75%", "percentage": 75, "color": "#FA8C16"}], "timePeriods": [{"name": "上午 (6:00-12:00)", "time": "8.5h", "percentage": 30, "color": "#52C41A"}, {"name": "下午 (12:00-18:00)", "time": "12.0h", "percentage": 42, "color": "#1890FF"}, {"name": "晚上 (18:00-24:00)", "time": "8.0h", "percentage": 28, "color": "#FA8C16"}], "recentSessions": [{"id": "session_001", "title": "高数第一章复习", "date": "今天", "duration": "25分钟", "efficiency": 92, "typeIcon": "🍅", "typeColor": "#FFF2E8"}, {"id": "session_002", "title": "线代矩阵运算", "date": "昨天", "duration": "45分钟", "efficiency": 88, "typeIcon": "📚", "typeColor": "#E6F7FF"}, {"id": "session_003", "title": "概率论习题", "date": "前天", "duration": "30分钟", "efficiency": 76, "typeIcon": "✏️", "typeColor": "#F6FFED"}], "studyInsights": [{"icon": "🎯", "title": "最佳学习时段", "content": "你在下午2-4点的学习效率最高，建议安排重要内容在这个时间段。", "bgColor": "#E6F7FF", "borderColor": "#91D5FF", "titleColor": "#1890FF"}, {"icon": "📈", "title": "进步趋势", "content": "本周学习效率比上周提升了12%，保持这个节奏！", "bgColor": "#F6FFED", "borderColor": "#B7EB8F", "titleColor": "#52C41A"}, {"icon": "⚠️", "title": "注意事项", "content": "晚上学习时容易分心，建议减少电子设备干扰。", "bgColor": "#FFF2E8", "borderColor": "#FFD591", "titleColor": "#FA8C16"}]}, "methods": {"showTimeRangeSelector": {"type": "function", "description": "显示时间范围选择器", "implementation": "displayTimeRangeOptions"}, "viewAllSessions": {"type": "navigate", "url": "/pages/data-center/sessions", "params": ["subjectId"]}, "viewSessionDetail": {"type": "navigate", "url": "/pages/data-center/session-detail", "params": ["sessionId"]}}, "lifecycle": {"onLoad": ["loadStatisticsDetail"], "onShow": ["refreshStatisticsData"]}}