// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { userInfo, action = 'login' } = event

  try {
    switch (action) {
      case 'login':
        return await handleLogin(wxContext, userInfo)
      case 'updateProfile':
        return await updateUserProfile(wxContext, userInfo)
      case 'getUserInfo':
        return await getUserInfo(wxContext)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('登录云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 处理登录
async function handleLogin(wxContext, userInfo) {
  const openid = wxContext.OPENID
  const appid = wxContext.APPID
  const unionid = wxContext.UNIONID

  try {
    // 查询用户是否已存在
    const userQuery = await db.collection('kaoba')
      .where({
        _openid: openid,
        type: 'user'
      })
      .get()

    let user
    const now = new Date()

    if (userQuery.data.length === 0) {
      // 新用户，创建用户记录
      const newUser = {
        type: 'user',
        openid: openid,
        appid: appid,
        unionid: unionid,
        nickName: userInfo?.nickName || '微信用户',
        avatarUrl: userInfo?.avatarUrl || '',
        gender: userInfo?.gender || 0,
        country: userInfo?.country || '',
        province: userInfo?.province || '',
        city: userInfo?.city || '',
        language: userInfo?.language || 'zh_CN',
        isFirstLogin: true,
        loginCount: 1,
        lastLoginTime: now,
        createTime: now,
        updateTime: now
      }

      const result = await db.collection('kaoba').add({
        data: newUser
      })

      user = { _id: result._id, ...newUser }
      console.log('新用户注册:', openid)
    } else {
      // 老用户，更新登录信息
      user = userQuery.data[0]

      const updateData = {
        lastLoginTime: now,
        loginCount: (user.loginCount || 0) + 1,
        updateTime: now,
        isFirstLogin: false
      }

      // 如果提供了新的用户信息，更新用户资料
      if (userInfo) {
        if (userInfo.nickName) updateData.nickName = userInfo.nickName
        if (userInfo.avatarUrl) updateData.avatarUrl = userInfo.avatarUrl
        if (userInfo.gender !== undefined) updateData.gender = userInfo.gender
        if (userInfo.country) updateData.country = userInfo.country
        if (userInfo.province) updateData.province = userInfo.province
        if (userInfo.city) updateData.city = userInfo.city
        if (userInfo.language) updateData.language = userInfo.language
      }

      await db.collection('kaoba')
        .doc(user._id)
        .update({
          data: updateData
        })

      // 更新本地用户对象
      Object.assign(user, updateData)
      console.log('用户登录:', openid)
    }

    return {
      success: true,
      data: {
        openid: openid,
        appid: appid,
        unionid: unionid,
        user: user,
        isFirstLogin: user.isFirstLogin
      }
    }
  } catch (error) {
    console.error('登录处理失败:', error)
    return { success: false, error: error.message }
  }
}

// 更新用户资料
async function updateUserProfile(wxContext, userInfo) {
  const openid = wxContext.OPENID

  try {
    const updateData = {
      updateTime: new Date()
    }

    if (userInfo.nickName) updateData.nickName = userInfo.nickName
    if (userInfo.avatarUrl) updateData.avatarUrl = userInfo.avatarUrl
    if (userInfo.gender !== undefined) updateData.gender = userInfo.gender
    if (userInfo.country) updateData.country = userInfo.country
    if (userInfo.province) updateData.province = userInfo.province
    if (userInfo.city) updateData.city = userInfo.city
    if (userInfo.language) updateData.language = userInfo.language

    const result = await db.collection('kaoba')
      .where({
        _openid: openid,
        type: 'user'
      })
      .update({
        data: updateData
      })

    return { success: true, data: result }
  } catch (error) {
    console.error('更新用户资料失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取用户信息
async function getUserInfo(wxContext) {
  const openid = wxContext.OPENID

  try {
    const result = await db.collection('kaoba')
      .where({
        _openid: openid,
        type: 'user'
      })
      .get()

    if (result.data.length === 0) {
      return { success: false, error: '用户不存在' }
    }

    return { success: true, data: result.data[0] }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return { success: false, error: error.message }
  }
}
