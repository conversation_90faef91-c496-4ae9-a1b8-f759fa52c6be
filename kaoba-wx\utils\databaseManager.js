// 数据库管理工具
class DatabaseManager {
  // 检查数据库状态
  static async checkStatus() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'check' }
      })
      
      if (result.result && result.result.success) {
        return result.result
      } else {
        return { success: false, error: result.result?.error || '检查失败' }
      }
    } catch (error) {
      console.error('检查数据库状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 初始化数据库
  static async initDatabase() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'init' }
      })
      
      if (result.result && result.result.success) {
        return result.result
      } else {
        return { success: false, error: result.result?.error || '初始化失败' }
      }
    } catch (error) {
      console.error('初始化数据库失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 重置数据库
  static async resetDatabase() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'reset' }
      })
      
      if (result.result && result.result.success) {
        return result.result
      } else {
        return { success: false, error: result.result?.error || '重置失败' }
      }
    } catch (error) {
      console.error('重置数据库失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 获取数据库统计信息
  static async getStats() {
    try {
      const statusResult = await this.checkStatus()
      if (!statusResult.success) {
        return statusResult
      }

      const stats = statusResult.data
      
      return {
        success: true,
        data: {
          ...stats,
          summary: `总计 ${stats.total} 条记录`,
          breakdown: [
            `任务: ${stats.tasks} 条`,
            `考试: ${stats.exams} 条`,
            `学习会话: ${stats.studySessions} 条`,
            `番茄钟: ${stats.pomodoroSessions} 条`
          ]
        }
      }
    } catch (error) {
      console.error('获取数据库统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 检查云开发连接
  static async testConnection() {
    try {
      if (!wx.cloud) {
        return { success: false, error: '云开发未初始化' }
      }

      // 尝试调用云函数
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: { action: 'getUserInfo' }
      })

      return { 
        success: true, 
        message: '云开发连接正常',
        data: result
      }
    } catch (error) {
      console.error('云开发连接测试失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 获取完整的系统状态
  static async getSystemStatus() {
    try {
      const [connectionResult, statsResult] = await Promise.all([
        this.testConnection(),
        this.getStats()
      ])

      return {
        success: true,
        data: {
          connection: connectionResult,
          database: statsResult,
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('获取系统状态失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 导出数据（获取所有数据）
  static async exportData() {
    try {
      const db = wx.cloud.database()

      // 分别获取各个集合的数据
      const [usersResult, tasksResult, examsResult, studySessionsResult, pomodoroSessionsResult] = await Promise.all([
        db.collection('users').get(),
        db.collection('tasks').get(),
        db.collection('exams').get(),
        db.collection('study_sessions').get(),
        db.collection('pomodoro_sessions').get()
      ])

      const groupedData = {
        users: usersResult.data || [],
        tasks: tasksResult.data || [],
        exams: examsResult.data || [],
        studySessions: studySessionsResult.data || [],
        pomodoroSessions: pomodoroSessionsResult.data || []
      }

      const total = Object.values(groupedData).reduce((sum, arr) => sum + arr.length, 0)

      return {
        success: true,
        data: groupedData,
        total,
        exportTime: new Date().toISOString()
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 清理过期数据
  static async cleanupExpiredData(daysOld = 90) {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysOld)

      const db = wx.cloud.database()
      const _ = db.command

      // 删除过期的学习会话和番茄钟记录
      const deleteResults = await Promise.all([
        db.collection('study_sessions').where({ createTime: _.lt(cutoffDate) }).remove(),
        db.collection('pomodoro_sessions').where({ createTime: _.lt(cutoffDate) }).remove()
      ])

      const totalDeleted = deleteResults.reduce((sum, result) => sum + result.stats.removed, 0)

      return {
        success: true,
        message: `清理了 ${totalDeleted} 条过期记录`,
        deleted: totalDeleted
      }
    } catch (error) {
      console.error('清理过期数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 备份数据到本地存储
  static async backupToLocal() {
    try {
      const exportResult = await this.exportData()
      if (!exportResult.success) {
        return exportResult
      }

      // 保存到本地存储
      const backupData = {
        ...exportResult.data,
        backupTime: new Date().toISOString(),
        version: '1.0.0'
      }

      wx.setStorageSync('kaoba_backup_data', backupData)

      return {
        success: true,
        message: '数据已备份到本地存储',
        data: {
          total: exportResult.total,
          backupTime: backupData.backupTime
        }
      }
    } catch (error) {
      console.error('备份数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 从本地存储恢复数据
  static async restoreFromLocal() {
    try {
      const backupData = wx.getStorageSync('kaoba_backup_data')
      if (!backupData) {
        return { success: false, error: '没有找到备份数据' }
      }

      // 先重置数据库
      const resetResult = await this.resetDatabase()
      if (!resetResult.success) {
        return resetResult
      }

      // 恢复数据
      const db = wx.cloud.database()
      let restoredCount = 0

      // 恢复到对应的集合
      const collectionMap = {
        users: 'users',
        tasks: 'tasks',
        exams: 'exams',
        studySessions: 'study_sessions',
        pomodoroSessions: 'pomodoro_sessions'
      }

      for (const [type, items] of Object.entries(backupData)) {
        if (Array.isArray(items) && collectionMap[type]) {
          for (const item of items) {
            try {
              await db.collection(collectionMap[type]).add({ data: item })
              restoredCount++
            } catch (error) {
              console.error('恢复数据项失败:', error)
            }
          }
        }
      }

      return {
        success: true,
        message: `从备份恢复了 ${restoredCount} 条记录`,
        restored: restoredCount,
        backupTime: backupData.backupTime
      }
    } catch (error) {
      console.error('从备份恢复数据失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = DatabaseManager
