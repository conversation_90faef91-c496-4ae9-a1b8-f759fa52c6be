<!--pages/pomodoro/index.wxml-->
<view class="container">
  <!-- 计时器主容器 -->
  <view class="timer-main-container">
    <!-- 当前任务信息 -->
    <view class="session-info" wx:if="{{currentTask}}">
      <text class="task-name">{{currentTask.title}}</text>
      <text class="task-subject" wx:if="{{currentTask.subject}}">{{currentTask.subject}}</text>
    </view>

    <!-- 计时器显示 -->
    <view class="timer-display">
      <view class="timer-circle">
        <!-- 进度圆环 -->
        <view class="progress-circle" style="transform: rotate({{progressDegree}}deg); border-top-color: {{timerState === 'work' ? '#FF6B6B' : '#4ECDC4'}}"></view>

        <!-- 时间显示 -->
        <view class="timer-content">
          <text class="timer-time">{{displayTime}}</text>
          <text class="timer-label">{{timerStateText}}</text>
        </view>
      </view>
    </view>

    <!-- 控制按钮 -->
    <view class="timer-controls">
      <button class="control-btn secondary-btn" bindtap="resetTimer" wx:if="{{isRunning || isPaused}}">
        <text class="btn-icon">⏹️</text>
        <text class="btn-text">重置</text>
      </button>

      <button class="control-btn primary-btn {{isRunning ? 'pause' : 'start'}}" bindtap="toggleTimer">
        <text class="btn-icon">{{isRunning ? '⏸️' : '▶️'}}</text>
        <text class="btn-text">{{isRunning ? '暂停' : (isPaused ? '继续' : '开始')}}</text>
      </button>

      <button class="control-btn secondary-btn" bindtap="skipSession" wx:if="{{isRunning || isPaused}}">
        <text class="btn-icon">⏭️</text>
        <text class="btn-text">跳过</text>
      </button>
    </view>

    <!-- 会话统计 -->
    <view class="session-stats">
      <view class="stat-item">
        <text class="stat-value">{{completedSessions}}</text>
        <text class="stat-label">已完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{totalSessions}}</text>
        <text class="stat-label">总计划</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{todayFocus}}</text>
        <text class="stat-label">今日专注</text>
      </view>
    </view>
  </view>

  <!-- 设置区域 -->
  <view class="settings-container">
    <view class="settings-header">
      <text class="settings-title">专注设置</text>
      <text class="settings-toggle" bindtap="toggleSettings">{{showSettings ? '收起' : '展开'}}</text>
    </view>

    <view class="settings-content" wx:if="{{showSettings}}">
      <!-- 时间设置 -->
      <view class="setting-group">
        <text class="setting-title">时间设置</text>
        <view class="time-settings">
          <view class="time-setting-item">
            <text class="time-label">工作时间</text>
            <view class="time-picker">
              <button class="time-btn" bindtap="adjustTime" data-type="work" data-action="decrease">-</button>
              <text class="time-value">{{workDuration}}分钟</text>
              <button class="time-btn" bindtap="adjustTime" data-type="work" data-action="increase">+</button>
            </view>
          </view>
          <view class="time-setting-item">
            <text class="time-label">短休息</text>
            <view class="time-picker">
              <button class="time-btn" bindtap="adjustTime" data-type="shortBreak" data-action="decrease">-</button>
              <text class="time-value">{{shortBreakDuration}}分钟</text>
              <button class="time-btn" bindtap="adjustTime" data-type="shortBreak" data-action="increase">+</button>
            </view>
          </view>
          <view class="time-setting-item">
            <text class="time-label">长休息</text>
            <view class="time-picker">
              <button class="time-btn" bindtap="adjustTime" data-type="longBreak" data-action="decrease">-</button>
              <text class="time-value">{{longBreakDuration}}分钟</text>
              <button class="time-btn" bindtap="adjustTime" data-type="longBreak" data-action="increase">+</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 任务选择 -->
      <view class="setting-group">
        <text class="setting-title">选择任务</text>
        <view class="task-selector" bindtap="selectTask">
          <text class="selected-task">{{currentTask ? currentTask.title : '点击选择任务'}}</text>
          <text class="selector-arrow">›</text>
        </view>
      </view>

      <!-- 背景音效 -->
      <view class="setting-group">
        <text class="setting-title">背景音效</text>
        <view class="sound-options">
          <view class="sound-option {{selectedSound === item.value ? 'active' : ''}}"
                wx:for="{{soundOptions}}"
                wx:key="value"
                bindtap="selectSound"
                data-sound="{{item.value}}">
            <text class="sound-icon">{{item.icon}}</text>
            <text class="sound-name">{{item.name}}</text>
            <text class="sound-check" wx:if="{{selectedSound === item.value}}">✓</text>
          </view>
        </view>
      </view>

      <!-- 其他设置 -->
      <view class="setting-group">
        <text class="setting-title">其他设置</text>
        <view class="other-settings">
          <view class="setting-item">
            <text class="setting-name">自动开始休息</text>
            <switch class="setting-switch" checked="{{autoStartBreak}}" bindchange="toggleAutoStartBreak"/>
          </view>
          <view class="setting-item">
            <text class="setting-name">振动提醒</text>
            <switch class="setting-switch" checked="{{vibrationEnabled}}" bindchange="toggleVibration"/>
          </view>
          <view class="setting-item">
            <text class="setting-name">声音提醒</text>
            <switch class="setting-switch" checked="{{soundEnabled}}" bindchange="toggleSound"/>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日统计 -->
  <view class="today-stats-container">
    <view class="stats-header">
      <text class="stats-title">今日统计</text>
      <text class="view-detail" bindtap="viewDetailStats">查看详情</text>
    </view>
    <view class="stats-grid">
      <view class="stat-card" wx:for="{{todayStats}}" wx:key="label">
        <text class="stat-icon">{{item.icon}}</text>
        <text class="stat-value">{{item.value}}</text>
        <text class="stat-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 最近专注记录 -->
  <view class="recent-sessions-container" wx:if="{{recentSessions.length > 0}}">
    <view class="sessions-header">
      <text class="sessions-title">最近专注</text>
      <text class="view-all" bindtap="viewAllSessions">查看全部</text>
    </view>
    <view class="sessions-list">
      <view class="session-item" wx:for="{{recentSessions}}" wx:key="id">
        <view class="session-icon">
          <text>{{item.type === 'work' ? '🍅' : '☕'}}</text>
        </view>
        <view class="session-info">
          <text class="session-task">{{item.taskName || '自由专注'}}</text>
          <text class="session-time">{{item.duration}} · {{item.time}}</text>
        </view>
        <view class="session-status">
          <text class="status-text status-{{item.status}}">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 任务选择弹窗 -->
<view class="task-modal-mask" wx:if="{{showTaskModal}}" bindtap="hideTaskModal">
  <view class="task-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择任务</text>
      <text class="modal-close" bindtap="hideTaskModal">×</text>
    </view>
    <view class="modal-body">
      <view class="task-option no-task {{!currentTask ? 'selected' : ''}}" bindtap="selectTaskOption" data-task="">
        <text class="task-icon">🎯</text>
        <text class="task-name">自由专注</text>
        <text class="task-check" wx:if="{{!currentTask}}">✓</text>
      </view>
      <view class="task-option {{currentTask && currentTask.id === item.id ? 'selected' : ''}}"
            wx:for="{{availableTasks}}"
            wx:key="id"
            bindtap="selectTaskOption"
            data-task="{{item}}">
        <text class="task-icon">📝</text>
        <view class="task-content">
          <text class="task-name">{{item.title}}</text>
          <text class="task-subject" wx:if="{{item.subject}}">{{item.subject}}</text>
        </view>
        <text class="task-check" wx:if="{{currentTask && currentTask.id === item.id}}">✓</text>
      </view>
    </view>
  </view>
</view>