/* pages/exam-detail/index.wxss */

/* 考试头部 */
.exam-header-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.exam-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.exam-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.status-active {
  background-color: #52C41A;
}

.status-inactive {
  background-color: #999999;
}

.status-text {
  font-size: 24rpx;
  color: #666666;
}

.exam-actions {
  display: flex;
  gap: 12rpx;
}

.set-active-btn {
  background-color: #E6F7FF;
  color: #1890FF;
  border: 1rpx solid #91D5FF;
  border-radius: 6rpx;
  padding: 6rpx 12rpx;
  font-size: 24rpx;
}

.edit-btn, .more-btn {
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 6rpx;
  padding: 6rpx 12rpx;
  font-size: 24rpx;
}

.exam-title-section {
  margin-bottom: 16rpx;
}

.exam-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}

.exam-meta {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.exam-type {
  font-size: 20rpx;
  color: #1890FF;
  background-color: #E6F7FF;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.exam-importance {
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.importance-high {
  background-color: #FFF1F0;
  color: #FF4D4F;
}

.importance-medium {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.importance-low {
  background-color: #F6FFED;
  color: #52C41A;
}

.exam-description {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.description-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 倒计时 */
.countdown-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.exam-date {
  font-size: 24rpx;
  color: #666666;
}

.countdown-display {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.countdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  min-width: 80rpx;
}

.countdown-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #FF4D4F;
  line-height: 1;
}

.countdown-label {
  font-size: 20rpx;
  color: #666666;
  margin-top: 4rpx;
}

.countdown-separator {
  font-size: 32rpx;
  color: #999999;
  font-weight: 700;
}

.countdown-progress {
  text-align: center;
}

.progress-bar {
  background-color: #F0F0F0;
  border-radius: 8rpx;
  height: 8rpx;
  margin-bottom: 12rpx;
  overflow: hidden;
}

.progress-fill {
  background-color: #FF4D4F;
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666666;
}

/* 考试科目 */
.subjects-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.subjects-count {
  font-size: 24rpx;
  color: #666666;
}

.subjects-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.subject-item {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.subject-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.subject-score {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.target-score {
  font-size: 24rpx;
  color: #1890FF;
  font-weight: 600;
}

.total-score {
  font-size: 20rpx;
  color: #999999;
}

.subject-progress {
  margin-bottom: 16rpx;
}

.subject-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 20rpx;
  color: #999999;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

/* 准备进度 */
.preparation-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.overall-progress {
  font-size: 32rpx;
  font-weight: 700;
  color: #52C41A;
}

.progress-circle {
  display: flex;
  justify-content: center;
  margin: 32rpx 0;
}

.circle-progress {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-inner {
  width: 160rpx;
  height: 160rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #52C41A;
}

.progress-label {
  font-size: 20rpx;
  color: #666666;
}

.preparation-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.prep-stat-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.prep-stat-icon {
  font-size: 24rpx;
}

.prep-stat-content {
  flex: 1;
}

.prep-stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.prep-stat-label {
  font-size: 20rpx;
  color: #666666;
}

/* 相关任务 */
.related-tasks-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.view-all {
  font-size: 24rpx;
  color: #1890FF;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
}

.task-checkbox {
  flex-shrink: 0;
}

.checkbox-icon {
  font-size: 24rpx;
}

.task-content {
  flex: 1;
}

.task-title {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.task-title.completed {
  text-decoration: line-through;
  color: #999999;
}

.task-meta {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.task-subject {
  font-size: 18rpx;
  color: #1890FF;
  background-color: #E6F7FF;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

.task-due {
  font-size: 18rpx;
  color: #666666;
}

.task-priority {
  font-size: 16rpx;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

.priority-high {
  background-color: #FFF1F0;
  color: #FF4D4F;
}

.priority-medium {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.priority-low {
  background-color: #F6FFED;
  color: #52C41A;
}

.task-progress {
  flex-shrink: 0;
}

.empty-tasks {
  text-align: center;
  padding: 40rpx 0;
}

.empty-text {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.add-task-btn {
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}

/* 快捷操作 */
.quick-actions-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: #F8F9FA;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  transition: all 0.3s ease;
}

.quick-action-btn:active {
  background-color: #E9ECEF;
  transform: scale(0.98);
}

.add-task-btn {
  background-color: #E6F7FF;
  color: #1890FF;
}

.study-btn {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.reminder-btn {
  background-color: #F6FFED;
  color: #52C41A;
}

.share-btn {
  background-color: #F9F0FF;
  color: #722ED1;
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
}
