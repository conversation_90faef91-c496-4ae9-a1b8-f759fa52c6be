# "要考试啦"小程序 UI/UX 设计文档

## 概述

本目录包含"要考试啦"微信小程序MVP版本的完整UI/UX设计文档，采用JSON格式描述每个页面的界面结构、交互逻辑和数据绑定。

## 设计原则

### 1. 用户体验优先
- **简洁直观**：界面简洁，操作直观，降低学习成本
- **专注考试场景**：所有设计都围绕考试复习场景优化
- **快速响应**：关键操作提供即时反馈

### 2. 视觉设计
- **主色调**：#1890FF（专业蓝）
- **辅助色**：#52C41A（成功绿）、#FA8C16（警告橙）、#FF4D4F（错误红）
- **背景色**：#F5F5F5（浅灰）、#FFFFFF（白色）
- **文字色**：#333333（主文字）、#666666（次要文字）、#999999（辅助文字）

### 3. 交互设计
- **一致性**：相同功能的交互方式保持一致
- **可预测性**：用户能够预测操作结果
- **容错性**：提供撤销和确认机制

## 页面结构

### 核心页面（Tab页面）

#### 01. 首页 (home-page.json)
- **功能**：考试倒计时、今日任务、快捷操作
- **特点**：信息密度适中，突出重要信息
- **交互**：下拉刷新、快捷操作按钮

#### 02. 考试中心 (exam-center.json)
- **功能**：考试管理、考试模板、当前考试设置
- **特点**：卡片式布局，清晰的信息层次
- **交互**：考试状态切换、模板选择

#### 03. 任务中心 (task-center.json)
- **功能**：任务列表、任务筛选、进度跟踪
- **特点**：列表式布局，支持多种筛选方式
- **交互**：任务状态切换、滑动操作

#### 04. 番茄钟 (pomodoro.json)
- **功能**：专注计时、背景音效、统计数据
- **特点**：大字体显示，圆形进度条
- **交互**：一键开始/暂停、设置调整

#### 05. 数据中心 (data-center.json)
- **功能**：学习统计、效率分析、准备度评估
- **特点**：图表展示，数据可视化
- **交互**：时间周期切换、详情查看

#### 06. 我的 (profile.json)
- **功能**：个人信息、设置、帮助反馈
- **特点**：功能分组，层次清晰
- **交互**：设置跳转、数据分享

### 功能页面

#### 07. 添加考试 (add-exam.json)
- **功能**：考试信息录入、模板选择、科目设置
- **特点**：表单式布局，智能提示
- **交互**：模板应用、表单验证

#### 08. 添加任务 (add-task.json)
- **功能**：任务信息录入、模板选择、子任务管理
- **特点**：分步骤填写，模板快速创建
- **交互**：模板应用、动态表单

#### 09. 底部导航 (tab-navigation.json)
- **功能**：页面切换、徽章提示、悬浮按钮
- **特点**：固定底部，图标+文字
- **交互**：Tab切换动画、徽章更新

#### 10. 编辑考试 (edit-exam.json)
- **功能**：修改考试信息、删除考试
- **特点**：表单编辑，危险操作区分
- **交互**：表单验证、确认删除

#### 11. 编辑任务 (edit-task.json)
- **功能**：修改任务信息、任务统计、删除任务
- **特点**：状态切换、数据展示
- **交互**：实时更新、统计查看

#### 12. 设置 (settings.json)
- **功能**：通知设置、学习设置、外观设置、数据管理
- **特点**：分类清晰、开关控制
- **交互**：设置切换、数据操作

#### 13. 帮助与反馈 (help-feedback.json)
- **功能**：快速帮助、常见问题、意见反馈、联系方式
- **特点**：FAQ展开、反馈表单
- **交互**：问题展开、表单提交

### 组件

#### 14. 启动页 (loading-splash.json)
- **功能**：应用启动、加载进度、新手引导
- **特点**：品牌展示、进度指示
- **交互**：加载动画、引导流程

#### 15. 弹窗组件 (modal-components.json)
- **功能**：确认对话框、提示框、Toast、加载框、操作菜单、分享菜单
- **特点**：通用组件、统一样式
- **交互**：模态显示、动画效果

#### 16. 任务详情 (task-detail.json)
- **功能**：任务详细信息、子任务管理、学习统计、完成操作
- **特点**：信息完整、操作便捷
- **交互**：状态切换、统计查看

#### 17. 考试详情 (exam-detail.json)
- **功能**：考试信息、倒计时、科目进度、学习计划、备考统计
- **特点**：信息丰富、进度可视化
- **交互**：计划管理、进度跟踪

#### 18. 专注完成 (pomodoro-complete.json)
- **功能**：完成庆祝、效果评分、休息建议、进度统计、继续操作
- **特点**：正向反馈、用户激励
- **交互**：评分反馈、操作引导

#### 19. 搜索 (search.json)
- **功能**：全局搜索、筛选器、最近搜索、热门搜索、结果展示
- **特点**：搜索便捷、结果准确
- **交互**：实时搜索、结果跳转

#### 20. 通知中心 (notification-center.json)
- **功能**：通知列表、分类筛选、已读管理、操作响应
- **特点**：分类清晰、操作便捷
- **交互**：筛选切换、批量操作

#### 21. 新手引导 (onboarding-guide.json)
- **功能**：首次使用引导、功能介绍、快速设置
- **特点**：分步引导、视觉吸引
- **交互**：滑动切换、跳过选项

#### 22. 错误页面 (error-pages.json)
- **功能**：网络错误、数据错误、404页面、权限错误、维护模式、空状态
- **特点**：友好提示、操作引导
- **交互**：重试操作、返回导航

#### 23. 快捷操作 (quick-actions.json)
- **功能**：悬浮按钮、快捷面板、上下文菜单、最近操作
- **特点**：操作便捷、快速访问
- **交互**：展开收起、菜单选择

#### 24. 数据导出 (data-export.json)
- **功能**：数据选择、格式选择、时间范围、导出预览、文件生成
- **特点**：选项丰富、预览清晰
- **交互**：多选操作、格式切换

#### 25. 成就系统 (achievement-system.json)
- **功能**：成就展示、分类浏览、进度跟踪、奖励系统
- **特点**：激励机制、进度可视化
- **交互**：分类切换、详情查看

#### 26. 学习记录详情 (study-session-detail.json)
- **功能**：学习会话详情、时间线、笔记记录、中断统计、成就展示
- **特点**：信息详尽、数据完整
- **交互**：重复学习、分享记录

#### 27. 学习统计详情 (statistics-detail.json)
- **功能**：科目详细统计、趋势图表、效率分析、时间分布、学习洞察
- **特点**：数据可视化、智能分析
- **交互**：时间范围切换、会话查看

#### 28. 任务完成 (task-completion.json)
- **功能**：完成庆祝、学习反思、难度评价、满意度评分、后续建议
- **特点**：正向激励、反思总结
- **交互**：评分反馈、建议选择

#### 29. 考试提醒 (exam-reminder.json)
- **功能**：紧急提醒、倒计时、准备状态、科目进度、紧急任务、备考建议
- **特点**：紧迫感设计、状态评估
- **交互**：立即学习、延后提醒

## JSON结构说明

### 页面信息 (pageInfo)
```json
{
  "pageName": "页面名称",
  "pageId": "页面ID",
  "pageType": "页面类型",
  "description": "页面描述",
  "version": "版本号",
  "lastModified": "最后修改时间"
}
```

### 布局 (layout)
```json
{
  "type": "布局类型",
  "direction": "布局方向",
  "backgroundColor": "背景色",
  "padding": "内边距"
}
```

### 组件 (components)
```json
{
  "id": "组件ID",
  "type": "组件类型",
  "className": "样式类名",
  "style": {}, // 样式对象
  "children": [], // 子组件
  "events": {}, // 事件绑定
  "condition": "显示条件",
  "forEach": "循环数据",
  "forItem": "循环项变量",
  "forIndex": "循环索引变量"
}
```

### 数据 (data)
```json
{
  "变量名": "变量值",
  // 页面所需的所有数据
}
```

### 方法 (methods)
```json
{
  "方法名": {
    "type": "方法类型",
    "description": "方法描述",
    "params": ["参数列表"],
    "implementation": "实现方式"
  }
}
```

### 生命周期 (lifecycle)
```json
{
  "onLoad": ["页面加载时执行的方法"],
  "onShow": ["页面显示时执行的方法"],
  "onHide": ["页面隐藏时执行的方法"]
}
```

### 交互 (interactions)
```json
{
  "pullToRefresh": {
    "enabled": true,
    "action": "下拉刷新执行的方法"
  },
  "scrollToTop": {
    "enabled": true,
    "threshold": 400
  }
}
```

## 设计特色

### 1. 考试场景优化
- **考试倒计时**：醒目的倒计时显示，营造紧迫感
- **准备度评估**：科学的准备程度评分系统
- **考试类型模板**：针对不同考试类型的专门设计

### 2. 科学理论支撑
- **番茄工作法**：25分钟专注+5分钟休息的经典模式
- **任务分解**：支持子任务，便于大任务的分解管理
- **数据分析**：基于学习数据的效率分析和建议

### 3. 本土化设计
- **中国考试类型**：考研、高考、四六级、公务员等
- **使用习惯**：符合中国用户的操作习惯
- **激励机制**：适合中国学生的激励方式

### 4. 渐进式体验
- **新手引导**：首次使用时的引导流程
- **模板系统**：快速上手的模板机制
- **智能建议**：基于使用数据的个性化建议

## 响应式设计

### 屏幕适配
- **基准尺寸**：iPhone 6/7/8 (375px)
- **单位使用**：rpx（响应式像素）
- **字体大小**：24rpx-48rpx范围
- **间距规范**：8rpx的倍数

### 组件尺寸
- **按钮高度**：最小88rpx（符合触摸标准）
- **输入框高度**：最小80rpx
- **列表项高度**：最小120rpx
- **图标尺寸**：24rpx、32rpx、48rpx

## 无障碍设计

### 可访问性
- **语义化标签**：使用正确的HTML语义
- **颜色对比度**：确保足够的对比度
- **字体大小**：支持系统字体缩放
- **触摸目标**：最小44px的触摸区域

### 辅助功能
- **屏幕阅读器**：支持VoiceOver等
- **键盘导航**：支持键盘操作
- **高对比度**：支持高对比度模式

## 性能优化

### 加载优化
- **图片懒加载**：非关键图片延迟加载
- **组件按需加载**：减少初始包大小
- **数据缓存**：合理使用本地缓存

### 交互优化
- **防抖处理**：防止重复点击
- **加载状态**：提供加载反馈
- **错误处理**：友好的错误提示

## 开发指南

### 实现建议
1. **组件化开发**：将JSON结构转换为可复用组件
2. **状态管理**：使用适当的状态管理方案
3. **数据绑定**：实现双向数据绑定
4. **事件处理**：统一的事件处理机制

### 技术栈建议
- **框架**：微信小程序原生框架或Taro
- **状态管理**：MobX或Redux
- **UI库**：Vant Weapp或自定义组件库
- **工具**：ESLint、Prettier、Husky

### 测试建议
- **单元测试**：组件和方法的单元测试
- **集成测试**：页面流程的集成测试
- **用户测试**：真实用户的可用性测试
- **性能测试**：加载速度和响应时间测试

## 更新日志

### v1.0.0 (2025-06-27)
- 完成MVP版本的完整UI/UX设计
- 包含6个核心Tab页面和23个功能页面/组件
- 实现完整的页面流程和交互设计
- 建立完整的设计规范和开发指南
- 核心功能页面：
  - 编辑考试页面 (edit-exam.json)
  - 编辑任务页面 (edit-task.json)
  - 设置页面 (settings.json)
  - 帮助与反馈页面 (help-feedback.json)
- 详情页面：
  - 任务详情页面 (task-detail.json)
  - 考试详情页面 (exam-detail.json)
  - 专注完成页面 (pomodoro-complete.json)
  - 学习记录详情页面 (study-session-detail.json)
  - 学习统计详情页面 (statistics-detail.json)
- 功能页面：
  - 搜索页面 (search.json)
  - 通知中心页面 (notification-center.json)
  - 数据导出页面 (data-export.json)
  - 成就系统页面 (achievement-system.json)
- 完成流程页面：
  - 任务完成页面 (task-completion.json)
  - 考试提醒页面 (exam-reminder.json)
- 引导和错误处理：
  - 新手引导页面 (onboarding-guide.json)
  - 错误页面组件 (error-pages.json)
- 交互组件：
  - 快捷操作组件 (quick-actions.json)
- 系统组件：
  - 启动页组件 (loading-splash.json)
  - 弹窗组件库 (modal-components.json)

## 📊 设计统计

- **总页面数**：29个
- **核心Tab页面**：6个
- **功能页面**：21个
- **通用组件**：2个
- **设计文件大小**：约5.1MB
- **设计规范**：完整的颜色、字体、间距规范

## 后续规划

### v1.1.0
- 完善数据可视化图表
- 优化交互动画效果
- 添加更多任务模板
- 增强无障碍支持

### v2.0.0
- 增加社交功能页面
- 实现AI智能推荐界面
- 添加学习社区功能
- 支持多人协作学习

---

本设计文档为"要考试啦"小程序的完整UI/UX设计方案，为开发团队提供详细的实现指导。所有设计都基于用户需求分析和竞品分析，确保产品的可用性和竞争力。
