/* pages/data-export/index.wxss */

/* 页面头部 */
.header-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666666;
  display: block;
}

/* 通用容器 */
.export-options-container,
.export-formats-container,
.date-range-container,
.export-preview-container,
.export-history-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
  display: block;
}

/* 导出选项 */
.data-types {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.data-type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.data-type-item:last-child {
  border-bottom: none;
}

.data-type-info {
  flex: 1;
}

.data-type-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
  display: block;
}

.data-type-description {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 4rpx;
  display: block;
}

.data-type-count {
  font-size: 22rpx;
  color: #1890FF;
  display: block;
}

/* 导出格式 */
.format-options {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background-color: #FFFFFF;
  border: 1rpx solid #D9D9D9;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  font-size: 26rpx;
  color: #333333;
  transition: all 0.3s ease;
}

.format-option.active {
  background-color: #E6F7FF;
  border-color: #1890FF;
  color: #1890FF;
}

.format-icon {
  font-size: 24rpx;
}

.format-name {
  font-size: 26rpx;
}

.format-description {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.format-desc-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

/* 时间范围 */
.date-range-options {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  margin-bottom: 24rpx;
}

.date-range-option {
  background-color: #FFFFFF;
  border: 1rpx solid #D9D9D9;
  border-radius: 6rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #333333;
  transition: all 0.3s ease;
}

.date-range-option.active {
  background-color: #E6F7FF;
  border-color: #1890FF;
  color: #1890FF;
}

.custom-date-range {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.date-picker {
  flex: 1;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 12rpx;
  border: 1rpx solid #E9ECEF;
}

.date-label {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 4rpx;
  display: block;
}

.date-value {
  font-size: 26rpx;
  color: #333333;
  display: block;
}

.date-separator {
  font-size: 24rpx;
  color: #666666;
}

/* 导出预览 */
.preview-stats {
  display: flex;
  justify-content: space-around;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
}

.preview-stat {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  margin-bottom: 4rpx;
  display: block;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
  display: block;
}

.preview-content {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.preview-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.preview-items {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.preview-item {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.4;
}

/* 导出历史 */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.clear-history-btn {
  background-color: transparent;
  color: #999999;
  border: none;
  font-size: 24rpx;
  padding: 0;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.history-info {
  flex: 1;
}

.history-name {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.history-details {
  font-size: 22rpx;
  color: #666666;
  display: block;
}

.history-actions {
  display: flex;
  gap: 8rpx;
}

.history-action-btn {
  background-color: #FFFFFF;
  border: 1rpx solid #E9ECEF;
  border-radius: 6rpx;
  padding: 8rpx;
  font-size: 20rpx;
  width: 48rpx;
  height: 48rpx;
}

.action-icon {
  font-size: 20rpx;
}

/* 导出按钮 */
.export-actions {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  position: sticky;
  bottom: 0;
  border-top: 1rpx solid #F0F0F0;
}

.preview-btn {
  flex: 1;
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.export-btn {
  flex: 2;
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.export-btn:disabled {
  background-color: #F5F5F5;
  color: #999999;
}

/* 弹窗样式 */
.export-modal,
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-content.large {
  max-width: 90vw;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.modal-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 导出进度 */
.export-progress {
  text-align: center;
  padding: 40rpx 0;
}

.progress-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: conic-gradient(#1890FF 0deg, #F0F0F0 0deg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 80rpx;
  height: 80rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  position: absolute;
}

.progress-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #1890FF;
  position: relative;
  z-index: 1;
}

.progress-status {
  font-size: 26rpx;
  color: #666666;
  display: block;
}

/* 导出结果 */
.export-result {
  text-align: center;
  padding: 20rpx 0;
}

.result-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  display: block;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #52C41A;
  margin-bottom: 16rpx;
  display: block;
}

.result-details {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
  display: block;
}

.result-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
}

.result-action-btn {
  flex: 1;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 26rpx;
  border: none;
}

.result-action-btn.primary {
  background-color: #1890FF;
  color: #FFFFFF;
}

.result-action-btn.secondary {
  background-color: #F5F5F5;
  color: #666666;
}

/* 数据预览 */
.preview-tabs {
  display: flex;
  gap: 8rpx;
  margin-bottom: 24rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.preview-tab {
  background-color: transparent;
  color: #666666;
  border: none;
  border-bottom: 2rpx solid transparent;
  padding: 12rpx 16rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.preview-tab.active {
  color: #1890FF;
  border-bottom-color: #1890FF;
}

.preview-data {
  height: 400rpx;
}

.data-scroll {
  height: 100%;
}

.data-table {
  width: 100%;
  border: 1rpx solid #F0F0F0;
  border-radius: 8rpx;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: #F8F9FA;
  border-bottom: 1rpx solid #F0F0F0;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #F0F0F0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 12rpx 8rpx;
  font-size: 22rpx;
  color: #333333;
  border-right: 1rpx solid #F0F0F0;
  word-break: break-all;
  min-width: 0;
}

.table-cell:last-child {
  border-right: none;
}

.table-header .table-cell {
  font-weight: 500;
  background-color: #F8F9FA;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;
}

.no-data-text {
  font-size: 26rpx;
  color: #999999;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
