# 考霸小程序部署指南

本指南将帮助您快速部署考霸小程序，包括云函数部署和数据库自动初始化。

## 🚀 快速部署

### 1. 环境准备
- ✅ 微信开发者工具（最新版）
- ✅ 微信小程序基础库 2.2.3+
- ✅ 微信云开发环境

### 2. 项目配置

#### 2.1 打开项目
1. 在微信开发者工具中打开项目
2. 确保项目ID和云开发环境ID正确配置

#### 2.2 配置云开发环境
在 `app.js` 中确认云开发环境ID：
```javascript
globalData: {
  cloudEnv: 'cloud1-8gvo8etxe4c004b3' // 您的云开发环境ID
}
```

### 3. 云函数部署

**⚠️ 重要：必须按以下顺序部署云函数**

#### 3.1 首先部署数据库初始化云函数
```
右键 cloudfunctions/initDatabase/ → 上传并部署：云端安装依赖
```
等待部署完成（约1-2分钟）

#### 3.2 然后部署其他云函数
```
右键 cloudfunctions/login/ → 上传并部署：云端安装依赖
右键 cloudfunctions/taskManager/ → 上传并部署：云端安装依赖
右键 cloudfunctions/examManager/ → 上传并部署：云端安装依赖
右键 cloudfunctions/studyManager/ → 上传并部署：云端安装依赖
```

### 4. 验证部署

#### 4.1 检查云函数状态
1. 在微信开发者工具中打开"云开发控制台"
2. 进入"云函数"页面
3. 确认所有5个云函数都显示为"部署成功"状态

#### 4.2 启动应用测试
1. 点击"编译"启动小程序
2. 查看控制台输出，应该看到：
   ```
   云开发初始化成功
   开始初始化云数据库...
   云数据库初始化成功: 数据库初始化完成
   ```

## 📊 自动初始化功能

### 数据库自动创建
应用启动时会自动：
1. **检查数据库状态** - 调用 `initDatabase` 云函数
2. **创建数据集合** - 自动创建 `kaoba` 集合
3. **写入示例数据** - 包括任务、考试、学习记录等
4. **跳过重复初始化** - 如果数据已存在，跳过初始化

### 示例数据内容
- **3个示例任务**: 数学复习、英语单词、政治学习
- **2个示例考试**: 高等数学期末考试、大学英语四级
- **2个学习会话**: 数学和英语的学习记录
- **2个番茄钟记录**: 完成的专注学习时间

## 🔧 故障排除

### 常见问题

#### 1. 云函数部署失败
**问题**: 云函数上传失败或超时
**解决方案**:
- 检查网络连接
- 重试部署
- 确认云开发环境状态正常

#### 2. 数据库初始化失败
**问题**: 控制台显示"云数据库初始化失败"
**解决方案**:
- 确认 `initDatabase` 云函数已正确部署
- 检查云开发环境权限
- 查看云函数日志排查具体错误

#### 3. 应用启动后显示空白
**问题**: 首页没有数据显示
**解决方案**:
- 检查控制台是否有错误信息
- 确认所有云函数都已部署
- 尝试重新编译应用

#### 4. 登录功能异常
**问题**: 无法登录或登录后没有用户信息
**解决方案**:
- 确认 `login` 云函数已部署
- 检查小程序的用户授权设置
- 查看控制台登录相关日志

### 调试技巧

#### 1. 查看控制台日志
在微信开发者工具的控制台中查看详细的初始化日志：
```
云开发初始化成功
开始初始化云数据库...
创建示例任务: 数学高数第一章复习
创建示例任务: 英语单词背诵
...
云数据库初始化成功
```

#### 2. 手动重置数据库
如果需要重新初始化数据库，可以使用：
```javascript
const DatabaseManager = require('./utils/databaseManager')
await DatabaseManager.resetDatabase()
```

#### 3. 检查数据库状态
```javascript
const DatabaseManager = require('./utils/databaseManager')
const status = await DatabaseManager.getSystemStatus()
console.log(status)
```

## 📱 测试功能

### 基础功能测试
1. **登录功能** - 测试微信授权登录
2. **任务管理** - 创建、编辑、完成任务
3. **考试管理** - 添加考试、查看倒计时
4. **番茄钟** - 启动计时器、记录学习时间
5. **数据统计** - 查看学习报告和统计

### 数据同步测试
1. 在一个设备上添加数据
2. 在另一个设备上登录同一账号
3. 验证数据是否正确同步

## 🎉 部署完成

恭喜！您已经成功部署了考霸小程序。现在您可以：

- ✅ 使用完整的学习管理功能
- ✅ 享受云端数据同步
- ✅ 体验智能的学习统计
- ✅ 使用番茄钟专注学习

如果遇到任何问题，请参考故障排除部分或查看详细的开发文档。

---

**考霸** - 让学习更高效！ 📚✨
