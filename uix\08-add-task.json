{"pageInfo": {"pageName": "添加任务", "pageId": "addTask", "pageType": "page", "description": "添加新任务页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "page-title", "type": "text", "content": "添加任务", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "page-subtitle", "type": "text", "content": "制定你的复习计划", "style": {"fontSize": "28rpx", "color": "#666666"}}]}, {"id": "task-templates", "type": "view", "condition": "{{!selectedTemplate}}", "className": "templates-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "templates-title", "type": "text", "content": "快速模板", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "templates-subtitle", "type": "text", "content": "选择模板快速创建任务", "style": {"fontSize": "26rpx", "color": "#666666", "marginBottom": "24rpx"}}, {"id": "templates-list", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "12rpx"}, "children": [{"id": "template-item", "type": "button", "forEach": "{{taskTemplates}}", "forItem": "template", "forIndex": "index", "style": {"backgroundColor": "#F8F9FA", "border": "1rpx solid #E9ECEF", "borderRadius": "8rpx", "padding": "16rpx 20rpx", "textAlign": "left", "display": "flex", "alignItems": "center", "gap": "12rpx"}, "events": {"tap": "selectTemplate"}, "children": [{"type": "text", "content": "{{template.icon}}", "style": {"fontSize": "24rpx"}}, {"id": "template-info", "type": "view", "style": {"flex": "1"}, "children": [{"type": "text", "content": "{{template.name}}", "style": {"fontSize": "28rpx", "color": "#333333", "fontWeight": "500", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{template.description}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"type": "text", "content": "{{template.duration}}分钟", "style": {"fontSize": "22rpx", "color": "#999999"}}]}]}]}, {"id": "task-form", "type": "view", "className": "task-form-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "task-title-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "task-title-label", "type": "text", "content": "任务标题 *", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "task-title-input", "type": "input", "placeholder": "请输入任务标题，如：数学高数第一章复习", "value": "{{formData.title}}", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "28rpx", "border": "1rpx solid #E9ECEF"}, "events": {"input": "updateTitle"}}]}, {"id": "task-subject-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "task-subject-label", "type": "text", "content": "所属科目", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "task-subject-selector", "type": "picker", "range": "{{availableSubjects}}", "rangeKey": "name", "value": "{{formData.subjectIndex}}", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "border": "1rpx solid #E9ECEF"}, "events": {"change": "selectSubject"}, "children": [{"type": "text", "content": "{{selectedSubject ? selectedSubject.name : '请选择科目'}}", "style": {"fontSize": "28rpx", "color": "{{selectedSubject ? '#333333' : '#999999'}}"}}]}]}, {"id": "task-description-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "description-label", "type": "text", "content": "任务描述", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "description-textarea", "type": "textarea", "placeholder": "请输入任务的详细描述，如：复习内容、学习目标等", "value": "{{formData.description}}", "maxlength": "200", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "28rpx", "border": "1rpx solid #E9ECEF", "minHeight": "120rpx"}, "events": {"input": "updateDescription"}}]}, {"id": "task-priority-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "priority-label", "type": "text", "content": "优先级", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "priority-options", "type": "view", "style": {"display": "flex", "gap": "12rpx"}, "children": [{"id": "priority-option", "type": "button", "forEach": "{{priorityOptions}}", "forItem": "option", "forIndex": "index", "content": "{{option.label}}", "style": {"fontSize": "26rpx", "padding": "12rpx 20rpx", "borderRadius": "8rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{formData.priority === option.value ? option.color : '#FFFFFF'}}", "color": "{{formData.priority === option.value ? '#FFFFFF' : '#333333'}}"}, "events": {"tap": "selectPriority"}}]}]}, {"id": "task-duration-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "duration-label", "type": "text", "content": "预计时长", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "duration-options", "type": "view", "style": {"display": "flex", "gap": "12rpx", "flexWrap": "wrap"}, "children": [{"id": "duration-option", "type": "button", "forEach": "{{durationOptions}}", "forItem": "option", "forIndex": "index", "content": "{{option.label}}", "style": {"fontSize": "26rpx", "padding": "12rpx 20rpx", "borderRadius": "8rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{formData.duration === option.value ? '#1890FF' : '#FFFFFF'}}", "color": "{{formData.duration === option.value ? '#FFFFFF' : '#333333'}}"}, "events": {"tap": "selectDuration"}}]}]}, {"id": "task-due-date-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "due-date-label", "type": "text", "content": "截止日期", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "due-date-picker", "type": "picker", "mode": "date", "value": "{{formData.dueDate}}", "start": "{{minDate}}", "end": "{{maxDate}}", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "border": "1rpx solid #E9ECEF"}, "events": {"change": "selectDueDate"}, "children": [{"type": "text", "content": "{{formData.dueDate || '请选择截止日期'}}", "style": {"fontSize": "28rpx", "color": "{{formData.dueDate ? '#333333' : '#999999'}}"}}]}]}, {"id": "task-subtasks-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "subtasks-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "12rpx"}, "children": [{"id": "subtasks-label", "type": "text", "content": "子任务", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333"}}, {"id": "add-subtask-btn", "type": "button", "content": "+ 添加", "style": {"fontSize": "24rpx", "color": "#1890FF", "backgroundColor": "transparent", "border": "none", "padding": "0"}, "events": {"tap": "addSubtask"}}]}, {"id": "subtasks-list", "type": "view", "condition": "{{formData.subtasks.length > 0}}", "children": [{"id": "subtask-item", "type": "view", "forEach": "{{formData.subtasks}}", "forItem": "subtask", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "gap": "12rpx", "marginBottom": "12rpx", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "12rpx"}, "children": [{"id": "subtask-input", "type": "input", "placeholder": "请输入子任务内容", "value": "{{subtask.title}}", "style": {"flex": "1", "fontSize": "26rpx", "backgroundColor": "transparent", "border": "none"}, "events": {"input": "updateSubtask"}}, {"id": "remove-subtask-btn", "type": "button", "content": "×", "style": {"fontSize": "24rpx", "color": "#FF4D4F", "backgroundColor": "transparent", "border": "none", "padding": "0", "width": "32rpx", "height": "32rpx"}, "events": {"tap": "removeSubtask"}}]}]}]}]}, {"id": "form-actions", "type": "view", "className": "form-actions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "action-buttons", "type": "view", "style": {"display": "flex", "gap": "16rpx"}, "children": [{"id": "cancel-btn", "type": "button", "content": "取消", "style": {"flex": "1", "fontSize": "28rpx", "color": "#666666", "backgroundColor": "#F5F5F5", "borderRadius": "8rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "cancelForm"}}, {"id": "save-btn", "type": "button", "content": "保存", "disabled": "{{!isFormValid}}", "style": {"flex": "2", "fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "{{isFormValid ? '#1890FF' : '#D9D9D9'}}", "borderRadius": "8rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "saveTask"}}]}, {"id": "quick-actions", "type": "view", "style": {"marginTop": "20rpx", "display": "flex", "justifyContent": "center", "gap": "16rpx"}, "children": [{"id": "save-and-start-btn", "type": "button", "content": "保存并开始", "condition": "{{isFormValid}}", "style": {"fontSize": "26rpx", "color": "#52C41A", "backgroundColor": "transparent", "border": "1rpx solid #52C41A", "borderRadius": "8rpx", "padding": "8rpx 16rpx"}, "events": {"tap": "saveAndStart"}}]}]}], "data": {"formData": {"title": "", "subjectIndex": 0, "description": "", "priority": "medium", "duration": 60, "dueDate": "", "subtasks": []}, "selectedTemplate": null, "selectedSubject": null, "isFormValid": false, "minDate": "2025-06-27", "maxDate": "2030-12-31", "availableSubjects": [{"id": "math", "name": "数学"}, {"id": "english", "name": "英语"}, {"id": "politics", "name": "政治"}, {"id": "professional", "name": "专业课"}, {"id": "chinese", "name": "语文"}, {"id": "physics", "name": "物理"}, {"id": "chemistry", "name": "化学"}, {"id": "biology", "name": "生物"}, {"id": "history", "name": "历史"}, {"id": "geography", "name": "地理"}, {"id": "other", "name": "其他"}], "priorityOptions": [{"value": "low", "label": "低", "color": "#52C41A"}, {"value": "medium", "label": "中", "color": "#FAAD14"}, {"value": "high", "label": "高", "color": "#FF4D4F"}], "durationOptions": [{"value": 30, "label": "30分钟"}, {"value": 60, "label": "1小时"}, {"value": 90, "label": "1.5小时"}, {"value": 120, "label": "2小时"}, {"value": 180, "label": "3小时"}], "taskTemplates": [{"id": "math_practice", "name": "数学练习题", "icon": "📊", "description": "数学题目练习和解题训练", "duration": 90, "data": {"title": "数学练习题", "subject": "math", "priority": "high", "duration": 90}}, {"id": "english_reading", "name": "英语阅读理解", "icon": "📖", "description": "英语阅读理解训练", "duration": 60, "data": {"title": "英语阅读理解", "subject": "english", "priority": "medium", "duration": 60}}, {"id": "vocabulary", "name": "单词背诵", "icon": "📝", "description": "英语单词记忆和复习", "duration": 45, "data": {"title": "英语单词背诵", "subject": "english", "priority": "medium", "duration": 45, "subtasks": [{"title": "新单词学习", "completed": false}, {"title": "旧单词复习", "completed": false}, {"title": "单词测试", "completed": false}]}}, {"id": "professional_review", "name": "专业课复习", "icon": "📚", "description": "专业课知识点复习", "duration": 120, "data": {"title": "专业课复习", "subject": "professional", "priority": "high", "duration": 120}}, {"id": "mock_exam", "name": "真题模拟", "icon": "📋", "description": "历年真题模拟练习", "duration": 180, "data": {"title": "真题模拟考试", "subject": "other", "priority": "high", "duration": 180, "subtasks": [{"title": "答题", "completed": false}, {"title": "对答案", "completed": false}, {"title": "错题分析", "completed": false}]}}]}, "methods": {"selectTemplate": {"type": "function", "description": "选择任务模板", "params": ["templateId"], "implementation": "applyTaskTemplate"}, "updateTitle": {"type": "function", "description": "更新任务标题", "params": ["value"], "implementation": "setTaskTitle"}, "selectSubject": {"type": "function", "description": "选择科目", "params": ["index"], "implementation": "setTaskSubject"}, "updateDescription": {"type": "function", "description": "更新任务描述", "params": ["value"], "implementation": "setTaskDescription"}, "selectPriority": {"type": "function", "description": "选择优先级", "params": ["priority"], "implementation": "setTaskPriority"}, "selectDuration": {"type": "function", "description": "选择预计时长", "params": ["duration"], "implementation": "setTaskDuration"}, "selectDueDate": {"type": "function", "description": "选择截止日期", "params": ["date"], "implementation": "setTaskDueDate"}, "addSubtask": {"type": "function", "description": "添加子任务", "implementation": "addNewSubtask"}, "updateSubtask": {"type": "function", "description": "更新子任务", "params": ["index", "value"], "implementation": "updateSubtaskTitle"}, "removeSubtask": {"type": "function", "description": "删除子任务", "params": ["index"], "implementation": "deleteSubtask"}, "cancelForm": {"type": "navigate", "url": "back"}, "saveTask": {"type": "function", "description": "保存任务", "implementation": "submitTaskForm"}, "saveAndStart": {"type": "function", "description": "保存任务并开始番茄钟", "implementation": "saveTaskAndStartPomodoro"}}, "lifecycle": {"onLoad": ["initTaskForm", "loadTaskTemplates", "loadAvailableSubjects"], "onShow": ["validateTaskForm"]}, "validation": {"rules": {"title": {"required": true, "minLength": 2, "maxLength": 100}}}}