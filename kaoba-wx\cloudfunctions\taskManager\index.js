// 任务管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    switch (action) {
      case 'getTasks':
        return await getTasks(wxContext.OPENID, data)
      case 'addTask':
        return await addTask(wxContext.OPENID, data)
      case 'updateTask':
        return await updateTask(wxContext.OPENID, data)
      case 'deleteTask':
        return await deleteTask(wxContext.OPENID, data)
      case 'completeTask':
        return await completeTask(wxContext.OPENID, data)
      case 'getTaskStats':
        return await getTaskStats(wxContext.OPENID, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('任务管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 获取任务列表
async function getTasks(openid, params) {
  const { filter, limit = 20, skip = 0 } = params || {}
  
  let query = db.collection('tasks').where({ _openid: openid })
  
  if (filter) {
    if (filter.completed !== undefined) {
      query = query.where({ completed: filter.completed })
    }
    if (filter.priority) {
      query = query.where({ priority: filter.priority })
    }
    if (filter.subject) {
      query = query.where({ subject: filter.subject })
    }
    if (filter.dateRange) {
      const { start, end } = filter.dateRange
      query = query.where({
        dueDate: _.gte(new Date(start)).and(_.lte(new Date(end)))
      })
    }
  }
  
  const result = await query
    .orderBy('dueDate', 'asc')
    .orderBy('priority', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return { success: true, data: result.data, total: result.data.length }
}

// 添加任务
async function addTask(openid, taskData) {
  const task = {
    ...taskData,
    _openid: openid,
    completed: false,
    createTime: new Date(),
    updateTime: new Date()
  }
  
  const result = await db.collection('tasks').add({ data: task })
  return { success: true, data: { _id: result._id, ...task } }
}

// 更新任务
async function updateTask(openid, { taskId, updates }) {
  const updateData = {
    ...updates,
    updateTime: new Date()
  }
  
  const result = await db.collection('tasks')
    .where({ _id: taskId, _openid: openid })
    .update({ data: updateData })
  
  return { success: true, data: result }
}

// 删除任务
async function deleteTask(openid, { taskId }) {
  const result = await db.collection('tasks')
    .where({ _id: taskId, _openid: openid })
    .remove()
  
  return { success: true, data: result }
}

// 完成任务
async function completeTask(openid, { taskId, completed = true }) {
  const updateData = {
    completed,
    updateTime: new Date()
  }
  
  if (completed) {
    updateData.completedTime = new Date()
  }
  
  const result = await db.collection('tasks')
    .where({ _id: taskId, _openid: openid })
    .update({ data: updateData })
  
  return { success: true, data: result }
}

// 获取任务统计
async function getTaskStats(openid, params) {
  const { dateRange } = params || {}
  
  let query = db.collection('tasks').where({ _openid: openid })
  
  if (dateRange) {
    const { start, end } = dateRange
    query = query.where({
      createTime: _.gte(new Date(start)).and(_.lte(new Date(end)))
    })
  }
  
  const allTasks = await query.get()
  const tasks = allTasks.data
  
  const stats = {
    total: tasks.length,
    completed: tasks.filter(t => t.completed).length,
    pending: tasks.filter(t => !t.completed).length,
    overdue: tasks.filter(t => !t.completed && new Date(t.dueDate) < new Date()).length,
    byPriority: {
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length
    },
    bySubject: {}
  }
  
  // 按科目统计
  tasks.forEach(task => {
    if (task.subject) {
      stats.bySubject[task.subject] = (stats.bySubject[task.subject] || 0) + 1
    }
  })
  
  return { success: true, data: stats }
}
