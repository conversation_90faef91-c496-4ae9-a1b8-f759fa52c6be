/* pages/data-center/index.wxss */

/* 页面头部 */
.header-container {
  text-align: center;
  padding: 40rpx 0;
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666666;
}

/* 考试准备度 */
.preparation-score-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.score-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.score-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.score-info-btn {
  background-color: #F0F0F0;
  border: none;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  font-size: 24rpx;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.score-circle {
  position: relative;
}

.score-progress {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-inner {
  width: 120rpx;
  height: 120rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.score-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #52C41A;
}

.score-unit {
  font-size: 20rpx;
  color: #666666;
}

.score-details {
  flex: 1;
}

.score-level {
  font-size: 36rpx;
  font-weight: 600;
  color: #52C41A;
  margin-bottom: 8rpx;
}

.score-description {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.score-factors {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.factor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.factor-name {
  font-size: 26rpx;
  color: #333333;
  min-width: 120rpx;
}

.factor-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-left: 24rpx;
}

.factor-bar {
  flex: 1;
  background-color: #F0F0F0;
  border-radius: 6rpx;
  height: 8rpx;
  overflow: hidden;
}

.factor-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.factor-score {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  min-width: 40rpx;
}

/* 学习统计 */
.study-stats-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.time-range-selector {
  display: flex;
  gap: 8rpx;
}

.time-range-item {
  font-size: 24rpx;
  color: #666666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background-color: #F5F5F5;
  transition: all 0.3s ease;
}

.time-range-item.active {
  background-color: #1890FF;
  color: #FFFFFF;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.stat-card {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.trend-icon {
  font-size: 16rpx;
}

.trend-text {
  font-size: 20rpx;
  color: #52C41A;
}

/* 效率分析 */
.efficiency-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.view-detail {
  font-size: 24rpx;
  color: #1890FF;
}

.efficiency-chart {
  margin-bottom: 24rpx;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.chart-title {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.chart-period {
  font-size: 22rpx;
  color: #999999;
}

.chart-content {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 200rpx;
  gap: 8rpx;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  position: relative;
}

.bar-fill {
  width: 100%;
  max-width: 32rpx;
  border-radius: 4rpx 4rpx 0 0;
  margin-bottom: 8rpx;
  transition: height 0.3s ease;
}

.bar-label {
  font-size: 20rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.bar-value {
  font-size: 18rpx;
  color: #333333;
  font-weight: 500;
}

.efficiency-insights {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
}

.insights-title {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.insight-item:last-child {
  margin-bottom: 0;
}

.insight-icon {
  font-size: 20rpx;
}

.insight-text {
  font-size: 24rpx;
  color: #666666;
  flex: 1;
}

/* 科目分析 */
.subjects-analysis-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.subjects-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.subject-item {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.subject-info {
  flex: 1;
}

.subject-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.subject-exam {
  font-size: 22rpx;
  color: #666666;
}

.subject-score {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.score-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #1890FF;
}

.score-max {
  font-size: 24rpx;
  color: #999999;
}

.subject-progress {
  margin-bottom: 16rpx;
}

.progress-bar {
  background-color: #E9ECEF;
  border-radius: 6rpx;
  height: 8rpx;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: #666666;
}

.subject-stats {
  display: flex;
  justify-content: space-between;
}

.subject-stat {
  text-align: center;
}

.stat-label {
  font-size: 20rpx;
  color: #999999;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

/* 学习习惯 */
.habits-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.habits-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.habit-card {
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}

.habit-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.habit-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.habit-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  margin-bottom: 8rpx;
}

.habit-description {
  font-size: 20rpx;
  color: #666666;
  line-height: 1.4;
}

/* 成就展示 */
.achievements-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  position: relative;
}

.achievement-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  flex-shrink: 0;
}

.achievement-content {
  flex: 1;
}

.achievement-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.achievement-description {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.achievement-time {
  font-size: 20rpx;
  color: #999999;
}

.achievement-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background-color: #FF4D4F;
  color: #FFFFFF;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 数据导出 */
.export-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.export-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.export-info {
  flex: 1;
}

.export-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.export-description {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.export-btn {
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
}

.export-btn:active {
  background-color: #096DD9;
}

.export-icon {
  font-size: 24rpx;
}

.export-text {
  font-size: 26rpx;
}

/* 准备度说明弹窗 */
.score-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.modal-body {
  padding: 32rpx;
}

.modal-text {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.factor-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.factor-explanation {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.factor-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.factor-desc {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.4;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
