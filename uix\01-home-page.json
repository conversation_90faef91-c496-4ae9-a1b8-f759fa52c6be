{"pageInfo": {"pageName": "首页", "pageId": "home", "pageType": "tabPage", "description": "应用主页，展示考试倒计时、今日任务和快捷操作", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "greeting", "type": "text", "content": "{{greetingText}}", "style": {"fontSize": "32rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "date", "type": "text", "content": "{{currentDate}}", "style": {"fontSize": "28rpx", "color": "#666666"}}]}, {"id": "exam-countdown", "type": "view", "className": "exam-countdown-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "countdown-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "24rpx"}, "children": [{"id": "countdown-title", "type": "text", "content": "距离考试还有", "style": {"fontSize": "28rpx", "color": "#666666"}}, {"id": "manage-exam-btn", "type": "button", "content": "管理", "style": {"fontSize": "26rpx", "color": "#1890FF", "backgroundColor": "transparent", "border": "none", "padding": "0"}, "events": {"tap": "navigateToExamCenter"}}]}, {"id": "countdown-display", "type": "view", "condition": "{{hasActiveExam}}", "style": {"textAlign": "center"}, "children": [{"id": "exam-name", "type": "text", "content": "{{activeExam.name}}", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "countdown-numbers", "type": "view", "style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "gap": "16rpx"}, "children": [{"id": "days", "type": "view", "style": {"textAlign": "center"}, "children": [{"type": "text", "content": "{{countdown.days}}", "style": {"fontSize": "48rpx", "fontWeight": "700", "color": "#FF4D4F", "display": "block"}}, {"type": "text", "content": "天", "style": {"fontSize": "24rpx", "color": "#999999"}}]}, {"id": "hours", "type": "view", "style": {"textAlign": "center"}, "children": [{"type": "text", "content": "{{countdown.hours}}", "style": {"fontSize": "48rpx", "fontWeight": "700", "color": "#FF4D4F", "display": "block"}}, {"type": "text", "content": "时", "style": {"fontSize": "24rpx", "color": "#999999"}}]}, {"id": "minutes", "type": "view", "style": {"textAlign": "center"}, "children": [{"type": "text", "content": "{{countdown.minutes}}", "style": {"fontSize": "48rpx", "fontWeight": "700", "color": "#FF4D4F", "display": "block"}}, {"type": "text", "content": "分", "style": {"fontSize": "24rpx", "color": "#999999"}}]}]}, {"id": "preparation-score", "type": "view", "style": {"marginTop": "24rpx", "padding": "16rpx 24rpx", "backgroundColor": "#F0F9FF", "borderRadius": "8rpx", "border": "1rpx solid #BAE7FF"}, "children": [{"type": "text", "content": "准备度：{{preparationScore}}%", "style": {"fontSize": "28rpx", "color": "#1890FF", "fontWeight": "500"}}]}]}, {"id": "no-exam-tip", "type": "view", "condition": "{{!hasActiveExam}}", "style": {"textAlign": "center", "padding": "40rpx 0"}, "children": [{"type": "text", "content": "还没有设置考试？", "style": {"fontSize": "28rpx", "color": "#999999", "marginBottom": "16rpx"}}, {"id": "add-exam-btn", "type": "button", "content": "添加考试", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "borderRadius": "8rpx", "padding": "12rpx 24rpx", "border": "none"}, "events": {"tap": "navigateToAddExam"}}]}]}, {"id": "today-tasks", "type": "view", "className": "today-tasks-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "tasks-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "24rpx"}, "children": [{"id": "tasks-title", "type": "text", "content": "今日任务 ({{todayTasks.completed}}/{{todayTasks.total}})", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333"}}, {"id": "view-all-btn", "type": "button", "content": "查看全部", "style": {"fontSize": "26rpx", "color": "#1890FF", "backgroundColor": "transparent", "border": "none", "padding": "0"}, "events": {"tap": "navigateToTaskCenter"}}]}, {"id": "task-list", "type": "view", "condition": "{{todayTasks.list.length > 0}}", "children": [{"id": "task-item", "type": "view", "forEach": "{{todayTasks.list}}", "forItem": "task", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "padding": "16rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "task-checkbox", "type": "checkbox", "checked": "{{task.completed}}", "style": {"marginRight": "16rpx"}, "events": {"change": "toggleTaskComplete"}}, {"id": "task-content", "type": "view", "style": {"flex": "1"}, "children": [{"id": "task-title", "type": "text", "content": "{{task.title}}", "style": {"fontSize": "28rpx", "color": "{{task.completed ? '#999999' : '#333333'}}", "textDecoration": "{{task.completed ? 'line-through' : 'none'}}"}}, {"id": "task-subject", "type": "text", "content": "{{task.subject}}", "condition": "{{task.subject}}", "style": {"fontSize": "24rpx", "color": "#999999", "marginTop": "4rpx"}}]}, {"id": "task-priority", "type": "view", "condition": "{{task.priority === 'high'}}", "style": {"width": "8rpx", "height": "8rpx", "borderRadius": "50%", "backgroundColor": "#FF4D4F", "marginLeft": "16rpx"}}]}]}, {"id": "no-tasks-tip", "type": "view", "condition": "{{todayTasks.list.length === 0}}", "style": {"textAlign": "center", "padding": "40rpx 0"}, "children": [{"type": "text", "content": "今天还没有安排任务", "style": {"fontSize": "28rpx", "color": "#999999", "marginBottom": "16rpx"}}, {"id": "add-task-btn", "type": "button", "content": "添加任务", "style": {"fontSize": "28rpx", "color": "#1890FF", "backgroundColor": "transparent", "border": "1rpx solid #1890FF", "borderRadius": "8rpx", "padding": "12rpx 24rpx"}, "events": {"tap": "navigateToAddTask"}}]}]}, {"id": "quick-actions", "type": "view", "className": "quick-actions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "actions-title", "type": "text", "content": "快捷操作", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "actions-grid", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr", "gap": "16rpx"}, "children": [{"id": "start-study-btn", "type": "button", "style": {"backgroundColor": "#1890FF", "color": "#FFFFFF", "borderRadius": "12rpx", "padding": "24rpx", "border": "none", "textAlign": "center"}, "events": {"tap": "startStudySession"}, "children": [{"type": "text", "content": "🍅", "style": {"fontSize": "32rpx", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "开始复习", "style": {"fontSize": "28rpx", "fontWeight": "500"}}]}, {"id": "add-task-quick-btn", "type": "button", "style": {"backgroundColor": "#52C41A", "color": "#FFFFFF", "borderRadius": "12rpx", "padding": "24rpx", "border": "none", "textAlign": "center"}, "events": {"tap": "navigateToAddTask"}, "children": [{"type": "text", "content": "➕", "style": {"fontSize": "32rpx", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "添加任务", "style": {"fontSize": "28rpx", "fontWeight": "500"}}]}]}]}], "data": {"greetingText": "早上好", "currentDate": "2025年6月27日 星期五", "hasActiveExam": true, "activeExam": {"id": "exam_001", "name": "2025年考研", "date": "2025-12-23", "type": "考研"}, "countdown": {"days": 179, "hours": 15, "minutes": 32}, "preparationScore": 65, "todayTasks": {"total": 5, "completed": 2, "list": [{"id": "task_001", "title": "数学高数第一章复习", "subject": "数学", "completed": true, "priority": "high"}, {"id": "task_002", "title": "英语单词背诵100个", "subject": "英语", "completed": false, "priority": "medium"}, {"id": "task_003", "title": "政治马原理论学习", "subject": "政治", "completed": true, "priority": "low"}]}}, "methods": {"navigateToExamCenter": {"type": "navigate", "url": "/pages/exam-center/index"}, "navigateToAddExam": {"type": "navigate", "url": "/pages/exam-center/add-exam"}, "navigateToTaskCenter": {"type": "navigate", "url": "/pages/task-center/index"}, "navigateToAddTask": {"type": "navigate", "url": "/pages/task-center/add-task"}, "startStudySession": {"type": "navigate", "url": "/pages/pomodoro/index"}, "toggleTaskComplete": {"type": "function", "description": "切换任务完成状态", "params": ["event"], "implementation": "updateTaskStatus"}}, "lifecycle": {"onLoad": ["initPageData", "startCountdownTimer"], "onShow": ["refreshTodayTasks", "updateCountdown"], "onHide": ["clearCountdownTimer"]}, "interactions": {"pullToRefresh": {"enabled": true, "action": "refreshAllData"}, "scrollToTop": {"enabled": true, "threshold": 400}}}