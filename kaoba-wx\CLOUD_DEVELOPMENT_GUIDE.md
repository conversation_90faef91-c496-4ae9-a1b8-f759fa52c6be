# 微信云开发配置指南

## 概述

本项目已集成微信云开发，提供完整的后端服务支持，包括云数据库、云函数、云存储等功能。

## 环境配置

### 云开发环境ID
- 环境ID: `cloud1-8gvo8etxe4c004b3`
- 已在 `app.js` 中配置

### 项目结构
```
kaoba-wx/
├── cloudfunctions/          # 云函数目录
│   ├── login/              # 用户登录云函数
│   ├── taskManager/        # 任务管理云函数
│   ├── examManager/        # 考试管理云函数
│   └── studyManager/       # 学习管理云函数
├── utils/
│   ├── cloudApi.js         # 云开发API封装
│   └── dbInit.js           # 数据库初始化工具
└── pages/                  # 页面文件
```

## 云函数说明

### 1. login 云函数
- **功能**: 用户登录，获取openid
- **调用**: `CloudApi.login()`

### 2. taskManager 云函数
- **功能**: 任务管理（增删改查、统计）
- **主要方法**:
  - `getTasks()` - 获取任务列表
  - `addTask()` - 添加任务
  - `updateTask()` - 更新任务
  - `deleteTask()` - 删除任务
  - `completeTask()` - 完成任务
  - `getTaskStats()` - 获取任务统计

### 3. examManager 云函数
- **功能**: 考试管理（增删改查、提醒）
- **主要方法**:
  - `getExams()` - 获取考试列表
  - `addExam()` - 添加考试
  - `updateExam()` - 更新考试
  - `deleteExam()` - 删除考试
  - `getUpcomingExams()` - 获取即将到来的考试
  - `getExamStats()` - 获取考试统计

### 4. studyManager 云函数
- **功能**: 学习会话管理（番茄钟、学习统计）
- **主要方法**:
  - `startStudySession()` - 开始学习会话
  - `endStudySession()` - 结束学习会话
  - `getStudyStats()` - 获取学习统计
  - `addPomodoroSession()` - 添加番茄钟会话
  - `getPomodoroStats()` - 获取番茄钟统计

## 数据库集合

### kaoba 集合（统一数据集合）
所有数据都存储在名为 `kaoba` 的单一集合中，通过 `type` 字段区分不同类型的数据：

#### 任务数据 (type: 'task')
```javascript
{
  _id: String,           // 自动生成
  _openid: String,       // 用户openid（自动注入）
  type: 'task',          // 数据类型标识
  title: String,         // 任务标题
  subject: String,       // 科目
  priority: String,      // 优先级：high/medium/low
  estimatedTime: Number, // 预估时间（分钟）
  dueDate: Date,         // 截止日期
  description: String,   // 任务描述
  completed: Boolean,    // 是否完成
  createTime: Date,      // 创建时间
  updateTime: Date,      // 更新时间
  completedTime: Date    // 完成时间
}
```

#### 考试数据 (type: 'exam')
```javascript
{
  _id: String,           // 自动生成
  _openid: String,       // 用户openid（自动注入）
  type: 'exam',          // 数据类型标识
  title: String,         // 考试标题
  subject: String,       // 科目
  examDate: Date,        // 考试日期
  examTime: String,      // 考试时间
  location: String,      // 考试地点
  description: String,   // 考试描述
  reminderDays: Number,  // 提醒天数
  createTime: Date,      // 创建时间
  updateTime: Date       // 更新时间
}
```

#### 学习会话数据 (type: 'study_session')
```javascript
{
  _id: String,           // 自动生成
  _openid: String,       // 用户openid（自动注入）
  type: 'study_session', // 数据类型标识
  subject: String,       // 学习科目
  taskId: String,        // 关联任务ID（可选）
  startTime: Date,       // 开始时间
  endTime: Date,         // 结束时间
  totalDuration: Number, // 总时长（分钟）
  pausedDuration: Number,// 暂停时长（分钟）
  status: String,        // 状态：active/paused/completed
  notes: String,         // 学习笔记
  createTime: Date       // 创建时间
}
```

#### 番茄钟数据 (type: 'pomodoro_session')
```javascript
{
  _id: String,           // 自动生成
  _openid: String,       // 用户openid（自动注入）
  type: 'pomodoro_session', // 数据类型标识
  subject: String,       // 学习科目
  taskId: String,        // 关联任务ID（可选）
  duration: Number,      // 番茄钟时长（分钟）
  completed: Boolean,    // 是否完成
  startTime: Date,       // 开始时间
  endTime: Date,         // 结束时间
  createTime: Date       // 创建时间
}
```

## 使用方法

### 1. 调用云函数
```javascript
// 引入API工具类
const CloudApi = require('../../utils/cloudApi')

// 获取任务列表
const result = await CloudApi.getTasks()
if (result.success) {
  console.log('任务列表:', result.data)
}

// 添加任务
const taskData = {
  title: '数学复习',
  subject: '数学',
  priority: 'high',
  estimatedTime: 120,
  dueDate: new Date(),
  description: '复习高等数学第一章'
}
const addResult = await CloudApi.addTask(taskData)
```

### 2. 数据库初始化
应用启动时会自动检查并初始化数据库，创建示例数据。如需手动初始化：

```javascript
const DatabaseInit = require('./utils/dbInit')
const result = await DatabaseInit.checkAndInit()
```

## 部署说明

### 1. 云函数部署
1. 在微信开发者工具中打开项目
2. 右键点击 `cloudfunctions` 目录下的各个云函数文件夹
3. 选择"上传并部署：云端安装依赖"

### 2. 数据库配置
1. 在微信开发者工具中打开"云开发控制台"
2. 进入"数据库"页面
3. 创建名为 `kaoba` 的集合
4. 系统会自动在该集合中存储所有类型的数据

### 3. 权限配置
数据库权限已设置为"仅创建者可读写"，确保数据安全。

## 注意事项

1. **环境ID**: 确保使用正确的云开发环境ID
2. **基础库版本**: 需要微信基础库版本 2.2.3 或以上
3. **网络权限**: 确保小程序有网络访问权限
4. **数据安全**: 所有数据操作都会自动注入用户openid，确保数据隔离

## 故障排除

### 常见问题

1. **云函数调用失败**
   - 检查云函数是否正确部署
   - 检查网络连接
   - 查看控制台错误信息

2. **数据库操作失败**
   - 检查数据库权限设置
   - 确认集合是否存在
   - 检查数据格式是否正确

3. **初始化失败**
   - 检查环境ID是否正确
   - 确认基础库版本
   - 查看控制台日志

### 调试方法

1. 在微信开发者工具中查看控制台日志
2. 在云开发控制台查看云函数日志
3. 使用真机调试测试网络环境
