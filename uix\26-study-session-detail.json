{"pageInfo": {"pageName": "学习记录详情", "pageId": "studySessionDetail", "pageType": "page", "description": "具体学习会话的详细记录页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "session-header", "type": "view", "className": "session-header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "session-status", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "20rpx"}, "children": [{"id": "session-type", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "8rpx"}, "children": [{"id": "type-icon", "type": "text", "content": "{{session.typeIcon}}", "style": {"fontSize": "28rpx"}}, {"id": "type-text", "type": "text", "content": "{{session.type}}", "style": {"fontSize": "26rpx", "color": "#666666"}}]}, {"id": "session-rating", "type": "view", "condition": "{{session.rating}}", "style": {"display": "flex", "alignItems": "center", "gap": "4rpx"}, "children": [{"id": "rating-stars", "type": "text", "content": "{{session.ratingStars}}", "style": {"fontSize": "24rpx"}}, {"id": "rating-text", "type": "text", "content": "{{session.rating}}/5", "style": {"fontSize": "22rpx", "color": "#999999"}}]}]}, {"id": "session-title", "type": "text", "content": "{{session.title}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "session-meta", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "16rpx", "marginBottom": "16rpx"}, "children": [{"id": "session-subject", "type": "view", "condition": "{{session.subject}}", "style": {"backgroundColor": "#E6F7FF", "color": "#1890FF", "fontSize": "22rpx", "padding": "4rpx 8rpx", "borderRadius": "4rpx"}, "children": [{"type": "text", "content": "{{session.subject}}"}]}, {"id": "session-task", "type": "text", "condition": "{{session.taskName}}", "content": "关联任务：{{session.taskName}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "session-time-info", "type": "view", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "display": "grid", "gridTemplateColumns": "1fr 1fr", "gap": "16rpx"}, "children": [{"id": "start-time", "type": "view", "children": [{"type": "text", "content": "开始时间", "style": {"fontSize": "22rpx", "color": "#999999", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{session.startTime}}", "style": {"fontSize": "26rpx", "color": "#333333", "fontWeight": "500"}}]}, {"id": "end-time", "type": "view", "children": [{"type": "text", "content": "结束时间", "style": {"fontSize": "22rpx", "color": "#999999", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{session.endTime}}", "style": {"fontSize": "26rpx", "color": "#333333", "fontWeight": "500"}}]}, {"id": "duration", "type": "view", "children": [{"type": "text", "content": "学习时长", "style": {"fontSize": "22rpx", "color": "#999999", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{session.duration}}", "style": {"fontSize": "26rpx", "color": "#52C41A", "fontWeight": "600"}}]}, {"id": "efficiency", "type": "view", "children": [{"type": "text", "content": "专注效率", "style": {"fontSize": "22rpx", "color": "#999999", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{session.efficiency}}%", "style": {"fontSize": "26rpx", "color": "#1890FF", "fontWeight": "600"}}]}]}]}, {"id": "session-timeline", "type": "view", "condition": "{{session.timeline && session.timeline.length > 0}}", "className": "timeline-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "timeline-title", "type": "text", "content": "学习时间线", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "timeline-list", "type": "view", "children": [{"id": "timeline-item", "type": "view", "forEach": "{{session.timeline}}", "forItem": "event", "forIndex": "index", "style": {"display": "flex", "alignItems": "flex-start", "gap": "16rpx", "marginBottom": "20rpx", "position": "relative"}, "children": [{"id": "timeline-dot", "type": "view", "style": {"width": "12rpx", "height": "12rpx", "borderRadius": "50%", "backgroundColor": "{{event.color}}", "marginTop": "8rpx", "position": "relative", "zIndex": "2"}}, {"id": "timeline-line", "type": "view", "condition": "{{index < session.timeline.length - 1}}", "style": {"position": "absolute", "left": "5rpx", "top": "20rpx", "width": "2rpx", "height": "40rpx", "backgroundColor": "#E9ECEF", "zIndex": "1"}}, {"id": "timeline-content", "type": "view", "style": {"flex": "1"}, "children": [{"id": "timeline-time", "type": "text", "content": "{{event.time}}", "style": {"fontSize": "22rpx", "color": "#999999", "marginBottom": "4rpx"}}, {"id": "timeline-event", "type": "text", "content": "{{event.description}}", "style": {"fontSize": "26rpx", "color": "#333333", "lineHeight": "1.4"}}]}]}]}]}, {"id": "session-notes", "type": "view", "condition": "{{session.notes}}", "className": "notes-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "notes-title", "type": "text", "content": "学习笔记", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "notes-content", "type": "text", "content": "{{session.notes}}", "style": {"fontSize": "26rpx", "color": "#666666", "lineHeight": "1.6", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx"}}]}, {"id": "session-interruptions", "type": "view", "condition": "{{session.interruptions && session.interruptions.length > 0}}", "className": "interruptions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "interruptions-title", "type": "text", "content": "中断记录", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "interruptions-summary", "type": "text", "content": "本次学习共被中断 {{session.interruptions.length}} 次", "style": {"fontSize": "24rpx", "color": "#666666", "marginBottom": "16rpx"}}, {"id": "interruptions-list", "type": "view", "children": [{"id": "interruption-item", "type": "view", "forEach": "{{session.interruptions}}", "forItem": "interruption", "forIndex": "index", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "12rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "interruption-info", "type": "view", "children": [{"id": "interruption-time", "type": "text", "content": "{{interruption.time}}", "style": {"fontSize": "24rpx", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "interruption-reason", "type": "text", "content": "{{interruption.reason}}", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "interruption-duration", "type": "text", "content": "{{interruption.duration}}", "style": {"fontSize": "22rpx", "color": "#FA8C16", "fontWeight": "500"}}]}]}]}, {"id": "session-achievements", "type": "view", "condition": "{{session.achievements && session.achievements.length > 0}}", "className": "achievements-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "achievements-title", "type": "text", "content": "🏆 本次获得成就", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "achievements-list", "type": "view", "style": {"display": "flex", "gap": "12rpx", "flexWrap": "wrap"}, "children": [{"id": "achievement-badge", "type": "view", "forEach": "{{session.achievements}}", "forItem": "achievement", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "gap": "8rpx", "backgroundColor": "#FFF7E6", "border": "1rpx solid #FFD591", "borderRadius": "8rpx", "padding": "8rpx 12rpx"}, "children": [{"type": "text", "content": "{{achievement.icon}}", "style": {"fontSize": "20rpx"}}, {"type": "text", "content": "{{achievement.name}}", "style": {"fontSize": "22rpx", "color": "#FA8C16", "fontWeight": "500"}}]}]}]}, {"id": "session-stats", "type": "view", "className": "stats-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "stats-title", "type": "text", "content": "详细统计", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "20rpx"}}, {"id": "stats-grid", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr", "gap": "16rpx"}, "children": [{"id": "stat-item", "type": "view", "forEach": "{{sessionStats}}", "forItem": "stat", "forIndex": "index", "style": {"textAlign": "center", "backgroundColor": "{{stat.bgColor}}", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{stat.value}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "{{stat.color}}", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "{{stat.label}}", "style": {"fontSize": "22rpx", "color": "#666666"}}]}]}]}, {"id": "session-actions", "type": "view", "className": "actions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "action-buttons", "type": "view", "style": {"display": "flex", "gap": "16rpx"}, "children": [{"id": "repeat-session-btn", "type": "button", "content": "重复此次学习", "style": {"flex": "1", "fontSize": "26rpx", "color": "#1890FF", "backgroundColor": "#E6F7FF", "border": "1rpx solid #91D5FF", "borderRadius": "8rpx", "padding": "12rpx"}, "events": {"tap": "repeatSession"}}, {"id": "share-session-btn", "type": "button", "content": "分享记录", "style": {"flex": "1", "fontSize": "26rpx", "color": "#52C41A", "backgroundColor": "#F6FFED", "border": "1rpx solid #B7EB8F", "borderRadius": "8rpx", "padding": "12rpx"}, "events": {"tap": "shareSession"}}]}]}], "data": {"sessionId": "", "session": {"id": "session_001", "title": "数学高数第一章复习", "type": "番茄钟学习", "typeIcon": "🍅", "subject": "数学", "taskName": "数学高数第一章复习", "startTime": "2025-06-27 14:30:00", "endTime": "2025-06-27 14:55:00", "duration": "25分钟", "efficiency": 92, "rating": 4, "ratingStars": "⭐⭐⭐⭐☆", "notes": "今天复习了极限的基本概念，重点掌握了数列极限和函数极限的定义。需要多做练习题加深理解。", "timeline": [{"time": "14:30", "description": "开始学习，阅读教材第一章", "color": "#52C41A"}, {"time": "14:35", "description": "完成概念理解，开始做例题", "color": "#1890FF"}, {"time": "14:42", "description": "短暂休息，喝水", "color": "#FA8C16"}, {"time": "14:45", "description": "继续做练习题", "color": "#1890FF"}, {"time": "14:55", "description": "完成学习，整理笔记", "color": "#52C41A"}], "interruptions": [{"time": "14:42", "reason": "接电话", "duration": "2分钟"}], "achievements": [{"id": "focus_master", "name": "专注大师", "icon": "🎯"}]}, "sessionStats": [{"label": "专注时长", "value": "23分钟", "color": "#52C41A", "bgColor": "#F6FFED"}, {"label": "中断次数", "value": "1次", "color": "#FA8C16", "bgColor": "#FFF2E8"}, {"label": "效率评分", "value": "92%", "color": "#1890FF", "bgColor": "#E6F7FF"}, {"label": "满意度", "value": "4/5", "color": "#722ED1", "bgColor": "#F9F0FF"}]}, "methods": {"repeatSession": {"type": "function", "description": "重复此次学习设置", "implementation": "createSimilarSession"}, "shareSession": {"type": "function", "description": "分享学习记录", "implementation": "generateSessionShare"}}, "lifecycle": {"onLoad": ["loadSessionDetail"], "onShow": ["refreshSessionData"]}}