/* pages/pomodoro/index.wxss */

/* 计时器主容器 */
.timer-main-container {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
  text-align: center;
  min-height: 600rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 会话信息 */
.session-info {
  margin-bottom: 40rpx;
}

.task-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.task-subject {
  font-size: 26rpx;
  color: #666666;
}

/* 计时器显示 */
.timer-display {
  position: relative;
  margin-bottom: 60rpx;
}

.timer-circle {
  width: 320rpx;
  height: 320rpx;
  border-radius: 50%;
  border: 8rpx solid #F0F0F0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.progress-circle {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  width: 320rpx;
  height: 320rpx;
  border-radius: 50%;
  border: 8rpx solid transparent;
  border-top-color: #FF6B6B;
  transition: transform 0.3s ease;
}

.timer-content {
  text-align: center;
}

.timer-time {
  font-size: 64rpx;
  font-weight: 700;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
  font-family: 'Courier New', monospace;
}

.timer-label {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

/* 控制按钮 */
.timer-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 40rpx;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  border: none;
  font-size: 24rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.primary-btn {
  background-color: #FF6B6B;
  color: #FFFFFF;
  transform: scale(1.1);
}

.primary-btn:active {
  background-color: #FF5252;
  transform: scale(1.05);
}

.primary-btn.pause {
  background-color: #FFA726;
}

.primary-btn.pause:active {
  background-color: #FF9800;
}

.secondary-btn {
  background-color: #F5F5F5;
  color: #666666;
}

.secondary-btn:active {
  background-color: #E0E0E0;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 会话统计 */
.session-stats {
  display: flex;
  justify-content: center;
  gap: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #FF6B6B;
  display: block;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

/* 设置容器 */
.settings-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.settings-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.settings-toggle {
  font-size: 24rpx;
  color: #1890FF;
}

.settings-content {
  animation: slideDown 0.3s ease-out;
}

/* 设置组 */
.setting-group {
  margin-bottom: 32rpx;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

/* 时间设置 */
.time-settings {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.time-setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.time-label {
  font-size: 26rpx;
  color: #333333;
}

.time-picker {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #F5F5F5;
  border: none;
  font-size: 24rpx;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-btn:active {
  background-color: #E0E0E0;
}

.time-value {
  font-size: 26rpx;
  color: #333333;
  min-width: 100rpx;
  text-align: center;
}

/* 任务选择器 */
.task-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
}

.selected-task {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.selector-arrow {
  font-size: 24rpx;
  color: #999999;
}

/* 音效选项 */
.sound-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
}

.sound-option {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 16rpx;
  position: relative;
}

.sound-option.active {
  background-color: #E6F7FF;
  border: 1rpx solid #91D5FF;
}

.sound-icon {
  font-size: 24rpx;
}

.sound-name {
  font-size: 24rpx;
  color: #333333;
  flex: 1;
}

.sound-check {
  font-size: 20rpx;
  color: #1890FF;
}

/* 其他设置 */
.other-settings {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.setting-name {
  font-size: 26rpx;
  color: #333333;
}

.setting-switch {
  transform: scale(0.8);
}

/* 今日统计 */
.today-stats-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.stats-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.view-detail {
  font-size: 24rpx;
  color: #1890FF;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16rpx;
}

.stat-card {
  text-align: center;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
}

.stat-icon {
  font-size: 32rpx;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #666666;
}

/* 最近专注记录 */
.recent-sessions-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.sessions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.sessions-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.view-all {
  font-size: 24rpx;
  color: #1890FF;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.session-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.session-item:last-child {
  border-bottom: none;
}

.session-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #F8F9FA;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  flex-shrink: 0;
}

.session-info {
  flex: 1;
}

.session-task {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.session-time {
  font-size: 22rpx;
  color: #999999;
}

.session-status {
  flex-shrink: 0;
}

.status-text {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.status-completed {
  background-color: #F6FFED;
  color: #52C41A;
}

/* 任务选择弹窗 */
.task-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.task-modal {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.modal-body {
  padding: 16rpx 0;
  max-height: 60vh;
  overflow-y: auto;
}

.task-option {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  gap: 16rpx;
}

.task-option:active {
  background-color: #F8F9FA;
}

.task-option.selected {
  background-color: #E6F7FF;
}

.task-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.task-subject {
  font-size: 22rpx;
  color: #666666;
}

.task-check {
  font-size: 24rpx;
  color: #1890FF;
}

/* 动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
