{"pageInfo": {"pageName": "弹窗组件", "pageId": "modalComponents", "pageType": "component", "description": "通用弹窗和对话框组件", "version": "1.0.0", "lastModified": "2025-06-27"}, "components": {"confirmModal": {"id": "confirm-modal", "type": "view", "className": "modal-overlay", "condition": "{{showConfirmModal}}", "style": {"position": "fixed", "top": "0", "left": "0", "right": "0", "bottom": "0", "backgroundColor": "rgba(0,0,0,0.5)", "zIndex": "9999", "display": "flex", "alignItems": "center", "justifyContent": "center", "padding": "40rpx"}, "children": [{"id": "confirm-dialog", "type": "view", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "40rpx", "maxWidth": "600rpx", "width": "100%", "boxShadow": "0 8rpx 32rpx rgba(0,0,0,0.2)"}, "children": [{"id": "confirm-icon", "type": "text", "condition": "{{confirmModal.icon}}", "content": "{{confirmModal.icon}}", "style": {"fontSize": "64rpx", "textAlign": "center", "display": "block", "marginBottom": "24rpx"}}, {"id": "confirm-title", "type": "text", "content": "{{confirmModal.title}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "textAlign": "center", "marginBottom": "16rpx"}}, {"id": "confirm-content", "type": "text", "content": "{{confirmModal.content}}", "style": {"fontSize": "28rpx", "color": "#666666", "textAlign": "center", "lineHeight": "1.5", "marginBottom": "40rpx"}}, {"id": "confirm-actions", "type": "view", "style": {"display": "flex", "gap": "16rpx"}, "children": [{"id": "confirm-cancel-btn", "type": "button", "content": "{{confirmModal.cancelText || '取消'}}", "style": {"flex": "1", "fontSize": "28rpx", "color": "#666666", "backgroundColor": "#F5F5F5", "border": "none", "borderRadius": "8rpx", "padding": "16rpx"}, "events": {"tap": "cancelConfirm"}}, {"id": "confirm-ok-btn", "type": "button", "content": "{{confirmModal.confirmText || '确定'}}", "style": {"flex": "1", "fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "{{confirmModal.type === 'danger' ? '#FF4D4F' : '#1890FF'}}", "border": "none", "borderRadius": "8rpx", "padding": "16rpx"}, "events": {"tap": "confirmAction"}}]}]}]}, "alertModal": {"id": "alert-modal", "type": "view", "className": "modal-overlay", "condition": "{{showAlertModal}}", "style": {"position": "fixed", "top": "0", "left": "0", "right": "0", "bottom": "0", "backgroundColor": "rgba(0,0,0,0.5)", "zIndex": "9999", "display": "flex", "alignItems": "center", "justifyContent": "center", "padding": "40rpx"}, "children": [{"id": "alert-dialog", "type": "view", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "40rpx", "maxWidth": "600rpx", "width": "100%", "boxShadow": "0 8rpx 32rpx rgba(0,0,0,0.2)"}, "children": [{"id": "alert-icon", "type": "text", "condition": "{{alertModal.icon}}", "content": "{{alertModal.icon}}", "style": {"fontSize": "64rpx", "textAlign": "center", "display": "block", "marginBottom": "24rpx"}}, {"id": "alert-title", "type": "text", "content": "{{alertModal.title}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "textAlign": "center", "marginBottom": "16rpx"}}, {"id": "alert-content", "type": "text", "content": "{{alertModal.content}}", "style": {"fontSize": "28rpx", "color": "#666666", "textAlign": "center", "lineHeight": "1.5", "marginBottom": "40rpx"}}, {"id": "alert-action", "type": "button", "content": "{{alertModal.buttonText || '确定'}}", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "border": "none", "borderRadius": "8rpx", "padding": "16rpx", "width": "100%"}, "events": {"tap": "<PERSON><PERSON><PERSON><PERSON>"}}]}]}, "toastMessage": {"id": "toast-message", "type": "view", "className": "toast-container", "condition": "{{showToast}}", "style": {"position": "fixed", "top": "50%", "left": "50%", "transform": "translate(-50%, -50%)", "zIndex": "10000", "backgroundColor": "rgba(0,0,0,0.8)", "color": "#FFFFFF", "borderRadius": "12rpx", "padding": "24rpx 32rpx", "fontSize": "28rpx", "textAlign": "center", "maxWidth": "500rpx", "animation": "fadeInOut 2s ease-in-out"}, "children": [{"id": "toast-icon", "type": "text", "condition": "{{toast.icon}}", "content": "{{toast.icon}}", "style": {"fontSize": "32rpx", "marginRight": "12rpx"}}, {"id": "toast-text", "type": "text", "content": "{{toast.message}}", "style": {"fontSize": "28rpx"}}]}, "loadingModal": {"id": "loading-modal", "type": "view", "className": "modal-overlay", "condition": "{{showLoadingModal}}", "style": {"position": "fixed", "top": "0", "left": "0", "right": "0", "bottom": "0", "backgroundColor": "rgba(0,0,0,0.3)", "zIndex": "9999", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "children": [{"id": "loading-dialog", "type": "view", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "40rpx", "display": "flex", "flexDirection": "column", "alignItems": "center", "minWidth": "200rpx"}, "children": [{"id": "loading-spinner", "type": "view", "style": {"width": "60rpx", "height": "60rpx", "border": "4rpx solid #F0F0F0", "borderTop": "4rpx solid #1890FF", "borderRadius": "50%", "animation": "spin 1s linear infinite", "marginBottom": "20rpx"}}, {"id": "loading-text", "type": "text", "content": "{{loadingModal.message || '加载中...'}}", "style": {"fontSize": "28rpx", "color": "#333333"}}]}]}, "actionSheet": {"id": "action-sheet", "type": "view", "className": "modal-overlay", "condition": "{{showActionSheet}}", "style": {"position": "fixed", "top": "0", "left": "0", "right": "0", "bottom": "0", "backgroundColor": "rgba(0,0,0,0.5)", "zIndex": "9999", "display": "flex", "alignItems": "flex-end", "justifyContent": "center"}, "events": {"tap": "closeActionSheet"}, "children": [{"id": "action-sheet-content", "type": "view", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx 16rpx 0 0", "width": "100%", "maxHeight": "80vh", "animation": "slideUp 0.3s ease-out"}, "events": {"tap": "stopPropagation"}, "children": [{"id": "action-sheet-header", "type": "view", "condition": "{{actionSheet.title}}", "style": {"padding": "32rpx", "borderBottom": "1rpx solid #F0F0F0", "textAlign": "center"}, "children": [{"id": "action-sheet-title", "type": "text", "content": "{{actionSheet.title}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333"}}]}, {"id": "action-sheet-items", "type": "view", "children": [{"id": "action-item", "type": "button", "forEach": "{{actionSheet.items}}", "forItem": "item", "forIndex": "index", "content": "{{item.text}}", "style": {"width": "100%", "fontSize": "32rpx", "color": "{{item.type === 'danger' ? '#FF4D4F' : '#333333'}}", "backgroundColor": "transparent", "border": "none", "borderBottom": "1rpx solid #F0F0F0", "padding": "32rpx", "textAlign": "center"}, "events": {"tap": "selectActionItem"}}]}, {"id": "action-sheet-cancel", "type": "button", "content": "取消", "style": {"width": "100%", "fontSize": "32rpx", "color": "#666666", "backgroundColor": "#F8F9FA", "border": "none", "padding": "32rpx", "textAlign": "center"}, "events": {"tap": "closeActionSheet"}}]}]}, "shareModal": {"id": "share-modal", "type": "view", "className": "modal-overlay", "condition": "{{showShareModal}}", "style": {"position": "fixed", "top": "0", "left": "0", "right": "0", "bottom": "0", "backgroundColor": "rgba(0,0,0,0.5)", "zIndex": "9999", "display": "flex", "alignItems": "flex-end", "justifyContent": "center"}, "events": {"tap": "closeShareModal"}, "children": [{"id": "share-content", "type": "view", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx 16rpx 0 0", "width": "100%", "padding": "40rpx", "animation": "slideUp 0.3s ease-out"}, "events": {"tap": "stopPropagation"}, "children": [{"id": "share-title", "type": "text", "content": "分享到", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "textAlign": "center", "marginBottom": "32rpx"}}, {"id": "share-options", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr 1fr 1fr", "gap": "32rpx", "marginBottom": "32rpx"}, "children": [{"id": "share-option", "type": "button", "forEach": "{{shareOptions}}", "forItem": "option", "forIndex": "index", "style": {"backgroundColor": "transparent", "border": "none", "textAlign": "center", "padding": "16rpx"}, "events": {"tap": "shareToTarget"}, "children": [{"type": "text", "content": "{{option.icon}}", "style": {"fontSize": "48rpx", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "{{option.name}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}]}, {"id": "share-cancel", "type": "button", "content": "取消", "style": {"width": "100%", "fontSize": "28rpx", "color": "#666666", "backgroundColor": "#F5F5F5", "border": "none", "borderRadius": "8rpx", "padding": "16rpx"}, "events": {"tap": "closeShareModal"}}]}]}}, "data": {"showConfirmModal": false, "confirmModal": {"title": "", "content": "", "icon": "", "type": "default", "confirmText": "确定", "cancelText": "取消", "onConfirm": null, "onCancel": null}, "showAlertModal": false, "alertModal": {"title": "", "content": "", "icon": "", "buttonText": "确定", "onClose": null}, "showToast": false, "toast": {"message": "", "icon": "", "duration": 2000}, "showLoadingModal": false, "loadingModal": {"message": "加载中..."}, "showActionSheet": false, "actionSheet": {"title": "", "items": [], "onSelect": null}, "showShareModal": false, "shareOptions": [{"id": "wechat", "name": "微信好友", "icon": "💬"}, {"id": "moments", "name": "朋友圈", "icon": "🌟"}, {"id": "qq", "name": "QQ", "icon": "🐧"}, {"id": "weibo", "name": "微博", "icon": "📱"}]}, "methods": {"showConfirm": {"type": "function", "description": "显示确认对话框", "params": ["options"], "implementation": "displayConfirmModal"}, "confirmAction": {"type": "function", "description": "确认操作", "implementation": "executeConfirmAction"}, "cancelConfirm": {"type": "function", "description": "取消确认", "implementation": "executeCancelAction"}, "showAlert": {"type": "function", "description": "显示提示对话框", "params": ["options"], "implementation": "displayAlertModal"}, "closeAlert": {"type": "function", "description": "关闭提示对话框", "implementation": "hideAlertModal"}, "showToast": {"type": "function", "description": "显示Toast消息", "params": ["message", "icon", "duration"], "implementation": "displayToastMessage"}, "showLoading": {"type": "function", "description": "显示加载对话框", "params": ["message"], "implementation": "displayLoadingModal"}, "hideLoading": {"type": "function", "description": "隐藏加载对话框", "implementation": "hideLoadingModal"}, "showActionSheet": {"type": "function", "description": "显示操作菜单", "params": ["options"], "implementation": "displayActionSheet"}, "selectActionItem": {"type": "function", "description": "选择操作项", "params": ["itemIndex"], "implementation": "executeActionItem"}, "closeActionSheet": {"type": "function", "description": "关闭操作菜单", "implementation": "hideActionSheet"}, "showShare": {"type": "function", "description": "显示分享菜单", "params": ["shareData"], "implementation": "displayShareModal"}, "shareToTarget": {"type": "function", "description": "分享到指定平台", "params": ["targetId"], "implementation": "executeShare"}, "closeShareModal": {"type": "function", "description": "关闭分享菜单", "implementation": "hideShareModal"}, "stopPropagation": {"type": "function", "description": "阻止事件冒泡", "implementation": "preventEventBubbling"}}, "animations": {"fadeInOut": {"keyframes": {"0%": {"opacity": "0", "transform": "translate(-50%, -50%) scale(0.8)"}, "10%": {"opacity": "1", "transform": "translate(-50%, -50%) scale(1)"}, "90%": {"opacity": "1", "transform": "translate(-50%, -50%) scale(1)"}, "100%": {"opacity": "0", "transform": "translate(-50%, -50%) scale(0.8)"}}, "duration": "2s", "timingFunction": "ease-in-out"}, "slideUp": {"keyframes": {"0%": {"transform": "translateY(100%)"}, "100%": {"transform": "translateY(0)"}}, "duration": "0.3s", "timingFunction": "ease-out"}, "spin": {"keyframes": {"0%": {"transform": "rotate(0deg)"}, "100%": {"transform": "rotate(360deg)"}}, "duration": "1s", "iterationCount": "infinite", "timingFunction": "linear"}}}