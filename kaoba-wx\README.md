# 要考试啦 - 微信小程序

一个专为学生设计的学习管理和考试备考小程序，帮助学生高效管理学习任务、跟踪学习进度、使用番茄钟专注学习。

## 🎯 项目特色

### 📱 核心功能
- **考试管理**：添加考试、倒计时提醒、进度跟踪
- **任务管理**：创建学习任务、子任务分解、进度可视化
- **番茄钟专注**：专业计时器、效率统计、背景音效
- **数据分析**：学习数据可视化、智能分析、趋势预测
- **个人中心**：用户信息、成就系统、设置管理

### 🎨 设计亮点
- **一致的设计语言**：统一的视觉风格和交互模式
- **响应式布局**：适配不同屏幕尺寸
- **数据可视化**：进度条、圆形进度、统计图表
- **动画效果**：流畅的UI反馈和过渡动画
- **emoji图标**：解决图片资源问题，提升加载速度

## 📁 项目结构

```
kaoba-wx/
├── app.js                    # 应用主入口
├── app.json                  # 应用配置
├── app.wxss                  # 全局样式
├── custom-tab-bar/           # 自定义TabBar组件
│   ├── index.js
│   ├── index.json
│   ├── index.wxml
│   └── index.wxss
└── pages/                    # 页面目录
    ├── home/                 # 首页
    ├── exam-center/          # 考试中心
    ├── task-center/          # 任务中心
    ├── pomodoro/             # 番茄钟
    ├── profile/              # 我的页面
    ├── data-center/          # 数据中心
    ├── add-task/             # 添加任务
    ├── add-exam/             # 添加考试
    ├── settings/             # 设置页面
    └── ...                   # 其他功能页面
```

## 🚀 主要页面

### 1. 首页 (pages/home/<USER>
- **问候区域**：时间问候、天气信息、日期显示
- **考试倒计时**：最近考试、倒计时显示、准备进度
- **今日任务**：任务列表、完成状态、快捷操作
- **学习统计**：今日数据展示
- **悬浮按钮**：快捷菜单

### 2. 考试中心 (pages/exam-center/)
- **考试统计**：考试数量、状态统计
- **筛选功能**：全部、即将到来、备考中、已完成
- **考试列表**：详细信息、倒计时、进度、科目
- **操作菜单**：长按菜单、快捷操作

### 3. 任务中心 (pages/task-center/)
- **任务统计**：任务数量、完成率统计
- **筛选排序**：多维度筛选和排序
- **任务分组**：按日期分组显示
- **子任务管理**：子任务展开、进度跟踪
- **快捷操作**：开始、编辑、专注

### 4. 番茄钟 (pages/pomodoro/)
- **专业计时器**：圆形进度条、时间显示
- **设置调整**：工作时间、休息时间、背景音效
- **任务选择**：关联学习任务
- **统计数据**：今日统计、最近会话

### 5. 我的页面 (pages/profile/)
- **用户信息**：头像、昵称、当前考试
- **学习统计**：学习天数、完成任务、专注时长
- **成就展示**：学习成就、进度展示
- **功能菜单**：设置、帮助、数据导出

### 6. 数据中心 (pages/data-center/)
- **考试准备度**：智能评分、因子分析
- **学习统计**：时间范围选择、趋势分析
- **效率分析**：每日效率、洞察建议
- **科目分析**：科目进度、学习时长
- **学习习惯**：习惯分析、最佳时段

## 🔧 技术特色

### 架构设计
- **自定义TabBar**：使用emoji图标，灵活导航
- **模块化代码**：页面独立、组件复用
- **统一样式系统**：全局CSS工具类
- **生命周期管理**：完整的页面生命周期

### 交互功能
- **下拉刷新**：数据实时更新
- **长按操作**：上下文菜单
- **悬浮按钮**：快捷操作菜单
- **筛选排序**：多维度数据管理
- **弹窗交互**：模态框、确认对话框

### 数据管理
- **本地存储**：用户设置和数据持久化
- **状态管理**：页面间数据同步
- **数据筛选**：多条件筛选和排序
- **计算逻辑**：倒计时、进度计算、统计分析

## 📱 使用说明

### 开发环境
1. 安装微信开发者工具
2. 导入项目目录 `kaoba-wx`
3. 编译运行

### 主要功能使用
1. **添加考试**：在考试中心点击添加，填写考试信息
2. **创建任务**：在任务中心添加学习任务，设置优先级和截止时间
3. **专注学习**：使用番茄钟进行专注学习，可选择关联任务
4. **查看数据**：在数据中心查看学习统计和分析
5. **个人设置**：在我的页面进行个人信息和应用设置

## 🎨 设计理念

### 用户体验
- **简洁直观**：清晰的信息层次和操作流程
- **高效便捷**：快捷操作和智能提醒
- **个性化**：可定制的设置和个人信息

### 视觉设计
- **现代简约**：清爽的界面设计
- **数据可视化**：直观的进度和统计展示
- **一致性**：统一的设计语言和交互模式

## 📈 未来规划

### 功能扩展
- [ ] 云端数据同步
- [ ] 学习小组功能
- [ ] AI学习建议
- [ ] 更多统计图表
- [ ] 学习资源推荐

### 技术优化
- [ ] 性能优化
- [ ] 离线功能
- [ ] 更多动画效果
- [ ] 无障碍支持

## 📄 许可证

本项目仅供学习和参考使用。

---

**要考试啦** - 让学习更高效，让备考更轻松！ 🎓
