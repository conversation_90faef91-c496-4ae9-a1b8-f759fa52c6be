/* pages/help-feedback/index.wxss */

/* 容器 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.header {
  background-color: #FFFFFF;
  padding: 40rpx 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin-bottom: 32rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #7F8C8D;
}

/* 内容区域 */
.content {
  padding: 0 32rpx;
}

/* 分组样式 */
.section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2C3E50;
  padding: 32rpx 32rpx 16rpx 32rpx;
  display: block;
  border-bottom: 1rpx solid #F0F0F0;
}

/* FAQ 样式 */
.faq-list {
  padding: 16rpx 0;
}

.faq-item {
  border-bottom: 1rpx solid #F8F9FA;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  cursor: pointer;
}

.question-text {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
  flex: 1;
}

.expand-icon {
  font-size: 20rpx;
  color: #7F8C8D;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer.show {
  max-height: 500rpx;
}

.answer-text {
  font-size: 24rpx;
  color: #7F8C8D;
  line-height: 1.6;
  padding: 0 32rpx 24rpx 32rpx;
  display: block;
}

/* 使用指南样式 */
.guide-list {
  padding: 16rpx 0;
}

.guide-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F8F9FA;
  transition: background-color 0.3s ease;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-item:active {
  background-color: #F8F9FA;
}

.guide-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.guide-content {
  flex: 1;
}

.guide-title {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.guide-desc {
  font-size: 22rpx;
  color: #7F8C8D;
}

.guide-arrow {
  font-size: 20rpx;
  color: #BDC3C7;
}

/* 反馈表单样式 */
.feedback-form {
  padding: 16rpx 32rpx 32rpx 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.feedback-types {
  display: flex;
  gap: 12rpx;
}

.type-btn {
  flex: 1;
  padding: 16rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  background-color: #F8F9FA;
  color: #7F8C8D;
  font-size: 22rpx;
  text-align: center;
}

.type-btn.active {
  border-color: #3498DB;
  background-color: #E3F2FD;
  color: #2196F3;
}

.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #2C3E50;
  background-color: #F8F9FA;
}

.feedback-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #2C3E50;
  background-color: #F8F9FA;
}

.char-count {
  font-size: 22rpx;
  color: #95A5A6;
  text-align: right;
  margin-top: 8rpx;
  display: block;
}

.submit-btn {
  width: 100%;
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 16rpx;
}

.submit-btn.enabled {
  background-color: #3498DB;
  color: #FFFFFF;
}

.submit-btn.disabled {
  background-color: #BDC3C7;
  color: #FFFFFF;
}

/* 联系我们样式 */
.contact-list {
  padding: 16rpx 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #F8F9FA;
  transition: background-color 0.3s ease;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:active {
  background-color: #F8F9FA;
}

.contact-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.contact-content {
  flex: 1;
}

.contact-title {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.contact-desc {
  font-size: 22rpx;
  color: #7F8C8D;
}

.contact-action {
  font-size: 24rpx;
  color: #3498DB;
}

/* 应用信息样式 */
.app-info {
  padding: 16rpx 32rpx 32rpx 32rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F8F9FA;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #2C3E50;
}

.info-value {
  font-size: 24rpx;
  color: #7F8C8D;
}

/* 指南弹窗样式 */
.guide-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.guide-modal-content {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.guide-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.guide-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #2C3E50;
}

.guide-modal-close {
  font-size: 36rpx;
  color: #7F8C8D;
  padding: 8rpx;
}

.guide-modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.guide-modal-text {
  font-size: 26rpx;
  color: #2C3E50;
  line-height: 1.8;
  white-space: pre-line;
}

/* 动画 */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
