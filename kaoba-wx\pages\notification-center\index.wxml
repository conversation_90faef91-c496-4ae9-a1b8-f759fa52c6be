<!--pages/notification-center/index.wxml-->
<view class="container">
  <!-- 通知头部 -->
  <view class="header-container">
    <view class="header-top">
      <text class="page-title">通知中心</text>
      <view class="header-actions">
        <button class="mark-all-read-btn" wx:if="{{unreadCount > 0}}" bindtap="markAllAsRead">
          全部已读
        </button>
        <button class="settings-btn" bindtap="openNotificationSettings">⚙️</button>
      </view>
    </view>

    <view class="notification-stats">
      <view class="stat-item">
        <text class="stat-value">{{totalCount}}</text>
        <text class="stat-label">总通知</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{unreadCount}}</text>
        <text class="stat-label">未读</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{todayCount}}</text>
        <text class="stat-label">今日</text>
      </view>
    </view>
  </view>

  <!-- 通知筛选 -->
  <view class="filter-container">
    <view class="filter-tabs">
      <button class="filter-tab {{currentFilter === item.id ? 'active' : ''}}"
              wx:for="{{notificationFilters}}"
              wx:key="id"
              bindtap="switchFilter"
              data-filter="{{item.id}}">
        {{item.label}}
        <text class="filter-count" wx:if="{{item.count > 0}}">({{item.count}})</text>
      </button>
    </view>
  </view>

  <!-- 通知列表 -->
  <view class="notifications-container" wx:if="{{filteredNotifications.length > 0}}">
    <view class="notification-group" wx:for="{{groupedNotifications}}" wx:key="date">
      <view class="group-header">
        <text class="group-date">{{item.date}}</text>
        <text class="group-count">{{item.notifications.length}}条</text>
      </view>

      <view class="group-notifications">
        <view class="notification-item {{notification.read ? 'read' : 'unread'}}"
              wx:for="{{item.notifications}}"
              wx:for-item="notification"
              wx:key="id"
              bindtap="openNotification"
              bindlongpress="showNotificationActions"
              data-notification="{{notification}}">

          <view class="notification-content">
            <view class="notification-icon" style="background-color: {{notification.iconBg}}">
              <text>{{notification.icon}}</text>
            </view>

            <view class="notification-body">
              <view class="notification-header">
                <text class="notification-title">{{notification.title}}</text>
                <text class="notification-time">{{notification.time}}</text>
              </view>

              <text class="notification-message">{{notification.message}}</text>

              <view class="notification-actions" wx:if="{{notification.actions && notification.actions.length > 0}}">
                <button class="notification-action {{action.type === 'primary' ? 'primary' : 'secondary'}}"
                        wx:for="{{notification.actions}}"
                        wx:for-item="action"
                        wx:key="id"
                        bindtap="executeNotificationAction"
                        data-notification="{{notification}}"
                        data-action="{{action}}">
                  {{action.label}}
                </button>
              </view>
            </view>

            <view class="unread-indicator" wx:if="{{!notification.read}}"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{filteredNotifications.length === 0}}">
    <text class="empty-icon">🔔</text>
    <text class="empty-title">{{currentFilter === 'unread' ? '没有未读通知' : '暂无通知'}}</text>
    <text class="empty-subtitle">{{currentFilter === 'unread' ? '所有通知都已阅读' : '开启通知提醒，不错过重要信息'}}</text>

    <button class="enable-notifications-btn" wx:if="{{!notificationsEnabled}}" bindtap="enableNotifications">
      开启通知
    </button>
  </view>
</view>

<!-- 通知操作菜单 -->
<view class="action-sheet-mask" wx:if="{{showActionSheet}}" bindtap="hideActionSheet">
  <view class="action-sheet" catchtap="stopPropagation">
    <view class="action-sheet-header">
      <text class="action-sheet-title">通知操作</text>
      <text class="action-sheet-close" bindtap="hideActionSheet">×</text>
    </view>

    <view class="action-sheet-body">
      <button class="action-item" bindtap="markAsRead" wx:if="{{!selectedNotification.read}}">
        <text class="action-icon">✅</text>
        <text class="action-text">标记为已读</text>
      </button>

      <button class="action-item" bindtap="markAsUnread" wx:if="{{selectedNotification.read}}">
        <text class="action-icon">📧</text>
        <text class="action-text">标记为未读</text>
      </button>

      <button class="action-item" bindtap="deleteNotification">
        <text class="action-icon">🗑️</text>
        <text class="action-text">删除通知</text>
      </button>

      <button class="action-item" bindtap="muteNotificationType">
        <text class="action-icon">🔇</text>
        <text class="action-text">屏蔽此类通知</text>
      </button>
    </view>
  </view>
</view>

<!-- 通知设置弹窗 -->
<view class="settings-modal" wx:if="{{showSettingsModal}}" bindtap="hideSettingsModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">通知设置</text>
      <text class="modal-close" bindtap="hideSettingsModal">×</text>
    </view>

    <view class="modal-body">
      <view class="setting-section">
        <text class="setting-title">通知类型</text>

        <view class="setting-item" wx:for="{{notificationSettings}}" wx:key="type">
          <view class="setting-info">
            <text class="setting-name">{{item.name}}</text>
            <text class="setting-description">{{item.description}}</text>
          </view>
          <switch checked="{{item.enabled}}" bindchange="toggleNotificationType" data-type="{{item.type}}"/>
        </view>
      </view>

      <view class="setting-section">
        <text class="setting-title">提醒时间</text>

        <view class="setting-item">
          <text class="setting-name">免打扰时间</text>
          <view class="time-range">
            <picker mode="time" value="{{quietHours.start}}" bindchange="setQuietStart">
              <text>{{quietHours.start}}</text>
            </picker>
            <text>-</text>
            <picker mode="time" value="{{quietHours.end}}" bindchange="setQuietEnd">
              <text>{{quietHours.end}}</text>
            </picker>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>