<!--pages/task-detail/index.wxml-->
<view class="container">
  <!-- 任务头部信息 -->
  <view class="task-header-container">
    <view class="task-status-bar">
      <view class="status-indicator status-{{task.status}}"></view>
      <text class="status-text">{{task.statusText}}</text>
      <view class="task-actions">
        <button class="action-btn edit-btn" bindtap="editTask">编辑</button>
        <button class="action-btn more-btn" bindtap="showMoreActions">⋯</button>
      </view>
    </view>

    <view class="task-title-section">
      <text class="task-title">{{task.title}}</text>
      <view class="task-meta">
        <text class="task-subject">{{task.subject}}</text>
        <text class="task-exam" wx:if="{{task.examName}}">{{task.examName}}</text>
        <text class="task-priority priority-{{task.priority}}">{{task.priorityText}}</text>
      </view>
    </view>

    <view class="task-description" wx:if="{{task.description}}">
      <text class="description-text">{{task.description}}</text>
    </view>
  </view>

  <!-- 任务进度 -->
  <view class="progress-container">
    <view class="section-header">
      <text class="section-title">任务进度</text>
      <text class="progress-percentage">{{task.progress}}%</text>
    </view>

    <view class="progress-bar">
      <view class="progress-fill" style="width: {{task.progress}}%"></view>
    </view>

    <view class="progress-stats">
      <view class="stat-item">
        <text class="stat-label">已完成</text>
        <text class="stat-value">{{task.completedSubtasks || 0}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">总计</text>
        <text class="stat-value">{{task.totalSubtasks || 0}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">剩余</text>
        <text class="stat-value">{{(task.totalSubtasks || 0) - (task.completedSubtasks || 0)}}</text>
      </view>
    </view>
  </view>

  <!-- 时间信息 -->
  <view class="time-info-container">
    <view class="section-header">
      <text class="section-title">时间信息</text>
    </view>

    <view class="time-items">
      <view class="time-item">
        <text class="time-icon">📅</text>
        <view class="time-content">
          <text class="time-label">截止时间</text>
          <text class="time-value">{{task.dueDate}} {{task.dueTime}}</text>
        </view>
      </view>

      <view class="time-item">
        <text class="time-icon">⏱️</text>
        <view class="time-content">
          <text class="time-label">预计时长</text>
          <text class="time-value">{{task.estimatedDuration}}</text>
        </view>
      </view>

      <view class="time-item" wx:if="{{task.actualDuration}}">
        <text class="time-icon">⏰</text>
        <view class="time-content">
          <text class="time-label">实际时长</text>
          <text class="time-value">{{task.actualDuration}}</text>
        </view>
      </view>

      <view class="time-item">
        <text class="time-icon">📝</text>
        <view class="time-content">
          <text class="time-label">创建时间</text>
          <text class="time-value">{{task.createTime}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-container">
    <view class="action-grid">
      <button class="quick-action-btn start-btn" bindtap="startPomodoro">
        <text class="action-icon">🍅</text>
        <text class="action-text">开始专注</text>
      </button>

      <button class="quick-action-btn complete-btn" bindtap="completeTask" wx:if="{{!task.completed}}">
        <text class="action-icon">✅</text>
        <text class="action-text">标记完成</text>
      </button>

      <button class="quick-action-btn edit-btn" bindtap="editTask">
        <text class="action-icon">✏️</text>
        <text class="action-text">编辑任务</text>
      </button>

      <button class="quick-action-btn share-btn" bindtap="shareTask">
        <text class="action-icon">📤</text>
        <text class="action-text">分享</text>
      </button>
    </view>
  </view>
</view>