{"pageInfo": {"pageName": "任务详情", "pageId": "taskDetail", "pageType": "page", "description": "任务详细信息和学习记录页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "task-header", "type": "view", "className": "task-header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "task-status-bar", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "20rpx"}, "children": [{"id": "task-status", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "8rpx"}, "children": [{"id": "status-indicator", "type": "view", "style": {"width": "12rpx", "height": "12rpx", "borderRadius": "50%", "backgroundColor": "{{task.completed ? '#52C41A' : '#FAAD14'}}"}}, {"id": "status-text", "type": "text", "content": "{{task.completed ? '已完成' : '进行中'}}", "style": {"fontSize": "26rpx", "color": "#666666"}}]}, {"id": "task-actions", "type": "view", "style": {"display": "flex", "gap": "12rpx"}, "children": [{"id": "start-pomodoro-btn", "type": "button", "condition": "{{!task.completed}}", "content": "🍅", "style": {"fontSize": "24rpx", "backgroundColor": "#FFF2E8", "color": "#FA8C16", "border": "1rpx solid #FFD591", "borderRadius": "6rpx", "padding": "8rpx", "width": "48rpx", "height": "48rpx"}, "events": {"tap": "startPomodoroForTask"}}, {"id": "edit-task-btn", "type": "button", "content": "✏️", "style": {"fontSize": "24rpx", "backgroundColor": "#F6F6F6", "color": "#666666", "border": "1rpx solid #D9D9D9", "borderRadius": "6rpx", "padding": "8rpx", "width": "48rpx", "height": "48rpx"}, "events": {"tap": "editTask"}}]}]}, {"id": "task-title", "type": "text", "content": "{{task.title}}", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "12rpx", "textDecoration": "{{task.completed ? 'line-through' : 'none'}}"}}, {"id": "task-meta", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "16rpx", "marginBottom": "16rpx"}, "children": [{"id": "task-subject", "type": "view", "condition": "{{task.subject}}", "style": {"backgroundColor": "#E6F7FF", "color": "#1890FF", "fontSize": "22rpx", "padding": "4rpx 8rpx", "borderRadius": "4rpx"}, "children": [{"type": "text", "content": "{{task.subject}}"}]}, {"id": "task-priority", "type": "view", "condition": "{{task.priority === 'high'}}", "style": {"backgroundColor": "#FF4D4F", "color": "#FFFFFF", "fontSize": "20rpx", "padding": "2rpx 6rpx", "borderRadius": "4rpx"}, "children": [{"type": "text", "content": "高优先级"}]}, {"id": "task-duration", "type": "text", "condition": "{{task.estimatedDuration}}", "content": "预计 {{task.estimatedDuration}} 分钟", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "task-description", "type": "text", "condition": "{{task.description}}", "content": "{{task.description}}", "style": {"fontSize": "28rpx", "color": "#666666", "lineHeight": "1.5", "marginBottom": "20rpx"}}, {"id": "task-progress", "type": "view", "condition": "{{task.subtasks && task.subtasks.length > 0}}", "children": [{"id": "progress-header", "type": "view", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "12rpx"}, "children": [{"id": "progress-title", "type": "text", "content": "完成进度", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333"}}, {"id": "progress-text", "type": "text", "content": "{{task.completedSubtasks}}/{{task.totalSubtasks}}", "style": {"fontSize": "26rpx", "color": "#666666"}}]}, {"id": "progress-bar", "type": "view", "style": {"backgroundColor": "#F0F0F0", "borderRadius": "4rpx", "height": "8rpx", "position": "relative"}, "children": [{"id": "progress-fill", "type": "view", "style": {"backgroundColor": "#52C41A", "borderRadius": "4rpx", "height": "100%", "width": "{{task.progressPercentage}}%", "transition": "width 0.3s ease"}}]}]}]}, {"id": "subtasks-section", "type": "view", "condition": "{{task.subtasks && task.subtasks.length > 0}}", "className": "subtasks-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "subtasks-title", "type": "text", "content": "子任务", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "subtasks-list", "type": "view", "children": [{"id": "subtask-item", "type": "view", "forEach": "{{task.subtasks}}", "forItem": "subtask", "forIndex": "index", "style": {"display": "flex", "alignItems": "center", "padding": "16rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "subtask-checkbox", "type": "checkbox", "checked": "{{subtask.completed}}", "style": {"marginRight": "16rpx"}, "events": {"change": "toggleSubtaskComplete"}}, {"id": "subtask-title", "type": "text", "content": "{{subtask.title}}", "style": {"fontSize": "28rpx", "color": "{{subtask.completed ? '#999999' : '#333333'}}", "textDecoration": "{{subtask.completed ? 'line-through' : 'none'}}", "flex": "1"}}]}]}]}, {"id": "study-stats", "type": "view", "className": "study-stats-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "stats-title", "type": "text", "content": "学习统计", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "stats-grid", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr 1fr", "gap": "16rpx", "marginBottom": "24rpx"}, "children": [{"id": "total-time", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#E6F7FF", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{task.stats.totalTime}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#1890FF", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "总时长", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "pomodoro-count", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#FFF2E8", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{task.stats.pomodoroCount}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#FA8C16", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "番茄钟", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "efficiency", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#F6FFED", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{task.stats.efficiency}}%", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#52C41A", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "效率", "style": {"fontSize": "22rpx", "color": "#666666"}}]}]}, {"id": "recent-sessions", "type": "view", "condition": "{{task.recentSessions.length > 0}}", "children": [{"id": "sessions-title", "type": "text", "content": "最近学习", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "sessions-list", "type": "view", "children": [{"id": "session-item", "type": "view", "forEach": "{{task.recentSessions}}", "forItem": "session", "forIndex": "index", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "12rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "session-info", "type": "view", "children": [{"id": "session-date", "type": "text", "content": "{{session.date}}", "style": {"fontSize": "26rpx", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "session-time", "type": "text", "content": "{{session.startTime}} - {{session.endTime}}", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "session-duration", "type": "text", "content": "{{session.duration}}", "style": {"fontSize": "26rpx", "fontWeight": "500", "color": "#52C41A"}}]}]}]}]}, {"id": "task-completion", "type": "view", "condition": "{{!task.completed}}", "className": "completion-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "completion-title", "type": "text", "content": "完成任务", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "completion-tip", "type": "text", "content": "确认完成这个任务吗？完成后将记录到学习统计中。", "style": {"fontSize": "26rpx", "color": "#666666", "lineHeight": "1.4", "marginBottom": "24rpx"}}, {"id": "complete-task-btn", "type": "button", "content": "标记为完成", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#52C41A", "borderRadius": "8rpx", "padding": "16rpx", "border": "none", "width": "100%"}, "events": {"tap": "completeTask"}}]}], "data": {"taskId": "", "task": {"id": "task_001", "title": "数学高数第一章复习", "subject": "数学", "description": "复习极限的概念和计算方法，包括数列极限和函数极限", "completed": false, "priority": "high", "estimatedDuration": 90, "subtasks": [{"id": "subtask_001", "title": "阅读教材第一章", "completed": true}, {"id": "subtask_002", "title": "完成课后习题1-10", "completed": true}, {"id": "subtask_003", "title": "整理知识点笔记", "completed": false}, {"id": "subtask_004", "title": "做历年真题", "completed": false}], "completedSubtasks": 2, "totalSubtasks": 4, "progressPercentage": 50, "stats": {"totalTime": "3.5h", "pomodoroCount": 7, "efficiency": 85}, "recentSessions": [{"id": "session_001", "date": "今天", "startTime": "14:30", "endTime": "15:15", "duration": "45分钟"}, {"id": "session_002", "date": "昨天", "startTime": "19:00", "endTime": "20:30", "duration": "1.5小时"}, {"id": "session_003", "date": "前天", "startTime": "10:00", "endTime": "11:25", "duration": "1小时25分钟"}]}}, "methods": {"startPomodoroForTask": {"type": "navigate", "url": "/pages/pomodoro/index", "params": ["taskId"]}, "editTask": {"type": "navigate", "url": "/pages/task-center/edit-task", "params": ["taskId"]}, "toggleSubtaskComplete": {"type": "function", "description": "切换子任务完成状态", "params": ["subtaskId"], "implementation": "updateSubtaskStatus"}, "completeTask": {"type": "function", "description": "完成任务", "implementation": "markTaskComplete"}}, "lifecycle": {"onLoad": ["loadTaskDetail"], "onShow": ["refreshTaskData"]}}