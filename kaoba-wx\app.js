// app.js
const DatabaseInit = require('./utils/dbInit')
const CloudTest = require('./utils/cloudTest')
const SmartApi = require('./utils/smartApi')

App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        // 环境 ID
        env: 'cloud1-8gvo8etxe4c004b3',
        traceUser: true,
      })
      console.log('云开发初始化成功')

      // 延迟初始化数据，确保云开发完全初始化
      setTimeout(async () => {
        // 使用智能API初始化数据
        const initResult = await SmartApi.initData()
        if (initResult.success) {
          console.log('数据初始化成功:', initResult.message)
          this.globalData.dbInitialized = true

          // 获取API状态
          const apiStatus = await SmartApi.getApiStatus()
          console.log('API状态:', apiStatus)
          this.globalData.apiStatus = apiStatus
        } else {
          console.error('数据初始化失败:', initResult.error)
        }
      }, 1000)
    }

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        console.log('登录成功', res.code)
      }
    })

    // 检查是否是首次启动
    this.checkFirstLaunch()
  },

  // 初始化数据库
  async initDatabase() {
    try {
      // 确保云开发已经初始化
      if (!wx.cloud) {
        console.error('云开发未初始化')
        return
      }

      const result = await DatabaseInit.checkAndInit()
      if (result.success) {
        console.log('数据库初始化完成:', result.message || '成功')
        this.globalData.dbInitialized = true
      } else {
        console.error('数据库初始化失败:', result.error)
      }
    } catch (error) {
      console.error('数据库初始化异常:', error)
      // 如果初始化失败，标记为已初始化以避免重复尝试
      this.globalData.dbInitialized = true
    }
  },

  onShow() {
    // 应用被用户重新打开时触发
    console.log('应用显示')
  },

  onHide() {
    // 应用被用户隐藏时触发
    console.log('应用隐藏')
  },

  onError(error) {
    // 应用发生错误时触发
    console.error('应用错误:', error)
  },

  // 检查是否首次启动
  checkFirstLaunch() {
    const hasLaunched = wx.getStorageSync('hasLaunched')
    if (!hasLaunched) {
      // 首次启动，设置标记
      wx.setStorageSync('hasLaunched', true)
      this.globalData.isFirstLaunch = true
    } else {
      this.globalData.isFirstLaunch = false
    }
  },

  // 测试云开发功能
  async testCloudDevelopment() {
    const result = await CloudTest.runFullTest()
    console.log('云开发测试结果:', result)
    return result
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isFirstLaunch: false,
    version: '1.0.0',
    dbInitialized: false,
    cloudEnv: 'cloud1-8gvo8etxe4c004b3',
    apiStatus: null
  }
})
