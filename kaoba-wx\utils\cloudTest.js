// 云开发测试工具
class CloudTest {
  // 测试云开发连接
  static async testConnection() {
    try {
      console.log('开始测试云开发连接...')
      
      // 检查云开发是否初始化
      if (!wx.cloud) {
        return { success: false, error: '云开发未初始化' }
      }

      // 测试调用登录云函数
      const loginResult = await wx.cloud.callFunction({
        name: 'login'
      })
      
      console.log('登录测试结果:', loginResult)
      
      if (loginResult.result) {
        console.log('云开发连接测试成功')
        return { 
          success: true, 
          data: {
            openid: loginResult.result.openid,
            appid: loginResult.result.appid
          }
        }
      } else {
        return { success: false, error: '登录云函数调用失败' }
      }
    } catch (error) {
      console.error('云开发连接测试失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 测试数据库连接
  static async testDatabase() {
    try {
      console.log('开始测试数据库连接...')
      
      const db = wx.cloud.database()
      
      // 尝试获取tasks集合的数据
      const result = await db.collection('tasks').limit(1).get()
      
      console.log('数据库测试结果:', result)
      
      return { 
        success: true, 
        data: {
          hasData: result.data.length > 0,
          count: result.data.length
        }
      }
    } catch (error) {
      console.error('数据库连接测试失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 创建测试任务
  static async createTestTask() {
    try {
      console.log('开始创建测试任务...')
      
      const db = wx.cloud.database()
      
      const testTask = {
        title: '测试任务',
        subject: '测试',
        priority: 'medium',
        estimatedTime: 30,
        dueDate: new Date(),
        description: '这是一个测试任务',
        completed: false,
        createTime: new Date(),
        updateTime: new Date()
      }
      
      const result = await db.collection('tasks').add({
        data: testTask
      })
      
      console.log('测试任务创建结果:', result)
      
      return { 
        success: true, 
        data: {
          _id: result._id,
          ...testTask
        }
      }
    } catch (error) {
      console.error('创建测试任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 运行完整测试
  static async runFullTest() {
    console.log('=== 开始云开发完整测试 ===')
    
    const results = {
      connection: await this.testConnection(),
      database: await this.testDatabase(),
      createTask: await this.createTestTask()
    }
    
    console.log('=== 云开发测试完成 ===')
    console.log('测试结果:', results)
    
    const allSuccess = Object.values(results).every(result => result.success)
    
    return {
      success: allSuccess,
      results: results,
      summary: allSuccess ? '所有测试通过' : '部分测试失败'
    }
  }
}

module.exports = CloudTest
