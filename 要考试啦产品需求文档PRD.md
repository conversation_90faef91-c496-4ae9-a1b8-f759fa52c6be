# "要考试啦"产品需求文档（PRD）

## 文档信息
- **产品名称**：要考试啦
- **文档版本**：V1.0
- **创建日期**：2024年12月
- **文档类型**：产品需求文档（PRD）
- **目标版本**：MVP版本

## 一、产品概述

### 1.1 产品定位
基于科学理论的考试复习效率工具，专注微信小程序生态的即用即走体验。

### 1.2 产品目标
- **用户目标**：帮助考试人群科学规划复习时间，提升复习效率，建立考试信心
- **商业目标**：通过广告流量变现，实现月收入20万元以上
- **产品目标**：成为考试复习场景的专业工具，DAU达到10万+

### 1.3 目标用户
- **主要用户**：高中生（16-18岁）、大学生（18-22岁）
- **次要用户**：考研学生、职场考试人群
- **用户规模**：目标18个月内达到10万DAU

## 二、功能需求

### 2.1 功能架构图

```
要考试啦小程序
├── 首页
│   ├── 考试倒计时
│   ├── 今日任务
│   └── 快捷操作
├── 考试中心
│   ├── 考试管理
│   ├── 目标设定
│   └── 倒计时设置
├── 任务规划
│   ├── 任务创建
│   ├── 计划制定
│   └── 进度跟踪
├── 效率工具
│   ├── 番茄钟
│   ├── 专注计时
│   └── 时间记录
├── 数据中心
│   ├── 复习统计
│   ├── 效率分析
│   └── 趋势图表
└── 我的
    ├── 个人设置
    ├── 分享功能
    └── 帮助反馈
```

### 2.2 MVP版本功能需求（P0）

#### 2.2.1 首页模块

**需求描述**：用户打开小程序的第一个页面，需要快速展示最重要的信息和操作入口。

**功能要求**：
1. **考试倒计时显示**
   - 显示最近的1-2个考试倒计时
   - 大字体显示剩余天数
   - 颜色区分紧急程度（红色<7天，橙色<30天，绿色>30天）
   - 点击可进入考试详情页

2. **今日任务列表**
   - 显示当天的复习任务（最多显示3-5个）
   - 任务状态：未开始、进行中、已完成
   - 显示预估时间和重要程度
   - 支持快速标记完成

3. **快捷操作区**
   - 开始复习按钮（一键启动番茄钟）
   - 添加任务按钮
   - 查看进度按钮

**交互要求**：
- 页面加载时间<1秒
- 支持下拉刷新
- 倒计时实时更新
- 任务完成有动画反馈

**UI要求**：
- 倒计时区域占据首屏1/3空间
- 使用渐变色背景增强视觉冲击
- 任务列表简洁清晰，易于扫描
- 快捷操作按钮突出显示

#### 2.2.2 考试中心模块

**需求描述**：管理用户的所有考试信息，包括添加、编辑、删除考试。

**功能要求**：
1. **考试列表**
   - 显示所有考试，按时间排序
   - 每个考试显示：名称、时间、倒计时、状态
   - 支持考试状态：未开始、进行中、已结束
   - 支持左滑删除操作

2. **添加考试**
   - 考试名称（必填，最多20字符）
   - 考试时间（必填，日期+时间选择器）
   - 考试科目（可选，支持多选）
   - 目标分数（可选，数字输入）
   - 重要程度（可选，高/中/低）
   - 备注信息（可选，最多100字符）

3. **编辑考试**
   - 支持修改所有考试信息
   - 修改后自动重新计算倒计时
   - 支持考试状态变更

4. **考试详情**
   - 显示考试完整信息
   - 显示相关的复习任务
   - 显示复习进度统计
   - 提供快捷操作（添加任务、开始复习）

**交互要求**：
- 添加考试流程不超过3步
- 表单验证实时反馈
- 操作确认提示（删除等）
- 支持快速复制已有考试

**UI要求**：
- 考试卡片设计，信息层次清晰
- 倒计时数字突出显示
- 重要程度用颜色标识
- 操作按钮位置合理

#### 2.2.3 任务规划模块

**需求描述**：帮助用户创建和管理复习任务，跟踪复习进度。

**功能要求**：
1. **任务列表**
   - 按状态分组显示：今日任务、本周任务、全部任务
   - 任务信息：标题、科目、预估时间、重要程度、截止时间
   - 任务状态：未开始、进行中、已完成、已过期
   - 支持筛选和排序（按时间、重要程度、科目）

2. **创建任务**
   - 任务标题（必填，最多30字符）
   - 关联考试（必填，下拉选择）
   - 任务科目（可选，与考试科目关联）
   - 预估时间（可选，默认25分钟）
   - 重要程度（可选，高/中/低，默认中）
   - 截止时间（可选，默认考试前一天）
   - 任务描述（可选，最多200字符）

3. **任务管理**
   - 支持编辑任务信息
   - 支持删除任务（需确认）
   - 支持批量操作（标记完成、删除）
   - 支持任务复制和模板保存

4. **进度跟踪**
   - 任务完成率统计
   - 各科目进度对比
   - 每日完成情况
   - 进度可视化图表

**交互要求**：
- 任务创建流程简化，核心信息一屏完成
- 支持快速添加（预设模板）
- 任务状态切换流畅
- 进度更新实时反馈

**UI要求**：
- 任务卡片设计，信息密度适中
- 状态用颜色和图标区分
- 进度条可视化显示
- 操作按钮易于点击

#### 2.2.4 效率工具模块

**需求描述**：提供番茄钟等时间管理工具，帮助用户专注复习。

**功能要求**：
1. **番茄钟功能**
   - 标准模式：25分钟专注+5分钟休息
   - 自定义模式：用户可设置专注和休息时间
   - 计时界面：大字体显示剩余时间
   - 状态提示：专注中、休息中、暂停
   - 完成提醒：声音+震动+弹窗提醒

2. **专注计时**
   - 长时间专注模式（不限时长）
   - 计时开始/暂停/结束
   - 后台计时支持
   - 计时记录保存

3. **时间记录**
   - 自动记录每次计时时长
   - 关联到具体任务和科目
   - 支持手动调整时间
   - 支持添加复习效果评分（1-10分）

4. **计时统计**
   - 今日/本周/本月计时统计
   - 各科目时间分布
   - 番茄钟完成个数统计
   - 平均专注时长分析

**交互要求**：
- 计时器启动/暂停操作简单
- 计时过程中界面简洁，减少干扰
- 时间到达时提醒明显但不突兀
- 支持快速重新开始

**UI要求**：
- 计时界面采用圆形进度条设计
- 时间数字大而清晰
- 操作按钮大且易于点击
- 专注模式界面简洁，颜色舒缓

#### 2.2.5 数据中心模块

**需求描述**：展示用户的复习数据统计和分析，帮助用户了解复习效果。

**功能要求**：
1. **复习统计**
   - 总复习时长（今日/本周/本月/总计）
   - 任务完成数量统计
   - 番茄钟完成个数
   - 连续复习天数

2. **科目分析**
   - 各科目复习时间分布（饼图）
   - 各科目任务完成率
   - 科目复习效率对比
   - 薄弱科目识别

3. **效率分析**
   - 每日复习时长趋势（折线图）
   - 不同时段复习效率对比
   - 复习方法效果分析
   - 个人最佳复习时段识别

4. **目标达成**
   - 考试准备进度
   - 目标完成率
   - 剩余时间分配建议
   - 复习计划调整建议

**交互要求**：
- 图表支持点击查看详情
- 数据更新实时反映
- 支持时间范围切换
- 支持数据导出分享

**UI要求**：
- 使用图表库展示数据，美观易读
- 关键数据突出显示
- 颜色搭配协调，符合品牌色调
- 支持横屏查看详细图表

### 2.3 V1.0版本功能需求（P1）

#### 2.3.1 智能功能
1. **智能计划生成**
   - 基于考试时间和任务量自动生成复习计划
   - 考虑用户历史数据和复习效率
   - 支持计划调整和优化建议

2. **个性化推荐**
   - 基于用户行为推荐最佳复习时段
   - 推荐适合的复习方法
   - 推荐任务优先级排序

#### 2.3.2 社交功能
1. **进度分享**
   - 生成复习进度海报
   - 支持分享到微信朋友圈/群聊
   - 倒计时分享卡片

2. **考试群组**
   - 创建考试专属群聊
   - 群内进度排行榜
   - 群成员互相监督

#### 2.3.3 高级分析
1. **深度数据分析**
   - 复习效率预测模型
   - 考试通过概率评估
   - 个人复习模式分析

2. **对比分析**
   - 与历史考试对比
   - 与同类用户对比
   - 最佳实践推荐

### 2.4 平台化版本功能需求（P2 - 18个月后）

#### 2.4.1 培训机构管理
1. **机构入驻系统**
   - 机构资质认证流程
   - 机构信息管理
   - 课程发布和管理
   - 学员管理系统

2. **机构服务功能**
   - 课程预约和安排
   - 学员进度跟踪
   - 教学质量评估
   - 收入分成结算

#### 2.4.2 专业老师管理
1. **老师入驻系统**
   - 老师资质认证
   - 个人简介和专长展示
   - 教学经验和成果展示
   - 服务价格设定

2. **一对一辅导服务**
   - 在线预约系统
   - 视频通话功能
   - 辅导记录管理
   - 学习效果跟踪

#### 2.4.3 智能匹配系统
1. **需求分析引擎**
   - 用户考试类型分析
   - 学习能力评估
   - 时间安排分析
   - 预算范围确定

2. **匹配推荐算法**
   - 基于需求匹配合适老师
   - 推荐专业培训机构
   - 个性化学习方案生成
   - 匹配成功率优化

#### 2.4.4 服务质量保障
1. **评价体系**
   - 老师和机构评分系统
   - 用户反馈收集
   - 服务质量监控
   - 问题处理机制

2. **质量控制**
   - 服务标准制定
   - 定期质量检查
   - 不合格服务处理
   - 用户满意度调研

## 三、非功能需求

### 3.1 性能需求
- **页面加载时间**：首页<1秒，其他页面<2秒
- **操作响应时间**：用户操作响应<0.5秒
- **数据同步时间**：本地数据同步到云端<3秒
- **并发用户数**：支持10万并发用户访问

### 3.2 可用性需求
- **系统可用性**：99.9%以上
- **错误恢复**：系统错误后能自动恢复
- **数据备份**：用户数据自动备份，支持恢复
- **离线使用**：核心功能支持离线使用

### 3.3 兼容性需求
- **微信版本**：支持微信7.0以上版本
- **手机系统**：支持iOS 10+、Android 6.0+
- **屏幕适配**：支持主流手机屏幕尺寸
- **网络环境**：支持2G/3G/4G/5G/WiFi网络

### 3.4 安全需求
- **数据加密**：用户敏感数据加密存储
- **隐私保护**：严格遵守用户隐私保护规范
- **数据安全**：防止数据泄露和恶意攻击
- **权限控制**：最小化权限申请原则

## 四、技术需求

### 4.1 技术架构
```
前端：微信小程序
├── 框架：微信小程序原生框架
├── UI组件：WeUI + 自定义组件
├── 状态管理：小程序全局数据管理
└── 本地存储：wx.storage API

后端：Node.js服务
├── 框架：Express.js
├── 数据库：MongoDB
├── 缓存：Redis
└── 云服务：腾讯云/阿里云
```

### 4.2 数据库设计

#### 4.2.1 用户表（users）
```javascript
{
  _id: ObjectId,
  openid: String, // 微信openid
  nickname: String, // 用户昵称
  avatar: String, // 头像URL
  createTime: Date, // 注册时间
  lastLoginTime: Date, // 最后登录时间
  settings: {
    defaultPomodoroTime: Number, // 默认番茄钟时长
    defaultBreakTime: Number, // 默认休息时长
    reminderEnabled: Boolean, // 是否开启提醒
    soundEnabled: Boolean // 是否开启声音
  }
}
```

#### 4.2.2 考试表（exams）
```javascript
{
  _id: ObjectId,
  userId: ObjectId, // 用户ID
  name: String, // 考试名称
  examTime: Date, // 考试时间
  subjects: [String], // 考试科目
  targetScore: Number, // 目标分数
  importance: String, // 重要程度：high/medium/low
  status: String, // 状态：upcoming/ongoing/finished
  notes: String, // 备注
  createTime: Date,
  updateTime: Date
}
```

#### 4.2.3 任务表（tasks）
```javascript
{
  _id: ObjectId,
  userId: ObjectId, // 用户ID
  examId: ObjectId, // 关联考试ID
  title: String, // 任务标题
  subject: String, // 科目
  estimatedTime: Number, // 预估时间（分钟）
  importance: String, // 重要程度
  deadline: Date, // 截止时间
  status: String, // 状态：pending/in_progress/completed/overdue
  description: String, // 任务描述
  createTime: Date,
  updateTime: Date,
  completedTime: Date // 完成时间
}
```

#### 4.2.4 学习记录表（study_records）
```javascript
{
  _id: ObjectId,
  userId: ObjectId, // 用户ID
  taskId: ObjectId, // 关联任务ID
  examId: ObjectId, // 关联考试ID
  subject: String, // 科目
  studyTime: Number, // 学习时长（分钟）
  method: String, // 学习方法：pomodoro/focus/other
  efficiency: Number, // 效率评分（1-10）
  startTime: Date, // 开始时间
  endTime: Date, // 结束时间
  notes: String // 学习笔记
}
```

### 4.3 API接口设计

#### 4.3.1 用户相关接口
```
POST /api/user/login - 用户登录
GET /api/user/profile - 获取用户信息
PUT /api/user/profile - 更新用户信息
PUT /api/user/settings - 更新用户设置
```

#### 4.3.2 考试相关接口
```
GET /api/exams - 获取考试列表
POST /api/exams - 创建考试
GET /api/exams/:id - 获取考试详情
PUT /api/exams/:id - 更新考试信息
DELETE /api/exams/:id - 删除考试
```

#### 4.3.3 任务相关接口
```
GET /api/tasks - 获取任务列表
POST /api/tasks - 创建任务
GET /api/tasks/:id - 获取任务详情
PUT /api/tasks/:id - 更新任务信息
DELETE /api/tasks/:id - 删除任务
PUT /api/tasks/:id/status - 更新任务状态
```

#### 4.3.4 学习记录相关接口
```
GET /api/study-records - 获取学习记录
POST /api/study-records - 创建学习记录
GET /api/study-records/stats - 获取学习统计
GET /api/study-records/analysis - 获取学习分析
```

### 4.4 第三方服务集成

#### 4.4.1 微信服务
- 微信登录：获取用户openid和基本信息
- 微信支付：未来付费功能预留
- 微信分享：分享到朋友圈和群聊
- 微信模板消息：重要提醒推送

#### 4.4.2 云服务
- 对象存储：用户头像、分享图片存储
- CDN加速：静态资源加速
- 数据库：MongoDB云数据库
- 缓存服务：Redis缓存

#### 4.4.3 数据分析
- 用户行为分析：统计用户使用行为
- 性能监控：监控系统性能指标
- 错误日志：收集和分析错误信息

## 五、UI/UX设计要求

### 5.1 设计原则
- **简洁性**：界面简洁清晰，信息层次分明
- **一致性**：保持设计风格和交互方式一致
- **易用性**：操作简单直观，学习成本低
- **专业性**：体现考试工具的专业感

### 5.2 视觉设计

#### 5.2.1 色彩规范
- **主色调**：#1890FF（蓝色，代表专业和信任）
- **辅助色**：#52C41A（绿色，代表完成和成功）
- **警告色**：#FA8C16（橙色，代表提醒和注意）
- **错误色**：#F5222D（红色，代表紧急和错误）
- **文字色**：#262626（深灰，主要文字）、#8C8C8C（浅灰，次要文字）

#### 5.2.2 字体规范
- **主标题**：28rpx，加粗
- **副标题**：24rpx，加粗
- **正文**：28rpx，常规
- **辅助文字**：24rpx，常规
- **数字显示**：32rpx，加粗（倒计时等重要数字）

#### 5.2.3 间距规范
- **页面边距**：32rpx
- **组件间距**：24rpx
- **元素间距**：16rpx
- **文字行距**：1.5倍

### 5.3 交互设计

#### 5.3.1 导航设计
- 使用微信小程序标准tabBar导航
- 主要页面：首页、考试、任务、工具、我的
- 支持页面间的流畅跳转
- 重要操作提供面包屑导航

#### 5.3.2 反馈设计
- 操作成功：绿色提示 + 成功图标
- 操作失败：红色提示 + 错误图标
- 加载状态：loading动画 + 提示文字
- 空状态：友好的空状态插图 + 引导文字

#### 5.3.3 动画设计
- 页面切换：平滑的过渡动画
- 状态变化：适当的变化动画
- 成功反馈：庆祝动画效果
- 加载等待：有趣的loading动画

### 5.4 响应式设计
- 适配不同屏幕尺寸的手机
- 横屏模式下的布局优化
- 考虑不同分辨率的显示效果
- 支持系统字体大小设置

## 六、测试需求

### 6.1 功能测试
- **核心功能测试**：考试管理、任务管理、番茄钟等核心功能
- **边界条件测试**：极限数据、异常输入的处理
- **业务流程测试**：完整的用户使用流程测试
- **兼容性测试**：不同设备、系统版本的兼容性

### 6.2 性能测试
- **响应时间测试**：各功能的响应时间测试
- **并发测试**：多用户同时使用的性能测试
- **压力测试**：系统在高负载下的表现
- **内存测试**：内存使用情况和泄漏检测

### 6.3 用户体验测试
- **可用性测试**：真实用户的使用体验测试
- **A/B测试**：不同设计方案的效果对比
- **用户反馈收集**：收集用户意见和建议
- **迭代优化**：基于测试结果持续优化

## 七、发布计划

### 7.1 开发阶段
- **需求确认**：1周
- **UI设计**：2周
- **前端开发**：4周
- **后端开发**：3周
- **联调测试**：1周
- **总计**：11周

### 7.2 测试阶段
- **内部测试**：1周
- **小范围用户测试**：2周
- **问题修复和优化**：1周
- **总计**：4周

### 7.3 发布阶段
- **提交微信审核**：3-7天
- **正式发布**：1天
- **发布后监控**：持续

### 7.4 里程碑计划

#### 工具阶段里程碑（0-18个月）
- **Week 4**：UI设计完成
- **Week 8**：前端开发完成
- **Week 11**：后端开发完成
- **Week 12**：联调测试完成
- **Week 15**：用户测试完成
- **Week 16**：正式发布
- **Month 6**：达到1万DAU
- **Month 12**：达到5万DAU
- **Month 18**：达到10万DAU，开始平台化准备

#### 平台化阶段里程碑（18个月+）
- **Month 19-21**：平台化功能开发
- **Month 22**：培训机构招募启动
- **Month 23**：专业老师招募启动
- **Month 24**：平台化功能正式上线
- **Month 30**：达到20万DAU，50+机构入驻
- **Month 36**：达到50万DAU，200+机构，1000+老师

## 八、风险评估

### 8.1 技术风险
- **微信政策变化**：微信小程序政策可能影响功能实现
- **性能问题**：用户量增长可能导致性能瓶颈
- **数据安全**：用户数据安全和隐私保护
- **第三方依赖**：依赖的第三方服务稳定性

### 8.2 产品风险
- **用户需求变化**：用户需求可能与预期不符
- **竞品压力**：竞争对手推出类似产品
- **用户留存**：用户留存率可能低于预期
- **商业化困难**：广告变现效果可能不理想

### 8.3 运营风险
- **获客成本高**：用户获取成本可能超出预算
- **内容审核**：分享内容可能涉及审核问题
- **用户投诉**：产品问题可能导致用户投诉
- **法律合规**：需要符合相关法律法规要求

### 8.4 风险应对
- **技术风险**：选择稳定的技术方案，做好备选方案
- **产品风险**：快速迭代，及时响应用户反馈
- **运营风险**：多渠道获客，建立用户服务体系
- **持续监控**：建立风险监控机制，及时发现和处理问题

## 九、成功指标

### 9.1 用户指标
- **用户注册量**：6个月内达到5万注册用户
- **日活跃用户**：6个月内达到1万DAU
- **用户留存率**：7日留存率>40%，30日留存率>20%
- **用户满意度**：应用评分>4.5分

### 9.2 产品指标
- **功能使用率**：核心功能使用率>80%
- **任务完成率**：用户创建任务的完成率>60%
- **分享传播率**：用户主动分享比例>15%
- **问题反馈率**：严重问题反馈率<1%

### 9.3 商业指标

#### 工具阶段商业指标（0-18个月）
- **广告收入**：6个月内月收入达到5万元
- **获客成本**：平均获客成本<5元
- **用户价值**：单用户月均价值>3元
- **投资回报**：6个月内实现盈亏平衡

#### 平台化阶段商业指标（18个月+）
- **平台收入**：24个月内月收入达到100万元
- **机构入驻**：24个月内入驻机构50+
- **老师入驻**：24个月内入驻老师200+
- **服务成交**：月成交一对一辅导服务1000+次
- **平台抽成**：平台服务费收入占总收入60%+

## 十、附录

### 10.1 术语表
- **DAU**：Daily Active Users，日活跃用户数
- **MAU**：Monthly Active Users，月活跃用户数
- **LTV**：Life Time Value，用户生命周期价值
- **CAC**：Customer Acquisition Cost，用户获取成本
- **MVP**：Minimum Viable Product，最小可行产品

### 10.2 参考资料
- 微信小程序开发文档
- 时间管理理论研究资料
- 竞品分析报告
- 用户调研报告

### 10.3 变更记录
- V1.0：初始版本，完成MVP功能需求定义

---

**文档结束**

本PRD文档详细定义了"要考试啦"小程序的产品需求，为开发团队提供了明确的开发指导。文档将根据开发过程中的实际情况和用户反馈持续更新和完善。
