/* custom-tab-bar/index.wxss */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #FFFFFF;
  display: flex;
  border-top: 1rpx solid #E5E5E5;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
}

.tab-icon {
  margin-bottom: 4rpx;
}

.icon-text {
  font-size: 44rpx;
  color: #999999;
  transition: color 0.3s ease;
}

.icon-text.active {
  color: #1890FF;
}

.tab-text {
  font-size: 20rpx;
  color: #999999;
  transition: color 0.3s ease;
}

.tab-text.active {
  color: #1890FF;
}
