/* pages/exam-center/index.wxss */

/* 顶部统计 */
.stats-header {
  margin-bottom: 24rpx;
}

.stats-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.stats-item {
  text-align: center;
}

.stats-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  display: block;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 22rpx;
  color: #666666;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24rpx;
}

.filter-tabs {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: #1890FF;
  color: #FFFFFF;
}

.filter-tab text {
  font-size: 26rpx;
  font-weight: 500;
}

.tab-count {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background-color: #FF4D4F;
  color: #FFFFFF;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
  min-width: 16rpx;
  height: 16rpx;
  line-height: 16rpx;
  text-align: center;
}

.filter-tab.active .tab-count {
  background-color: rgba(255,255,255,0.3);
}

.search-bar {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.search-icon {
  font-size: 28rpx;
  color: #999999;
}

.search-placeholder {
  font-size: 26rpx;
  color: #999999;
  flex: 1;
}

/* 考试列表 */
.exams-section {
  margin-bottom: 24rpx;
}

.exam-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  position: relative;
}

.exam-status-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
}

.status-preparing {
  background-color: #1890FF;
}

.status-upcoming {
  background-color: #FA8C16;
}

.status-completed {
  background-color: #52C41A;
}

.exam-content {
  padding: 32rpx;
  padding-left: 38rpx;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.exam-info {
  flex: 1;
}

.exam-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.exam-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.exam-type {
  font-size: 22rpx;
  color: #1890FF;
  background-color: #E6F7FF;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.exam-date {
  font-size: 22rpx;
  color: #666666;
}

.exam-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.exam-status-text {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.exam-status-text.status-preparing {
  background-color: #E6F7FF;
  color: #1890FF;
}

.exam-status-text.status-upcoming {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.exam-status-text.status-completed {
  background-color: #F6FFED;
  color: #52C41A;
}

.more-icon {
  font-size: 24rpx;
  color: #999999;
  padding: 8rpx;
}

/* 倒计时 */
.exam-countdown {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
}

.countdown-item {
  text-align: center;
  min-width: 60rpx;
}

.countdown-number {
  font-size: 28rpx;
  font-weight: 700;
  color: #FF4D4F;
  display: block;
  margin-bottom: 4rpx;
}

.countdown-unit {
  font-size: 20rpx;
  color: #666666;
}

/* 进度条 */
.exam-progress {
  margin-bottom: 24rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #666666;
}

.progress-percentage {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.progress-bar {
  background-color: #F0F0F0;
  border-radius: 8rpx;
  height: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

/* 科目列表 */
.exam-subjects {
  margin-bottom: 24rpx;
}

.subject-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.subject-name {
  font-size: 24rpx;
  color: #333333;
  min-width: 80rpx;
}

.subject-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-left: 16rpx;
}

.subject-progress-bar {
  flex: 1;
  background-color: #F0F0F0;
  border-radius: 4rpx;
  height: 6rpx;
  overflow: hidden;
}

.subject-progress-fill {
  background-color: #52C41A;
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.subject-progress-text {
  font-size: 20rpx;
  color: #666666;
  min-width: 40rpx;
}

/* 快捷操作 */
.exam-quick-actions {
  display: flex;
  gap: 12rpx;
}

.quick-action-btn {
  flex: 1;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 8rpx;
  padding: 12rpx 8rpx;
  font-size: 22rpx;
  color: #666666;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.quick-action-btn:active {
  background-color: #E9ECEF;
}

.action-icon {
  font-size: 20rpx;
}

.action-text {
  font-size: 20rpx;
}

/* 空状态 */
.empty-state {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
}

.empty-message {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

/* 添加按钮 */
.add-exam-section {
  margin-bottom: 24rpx;
}

.add-exam-btn {
  background-color: #FFFFFF;
  border: 2rpx dashed #1890FF;
  border-radius: 16rpx;
  padding: 32rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  color: #1890FF;
}

.add-exam-btn:active {
  background-color: #F0F9FF;
}

.add-icon {
  font-size: 48rpx;
  font-weight: 300;
}

.add-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 操作菜单 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.action-sheet {
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}

.action-sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.action-sheet-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.action-sheet-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.action-sheet-body {
  padding: 16rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  gap: 16rpx;
}

.action-item:active {
  background-color: #F8F9FA;
}

.action-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
}

.action-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.action-arrow {
  font-size: 24rpx;
  color: #999999;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
