/* pages/settings/index.wxss */

/* 容器 */
.container {
  background-color: #F8F9FA;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.header {
  background-color: #FFFFFF;
  padding: 40rpx 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin-bottom: 32rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2C3E50;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #7F8C8D;
}

/* 设置内容 */
.settings-content {
  padding: 0 32rpx;
}

/* 设置分组 */
.setting-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2C3E50;
  padding: 32rpx 32rpx 16rpx 32rpx;
  display: block;
  border-bottom: 1rpx solid #F0F0F0;
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F8F9FA;
  transition: background-color 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #F8F9FA;
}

.setting-info {
  flex: 1;
}

.setting-name {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.setting-desc {
  font-size: 22rpx;
  color: #7F8C8D;
  line-height: 1.4;
}

.setting-arrow {
  font-size: 24rpx;
  color: #BDC3C7;
}

/* 时间调节控件 */
.setting-control {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 1rpx solid #E9ECEF;
  background-color: #F8F9FA;
  color: #7F8C8D;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-btn:active {
  background-color: #E9ECEF;
}

.time-value {
  font-size: 26rpx;
  color: #2C3E50;
  font-weight: 500;
  min-width: 100rpx;
  text-align: center;
}

/* 时间选择器 */
.time-picker {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #F8F9FA;
  border-radius: 8rpx;
  border: 1rpx solid #E9ECEF;
}

.picker-arrow {
  font-size: 20rpx;
  color: #BDC3C7;
}

/* 音量控制 */
.volume-control {
  display: flex;
  align-items: center;
  gap: 16rpx;
  min-width: 200rpx;
}

.volume-text {
  font-size: 22rpx;
  color: #7F8C8D;
  min-width: 60rpx;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}

/* 滑块样式 */
slider {
  flex: 1;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .setting-item {
    padding: 24rpx;
  }
  
  .setting-name {
    font-size: 26rpx;
  }
  
  .setting-desc {
    font-size: 20rpx;
  }
}

/* 深色模式适配 */
.container.dark {
  background-color: #1A1A1A;
}

.container.dark .header {
  background-color: #2C2C2C;
}

.container.dark .page-title {
  color: #FFFFFF;
}

.container.dark .page-subtitle {
  color: #CCCCCC;
}

.container.dark .setting-section {
  background-color: #2C2C2C;
}

.container.dark .section-title {
  color: #FFFFFF;
  border-bottom-color: #404040;
}

.container.dark .setting-item {
  border-bottom-color: #404040;
}

.container.dark .setting-item:active {
  background-color: #404040;
}

.container.dark .setting-name {
  color: #FFFFFF;
}

.container.dark .setting-desc {
  color: #CCCCCC;
}

.container.dark .time-btn {
  background-color: #404040;
  border-color: #555555;
  color: #CCCCCC;
}

.container.dark .time-value {
  color: #FFFFFF;
}

.container.dark .time-picker {
  background-color: #404040;
  border-color: #555555;
}

/* 动画效果 */
.setting-item {
  transition: all 0.3s ease;
}

.time-btn {
  transition: all 0.2s ease;
}

.time-btn:active {
  transform: scale(0.95);
}

/* 特殊状态 */
.setting-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.setting-item.highlight {
  background-color: #E3F2FD;
}

.container.dark .setting-item.highlight {
  background-color: #1565C0;
}

/* 分割线 */
.setting-divider {
  height: 1rpx;
  background-color: #E9ECEF;
  margin: 0 32rpx;
}

.container.dark .setting-divider {
  background-color: #404040;
}

/* 提示文本 */
.setting-tip {
  font-size: 20rpx;
  color: #95A5A6;
  padding: 16rpx 32rpx;
  line-height: 1.5;
}

.container.dark .setting-tip {
  color: #AAAAAA;
}

/* 危险操作样式 */
.setting-item.danger .setting-name {
  color: #E74C3C;
}

.container.dark .setting-item.danger .setting-name {
  color: #FF6B6B;
}
