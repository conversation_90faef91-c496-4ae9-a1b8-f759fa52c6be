{"pageInfo": {"pageName": "番茄钟", "pageId": "pomodoro", "pageType": "tabPage", "description": "番茄工作法专注计时页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "timer-container", "type": "view", "className": "timer-main-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "24rpx", "padding": "60rpx 40rpx", "marginBottom": "24rpx", "boxShadow": "0 8rpx 24rpx rgba(0,0,0,0.1)", "textAlign": "center", "minHeight": "600rpx", "display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center"}, "children": [{"id": "session-info", "type": "view", "condition": "{{currentTask}}", "style": {"marginBottom": "40rpx"}, "children": [{"id": "task-name", "type": "text", "content": "{{currentTask.title}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "task-subject", "type": "text", "content": "{{currentTask.subject}}", "condition": "{{currentTask.subject}}", "style": {"fontSize": "26rpx", "color": "#666666"}}]}, {"id": "timer-display", "type": "view", "style": {"position": "relative", "marginBottom": "60rpx"}, "children": [{"id": "timer-circle", "type": "view", "style": {"width": "320rpx", "height": "320rpx", "borderRadius": "50%", "border": "8rpx solid #F0F0F0", "position": "relative", "display": "flex", "alignItems": "center", "justifyContent": "center", "margin": "0 auto"}, "children": [{"id": "progress-circle", "type": "view", "style": {"position": "absolute", "top": "-8rpx", "left": "-8rpx", "width": "320rpx", "height": "320rpx", "borderRadius": "50%", "border": "8rpx solid transparent", "borderTopColor": "{{timerState === 'work' ? '#FF6B6B' : '#4ECDC4'}}", "transform": "rotate({{progressDegree}}deg)", "transition": "transform 1s linear"}}, {"id": "timer-text", "type": "view", "style": {"textAlign": "center"}, "children": [{"id": "time-display", "type": "text", "content": "{{displayTime}}", "style": {"fontSize": "64rpx", "fontWeight": "700", "color": "#333333", "fontFamily": "monospace", "display": "block", "marginBottom": "8rpx"}}, {"id": "session-type", "type": "text", "content": "{{sessionTypeText}}", "style": {"fontSize": "28rpx", "color": "#666666", "fontWeight": "500"}}]}]}]}, {"id": "timer-controls", "type": "view", "style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "gap": "32rpx"}, "children": [{"id": "start-pause-btn", "type": "button", "content": "{{isRunning ? '暂停' : '开始'}}", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#FFFFFF", "backgroundColor": "{{isRunning ? '#FA8C16' : '#52C41A'}}", "borderRadius": "50%", "width": "120rpx", "height": "120rpx", "border": "none", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.2)"}, "events": {"tap": "toggleTimer"}}, {"id": "stop-btn", "type": "button", "content": "停止", "condition": "{{isRunning || isPaused}}", "style": {"fontSize": "28rpx", "color": "#666666", "backgroundColor": "#F5F5F5", "borderRadius": "50%", "width": "80rpx", "height": "80rpx", "border": "none"}, "events": {"tap": "stopTimer"}}]}]}, {"id": "session-settings", "type": "view", "condition": "{{!isRunning && !isPaused}}", "className": "settings-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "settings-title", "type": "text", "content": "番茄钟设置", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "duration-setting", "type": "view", "style": {"marginBottom": "24rpx"}, "children": [{"id": "duration-label", "type": "text", "content": "专注时长", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "duration-options", "type": "view", "style": {"display": "flex", "gap": "12rpx", "flexWrap": "wrap"}, "children": [{"id": "duration-option", "type": "button", "forEach": "{{durationOptions}}", "forItem": "option", "forIndex": "index", "content": "{{option.label}}", "style": {"fontSize": "26rpx", "padding": "12rpx 20rpx", "borderRadius": "8rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{selectedDuration === option.value ? '#1890FF' : '#FFFFFF'}}", "color": "{{selectedDuration === option.value ? '#FFFFFF' : '#333333'}}"}, "events": {"tap": "selectDuration"}}]}]}, {"id": "task-selection", "type": "view", "style": {"marginBottom": "24rpx"}, "children": [{"id": "task-label", "type": "text", "content": "选择任务（可选）", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "task-selector", "type": "picker", "range": "{{availableTasks}}", "rangeKey": "title", "value": "{{selectedTaskIndex}}", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "border": "1rpx solid #E9ECEF"}, "events": {"change": "selectTask"}, "children": [{"type": "text", "content": "{{selectedTask ? selectedTask.title : '选择任务'}}", "style": {"fontSize": "28rpx", "color": "{{selectedTask ? '#333333' : '#999999'}}"}}]}]}, {"id": "background-sound", "type": "view", "children": [{"id": "sound-label", "type": "text", "content": "背景音效", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "sound-options", "type": "view", "style": {"display": "flex", "gap": "12rpx", "flexWrap": "wrap"}, "children": [{"id": "sound-option", "type": "button", "forEach": "{{soundOptions}}", "forItem": "sound", "forIndex": "index", "content": "{{sound.name}}", "style": {"fontSize": "26rpx", "padding": "12rpx 20rpx", "borderRadius": "8rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{selectedSound === sound.id ? '#1890FF' : '#FFFFFF'}}", "color": "{{selectedSound === sound.id ? '#FFFFFF' : '#333333'}}"}, "events": {"tap": "selectSound"}}]}]}]}, {"id": "today-stats", "type": "view", "className": "stats-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "stats-title", "type": "text", "content": "今日统计", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "stats-grid", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr 1fr", "gap": "20rpx"}, "children": [{"id": "completed-pomodoros", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#FFF2E8", "borderRadius": "12rpx", "padding": "20rpx"}, "children": [{"type": "text", "content": "{{todayStats.completedPomodoros}}", "style": {"fontSize": "36rpx", "fontWeight": "700", "color": "#FA8C16", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "完成番茄", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "focus-time", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#F6FFED", "borderRadius": "12rpx", "padding": "20rpx"}, "children": [{"type": "text", "content": "{{todayStats.focusTime}}", "style": {"fontSize": "36rpx", "fontWeight": "700", "color": "#52C41A", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "专注时长", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "break-time", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#E6F7FF", "borderRadius": "12rpx", "padding": "20rpx"}, "children": [{"type": "text", "content": "{{todayStats.breakTime}}", "style": {"fontSize": "36rpx", "fontWeight": "700", "color": "#1890FF", "display": "block", "marginBottom": "8rpx"}}, {"type": "text", "content": "休息时长", "style": {"fontSize": "24rpx", "color": "#666666"}}]}]}]}, {"id": "recent-sessions", "type": "view", "condition": "{{recentSessions.length > 0}}", "className": "recent-sessions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "recent-title", "type": "text", "content": "最近专注", "style": {"fontSize": "32rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "24rpx"}}, {"id": "sessions-list", "type": "view", "children": [{"id": "session-item", "type": "view", "forEach": "{{recentSessions}}", "forItem": "session", "forIndex": "index", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "padding": "16rpx 0", "borderBottom": "1rpx solid #F0F0F0"}, "children": [{"id": "session-info", "type": "view", "children": [{"id": "session-task", "type": "text", "content": "{{session.taskName || '自由专注'}}", "style": {"fontSize": "28rpx", "color": "#333333", "marginBottom": "4rpx"}}, {"id": "session-time", "type": "text", "content": "{{session.startTime}} - {{session.endTime}}", "style": {"fontSize": "24rpx", "color": "#666666"}}]}, {"id": "session-duration", "type": "view", "style": {"textAlign": "right"}, "children": [{"type": "text", "content": "{{session.duration}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#52C41A"}}]}]}]}]}], "data": {"isRunning": false, "isPaused": false, "timerState": "work", "displayTime": "25:00", "progressDegree": 0, "sessionTypeText": "专注时间", "selectedDuration": 25, "selectedTask": null, "selectedTaskIndex": 0, "selectedSound": "none", "currentTask": null, "durationOptions": [{"value": 15, "label": "15分钟"}, {"value": 25, "label": "25分钟"}, {"value": 45, "label": "45分钟"}, {"value": 60, "label": "60分钟"}], "availableTasks": [{"id": "task_001", "title": "数学高数第一章复习", "subject": "数学"}, {"id": "task_002", "title": "英语单词背诵", "subject": "英语"}, {"id": "task_003", "title": "政治马原理论学习", "subject": "政治"}], "soundOptions": [{"id": "none", "name": "无音效"}, {"id": "rain", "name": "雨声"}, {"id": "forest", "name": "森林"}, {"id": "cafe", "name": "咖啡厅"}, {"id": "ocean", "name": "海浪"}], "todayStats": {"completedPomodoros": 3, "focusTime": "2.5h", "breakTime": "30m"}, "recentSessions": [{"id": "session_001", "taskName": "数学高数第一章复习", "startTime": "14:30", "endTime": "15:15", "duration": "45分钟"}, {"id": "session_002", "taskName": "英语单词背诵", "startTime": "10:00", "endTime": "10:25", "duration": "25分钟"}, {"id": "session_003", "taskName": null, "startTime": "09:00", "endTime": "09:25", "duration": "25分钟"}]}, "methods": {"toggleTimer": {"type": "function", "description": "开始/暂停计时器", "implementation": "startOrPauseTimer"}, "stopTimer": {"type": "function", "description": "停止计时器", "implementation": "stopCurrentTimer"}, "selectDuration": {"type": "function", "description": "选择专注时长", "params": ["duration"], "implementation": "setTimerDuration"}, "selectTask": {"type": "function", "description": "选择关联任务", "params": ["taskIndex"], "implementation": "setCurrentTask"}, "selectSound": {"type": "function", "description": "选择背景音效", "params": ["soundId"], "implementation": "setBackgroundSound"}}, "lifecycle": {"onLoad": ["initTimer", "loadAvailableTasks", "loadTodayStats"], "onShow": ["refreshStats"], "onHide": ["pauseTimer<PERSON>f<PERSON><PERSON>ning"], "onUnload": ["saveTimerState"]}, "interactions": {"keepScreenOn": {"enabled": true, "condition": "{{isRunning}}"}, "vibration": {"enabled": true, "events": ["timerComplete", "breakComplete"]}, "notification": {"enabled": true, "events": ["timerComplete", "breakComplete"]}}}