{"pageInfo": {"pageName": "错误页面", "pageId": "errorPages", "pageType": "component", "description": "各种错误状态页面组件", "version": "1.0.0", "lastModified": "2025-06-27"}, "components": {"networkError": {"id": "network-error", "type": "view", "className": "error-page-container", "style": {"display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center", "minHeight": "100vh", "padding": "80rpx 60rpx", "backgroundColor": "#F5F5F5", "textAlign": "center"}, "children": [{"id": "error-illustration", "type": "image", "src": "/images/errors/network-error.png", "style": {"width": "300rpx", "height": "240rpx", "marginBottom": "40rpx"}}, {"id": "error-icon", "type": "text", "content": "📡", "style": {"fontSize": "80rpx", "marginBottom": "24rpx", "opacity": "0.6"}}, {"id": "error-title", "type": "text", "content": "网络连接异常", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "error-message", "type": "text", "content": "请检查网络连接后重试", "style": {"fontSize": "28rpx", "color": "#666666", "lineHeight": "1.4", "marginBottom": "60rpx"}}, {"id": "error-actions", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "16rpx", "width": "100%", "maxWidth": "400rpx"}, "children": [{"id": "retry-btn", "type": "button", "content": "重新加载", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "borderRadius": "12rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "retryConnection"}}, {"id": "offline-mode-btn", "type": "button", "content": "离线模式", "style": {"fontSize": "26rpx", "color": "#666666", "backgroundColor": "transparent", "border": "1rpx solid #D9D9D9", "borderRadius": "8rpx", "padding": "12rpx"}, "events": {"tap": "enterOfflineMode"}}]}]}, "dataError": {"id": "data-error", "type": "view", "className": "error-page-container", "style": {"display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center", "minHeight": "100vh", "padding": "80rpx 60rpx", "backgroundColor": "#F5F5F5", "textAlign": "center"}, "children": [{"id": "error-icon", "type": "text", "content": "💾", "style": {"fontSize": "80rpx", "marginBottom": "24rpx", "opacity": "0.6"}}, {"id": "error-title", "type": "text", "content": "数据加载失败", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "error-message", "type": "text", "content": "数据获取异常，请稍后重试", "style": {"fontSize": "28rpx", "color": "#666666", "lineHeight": "1.4", "marginBottom": "60rpx"}}, {"id": "error-actions", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "16rpx", "width": "100%", "maxWidth": "400rpx"}, "children": [{"id": "reload-btn", "type": "button", "content": "重新加载", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "borderRadius": "12rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "reloadData"}}, {"id": "back-btn", "type": "button", "content": "返回上页", "style": {"fontSize": "26rpx", "color": "#666666", "backgroundColor": "transparent", "border": "1rpx solid #D9D9D9", "borderRadius": "8rpx", "padding": "12rpx"}, "events": {"tap": "goBack"}}]}]}, "notFound": {"id": "not-found", "type": "view", "className": "error-page-container", "style": {"display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center", "minHeight": "100vh", "padding": "80rpx 60rpx", "backgroundColor": "#F5F5F5", "textAlign": "center"}, "children": [{"id": "error-icon", "type": "text", "content": "🔍", "style": {"fontSize": "80rpx", "marginBottom": "24rpx", "opacity": "0.6"}}, {"id": "error-title", "type": "text", "content": "页面不存在", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "error-message", "type": "text", "content": "抱歉，您访问的页面不存在或已被删除", "style": {"fontSize": "28rpx", "color": "#666666", "lineHeight": "1.4", "marginBottom": "60rpx"}}, {"id": "error-actions", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "16rpx", "width": "100%", "maxWidth": "400rpx"}, "children": [{"id": "home-btn", "type": "button", "content": "返回首页", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "borderRadius": "12rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "goHome"}}, {"id": "search-btn", "type": "button", "content": "搜索内容", "style": {"fontSize": "26rpx", "color": "#666666", "backgroundColor": "transparent", "border": "1rpx solid #D9D9D9", "borderRadius": "8rpx", "padding": "12rpx"}, "events": {"tap": "openSearch"}}]}]}, "permissionDenied": {"id": "permission-denied", "type": "view", "className": "error-page-container", "style": {"display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center", "minHeight": "100vh", "padding": "80rpx 60rpx", "backgroundColor": "#F5F5F5", "textAlign": "center"}, "children": [{"id": "error-icon", "type": "text", "content": "🔒", "style": {"fontSize": "80rpx", "marginBottom": "24rpx", "opacity": "0.6"}}, {"id": "error-title", "type": "text", "content": "权限不足", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "error-message", "type": "text", "content": "您没有访问此内容的权限", "style": {"fontSize": "28rpx", "color": "#666666", "lineHeight": "1.4", "marginBottom": "60rpx"}}, {"id": "error-actions", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "16rpx", "width": "100%", "maxWidth": "400rpx"}, "children": [{"id": "login-btn", "type": "button", "content": "重新登录", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "borderRadius": "12rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "reLogin"}}, {"id": "contact-btn", "type": "button", "content": "联系客服", "style": {"fontSize": "26rpx", "color": "#666666", "backgroundColor": "transparent", "border": "1rpx solid #D9D9D9", "borderRadius": "8rpx", "padding": "12rpx"}, "events": {"tap": "contactSupport"}}]}]}, "maintenanceMode": {"id": "maintenance-mode", "type": "view", "className": "error-page-container", "style": {"display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center", "minHeight": "100vh", "padding": "80rpx 60rpx", "backgroundColor": "#F5F5F5", "textAlign": "center"}, "children": [{"id": "error-icon", "type": "text", "content": "🔧", "style": {"fontSize": "80rpx", "marginBottom": "24rpx", "opacity": "0.6"}}, {"id": "error-title", "type": "text", "content": "系统维护中", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "16rpx"}}, {"id": "error-message", "type": "text", "content": "系统正在升级维护，预计{{maintenanceEndTime}}恢复", "style": {"fontSize": "28rpx", "color": "#666666", "lineHeight": "1.4", "marginBottom": "40rpx"}}, {"id": "maintenance-info", "type": "view", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "12rpx", "padding": "24rpx", "marginBottom": "40rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "maintenance-title", "type": "text", "content": "本次更新内容：", "style": {"fontSize": "26rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "maintenance-features", "type": "view", "style": {"textAlign": "left"}, "children": [{"type": "text", "content": "• 优化学习数据统计功能\n• 新增智能学习建议\n• 修复已知问题", "style": {"fontSize": "24rpx", "color": "#666666", "lineHeight": "1.6"}}]}]}, {"id": "error-actions", "type": "view", "style": {"display": "flex", "flexDirection": "column", "gap": "16rpx", "width": "100%", "maxWidth": "400rpx"}, "children": [{"id": "refresh-btn", "type": "button", "content": "刷新页面", "style": {"fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "#1890FF", "borderRadius": "12rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "refreshPage"}}, {"id": "notification-btn", "type": "button", "content": "开启恢复通知", "style": {"fontSize": "26rpx", "color": "#666666", "backgroundColor": "transparent", "border": "1rpx solid #D9D9D9", "borderRadius": "8rpx", "padding": "12rpx"}, "events": {"tap": "enableNotification"}}]}]}, "emptyState": {"id": "empty-state", "type": "view", "className": "empty-state-container", "style": {"display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center", "padding": "80rpx 60rpx", "textAlign": "center", "minHeight": "400rpx"}, "children": [{"id": "empty-icon", "type": "text", "content": "{{emptyConfig.icon || '📝'}}", "style": {"fontSize": "64rpx", "marginBottom": "24rpx", "opacity": "0.5"}}, {"id": "empty-title", "type": "text", "content": "{{emptyConfig.title || '暂无数据'}}", "style": {"fontSize": "32rpx", "fontWeight": "500", "color": "#666666", "marginBottom": "12rpx"}}, {"id": "empty-message", "type": "text", "content": "{{emptyConfig.message || '还没有相关内容'}}", "style": {"fontSize": "26rpx", "color": "#999999", "lineHeight": "1.4", "marginBottom": "40rpx"}}, {"id": "empty-action", "type": "button", "condition": "{{emptyConfig.actionText}}", "content": "{{emptyConfig.actionText}}", "style": {"fontSize": "26rpx", "color": "#1890FF", "backgroundColor": "transparent", "border": "1rpx solid #1890FF", "borderRadius": "8rpx", "padding": "12rpx 24rpx"}, "events": {"tap": "executeEmptyAction"}}]}}, "data": {"maintenanceEndTime": "15:00", "emptyConfig": {"icon": "📝", "title": "暂无数据", "message": "还没有相关内容", "actionText": "添加内容"}}, "methods": {"retryConnection": {"type": "function", "description": "重试网络连接", "implementation": "checkNetworkAndRetry"}, "enterOfflineMode": {"type": "function", "description": "进入离线模式", "implementation": "switchToOfflineMode"}, "reloadData": {"type": "function", "description": "重新加载数据", "implementation": "refreshPageData"}, "goBack": {"type": "navigate", "url": "back"}, "goHome": {"type": "navigate", "url": "/pages/home/<USER>"}, "openSearch": {"type": "navigate", "url": "/pages/search/index"}, "reLogin": {"type": "function", "description": "重新登录", "implementation": "redirectToLogin"}, "contactSupport": {"type": "function", "description": "联系客服", "implementation": "openSupportChannel"}, "refreshPage": {"type": "function", "description": "刷新页面", "implementation": "reloadCurrentPage"}, "enableNotification": {"type": "function", "description": "开启通知", "implementation": "requestNotificationPermission"}, "executeEmptyAction": {"type": "function", "description": "执行空状态操作", "implementation": "handleEmptyStateAction"}}}