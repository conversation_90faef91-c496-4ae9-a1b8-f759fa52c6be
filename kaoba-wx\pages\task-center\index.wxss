/* pages/task-center/index.wxss */

/* 头部区域 */
.header-section {
  margin-bottom: 24rpx;
}

.stats-overview {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.stats-item {
  text-align: center;
}

.stats-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1890FF;
  display: block;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 22rpx;
  color: #666666;
}

/* 筛选栏 */
.filter-bar {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 16rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 12rpx 20rpx;
  margin-right: 12rpx;
  border-radius: 20rpx;
  background-color: #F5F5F5;
  color: #666666;
  font-size: 24rpx;
  position: relative;
  transition: all 0.3s ease;
}

.filter-item.active {
  background-color: #1890FF;
  color: #FFFFFF;
}

.filter-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #FF4D4F;
  color: #FFFFFF;
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
  min-width: 16rpx;
  height: 16rpx;
  line-height: 16rpx;
  text-align: center;
}

.filter-item.active .filter-count {
  background-color: rgba(255,255,255,0.3);
}

.filter-actions {
  display: flex;
  gap: 16rpx;
}

.filter-icon,
.search-icon {
  font-size: 32rpx;
  color: #666666;
  padding: 8rpx;
}

/* 任务区域 */
.tasks-section {
  margin-bottom: 24rpx;
}

.task-group {
  margin-bottom: 24rpx;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8rpx 16rpx;
}

.group-date {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.group-count {
  font-size: 22rpx;
  color: #999999;
}

.task-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.task-item {
  position: relative;
  border-bottom: 1rpx solid #F0F0F0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-status-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
}

.status-pending {
  background-color: #FA8C16;
}

.status-completed {
  background-color: #52C41A;
}

.status-overdue {
  background-color: #FF4D4F;
}

.task-content {
  padding: 24rpx;
  padding-left: 30rpx;
}

.task-header {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.task-checkbox {
  flex-shrink: 0;
  margin-top: 4rpx;
}

.checkbox-icon {
  font-size: 32rpx;
}

.task-info {
  flex: 1;
}

.task-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  font-weight: 500;
  line-height: 1.4;
}

.task-title.completed {
  text-decoration: line-through;
  color: #999999;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.task-subject {
  font-size: 20rpx;
  color: #1890FF;
  background-color: #E6F7FF;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.task-exam {
  font-size: 20rpx;
  color: #52C41A;
  background-color: #F6FFED;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.task-priority {
  font-size: 18rpx;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

.priority-high {
  background-color: #FFF1F0;
  color: #FF4D4F;
}

.priority-medium {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.priority-low {
  background-color: #F6FFED;
  color: #52C41A;
}

.task-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.task-status-text {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.task-status-text.status-pending {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.task-status-text.status-completed {
  background-color: #F6FFED;
  color: #52C41A;
}

.more-icon {
  font-size: 24rpx;
  color: #999999;
  padding: 8rpx;
}

/* 任务详情 */
.task-details {
  margin-top: 16rpx;
}

.task-time-info {
  display: flex;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-icon {
  font-size: 20rpx;
}

.time-text {
  font-size: 22rpx;
  color: #666666;
}

/* 进度条 */
.task-progress {
  margin-bottom: 16rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.progress-label {
  font-size: 22rpx;
  color: #666666;
}

.progress-percentage {
  font-size: 22rpx;
  font-weight: 600;
  color: #333333;
}

.progress-bar {
  background-color: #F0F0F0;
  border-radius: 6rpx;
  height: 6rpx;
  overflow: hidden;
}

.progress-fill {
  background-color: #52C41A;
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 子任务 */
.subtasks {
  margin-bottom: 16rpx;
}

.subtasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.subtasks-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

.subtasks-toggle {
  font-size: 22rpx;
  color: #1890FF;
}

.subtasks-list {
  background-color: #F8F9FA;
  border-radius: 8rpx;
  padding: 12rpx;
}

.subtask-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 8rpx 0;
}

.subtask-checkbox {
  flex-shrink: 0;
}

.subtask-title {
  font-size: 24rpx;
  color: #333333;
  flex: 1;
}

.subtask-title.completed {
  text-decoration: line-through;
  color: #999999;
}

/* 快捷操作 */
.task-quick-actions {
  display: flex;
  gap: 12rpx;
}

.quick-action-btn {
  flex: 1;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 6rpx;
  padding: 8rpx 12rpx;
  font-size: 20rpx;
  color: #666666;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.quick-action-btn:active {
  background-color: #E9ECEF;
}

.action-icon {
  font-size: 16rpx;
}

.action-text {
  font-size: 18rpx;
}

/* 已完成任务信息 */
.completed-task-info {
  margin-top: 16rpx;
  padding: 12rpx;
  background-color: #F6FFED;
  border-radius: 8rpx;
}

.completion-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.completion-time {
  font-size: 22rpx;
  color: #52C41A;
}

.completion-duration {
  font-size: 22rpx;
  color: #666666;
}

/* 空状态 */
.empty-state {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
}

.empty-message {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

/* 添加任务按钮 */
.add-task-section {
  margin-bottom: 24rpx;
}

.add-task-btn {
  background-color: #FFFFFF;
  border: 2rpx dashed #1890FF;
  border-radius: 16rpx;
  padding: 32rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  color: #1890FF;
}

.add-task-btn:active {
  background-color: #F0F9FF;
}

.add-icon {
  font-size: 48rpx;
  font-weight: 300;
}

.add-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 操作菜单 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.action-sheet {
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}

.action-sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.action-sheet-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.action-sheet-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.action-sheet-body {
  padding: 16rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  gap: 16rpx;
}

.action-item:active {
  background-color: #F8F9FA;
}

.action-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
}

.action-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.action-arrow {
  font-size: 24rpx;
  color: #999999;
}

/* 筛选菜单 */
.filter-menu-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.filter-menu {
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}

.filter-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.filter-menu-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.filter-menu-close {
  font-size: 36rpx;
  color: #999999;
  padding: 8rpx;
}

.filter-menu-body {
  padding: 24rpx 0;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 32rpx;
  padding: 0 32rpx;
}

.filter-section-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.filter-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #333333;
}

.filter-option.active {
  color: #1890FF;
}

.check-icon {
  font-size: 24rpx;
  color: #1890FF;
}

.filter-menu-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #F0F0F0;
}

.filter-reset-btn {
  flex: 1;
  background-color: #F5F5F5;
  color: #666666;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
}

.filter-reset-btn:active {
  background-color: #E6E6E6;
}

.filter-apply-btn {
  flex: 1;
  background-color: #1890FF;
  color: #FFFFFF;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
}

.filter-apply-btn:active {
  background-color: #096DD9;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
