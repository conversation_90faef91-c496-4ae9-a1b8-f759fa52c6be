// app.js
const LoginApi = require('./utils/loginApi')

App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        // 环境 ID
        env: 'cloud1-8gvo8etxe4c004b3',
        traceUser: true,
      })
      console.log('云开发初始化成功')

      // 延迟初始化数据，确保云开发完全初始化
      setTimeout(async () => {
        await this.initCloudDatabase()
      }, 1000)
    }

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 延迟执行其他初始化，确保应用完全启动
    setTimeout(() => {
      // 检查登录状态
      this.checkLoginStatus()

      // 自动登录
      this.autoLogin()

      // 检查是否是首次启动
      this.checkFirstLaunch()
    }, 500)
  },

  // 初始化云数据库
  async initCloudDatabase() {
    try {
      // 确保云开发已经初始化
      if (!wx.cloud) {
        console.error('云开发未初始化，使用本地存储')
        this.initLocalData()
        return
      }

      console.log('开始初始化云数据库...')

      // 调用数据库初始化云函数
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'init' }
      })

      if (result.result && result.result.success) {
        console.log('云数据库初始化成功:', result.result.message)
        this.globalData.dbInitialized = true
        this.globalData.apiStatus = {
          cloudAvailable: true,
          currentApi: 'cloud',
          message: '云开发可用'
        }
      } else {
        console.error('云数据库初始化失败:', result.result?.error)
        this.initLocalData()
      }
    } catch (error) {
      console.error('云数据库初始化异常:', error)
      this.initLocalData()
    }
  },

  // 初始化本地数据
  initLocalData() {
    try {
      console.log('初始化本地存储数据...')
      const LocalStorageApi = require('./utils/localStorageApi')
      const result = LocalStorageApi.initSampleData()

      if (result.success) {
        console.log('本地数据初始化成功:', result.message)
        this.globalData.dbInitialized = true
        this.globalData.apiStatus = {
          cloudAvailable: false,
          currentApi: 'local',
          message: '使用本地存储'
        }
      } else {
        console.error('本地数据初始化失败:', result.error)
      }
    } catch (error) {
      console.error('本地数据初始化异常:', error)
      // 即使失败也标记为已初始化，避免重复尝试
      this.globalData.dbInitialized = true
      this.globalData.apiStatus = {
        cloudAvailable: false,
        currentApi: 'local',
        message: '本地存储初始化失败'
      }
    }
  },

  onShow() {
    // 应用被用户重新打开时触发
    console.log('应用显示')
  },

  onHide() {
    // 应用被用户隐藏时触发
    console.log('应用隐藏')
  },

  onError(error) {
    // 应用发生错误时触发
    console.error('应用错误:', error)
  },

  // 检查是否首次启动
  checkFirstLaunch() {
    const hasLaunched = wx.getStorageSync('hasLaunched')
    if (!hasLaunched) {
      // 首次启动，设置标记
      wx.setStorageSync('hasLaunched', true)
      this.globalData.isFirstLaunch = true
    } else {
      this.globalData.isFirstLaunch = false
    }
  },



  // 检查登录状态
  checkLoginStatus() {
    const loginStatus = LoginApi.checkLoginStatus()
    if (loginStatus.isLoggedIn) {
      console.log('用户已登录:', loginStatus.userInfo.nickName)
      this.globalData.userInfo = loginStatus.userInfo
      this.globalData.openid = loginStatus.openid
    } else {
      console.log('用户未登录')
    }
    return loginStatus
  },

  // 自动登录
  async autoLogin() {
    try {
      const loginStatus = this.checkLoginStatus()
      if (!loginStatus.isLoggedIn) {
        console.log('开始自动登录...')
        const result = await LoginApi.login()
        if (result.success) {
          console.log('自动登录成功')
          this.globalData.userInfo = result.data.user
          this.globalData.openid = result.data.openid

          // 如果是首次登录，可以显示欢迎信息
          if (result.isFirstLogin) {
            this.globalData.isFirstLaunch = true
          }
        } else {
          console.error('自动登录失败:', result.error)
        }
      }
    } catch (error) {
      console.error('自动登录异常:', error)
    }
  },

  // 手动登录（带用户信息）
  async loginWithUserInfo(userInfo) {
    try {
      const result = await LoginApi.login(userInfo)
      if (result.success) {
        this.globalData.userInfo = result.data.user
        this.globalData.openid = result.data.openid

        console.log('手动登录成功:', result.data.user.nickName)
        return result
      } else {
        console.error('手动登录失败:', result.error)
        return result
      }
    } catch (error) {
      console.error('手动登录异常:', error)
      return { success: false, error: error.message }
    }
  },

  // 退出登录
  logout() {
    const result = LoginApi.logout()
    if (result.success) {
      this.globalData.userInfo = null
      this.globalData.openid = null
      console.log('退出登录成功')
    }
    return result
  },

  // 全局数据
  globalData: {
    userInfo: null,
    openid: null,
    isFirstLaunch: false,
    version: '1.0.0',
    dbInitialized: false,
    cloudEnv: 'cloud1-8gvo8etxe4c004b3',
    apiStatus: null
  }
})
