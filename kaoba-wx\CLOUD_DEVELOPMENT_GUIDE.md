# 微信云开发配置指南

## 概述

本项目已集成微信云开发，提供完整的后端服务支持，包括云数据库、云函数、云存储等功能。

## 环境配置

### 云开发环境ID
- 环境ID: `cloud1-8gvo8etxe4c004b3`
- 已在 `app.js` 中配置

### 项目结构
```
kaoba-wx/
├── cloudfunctions/          # 云函数目录
│   ├── login/              # 用户登录云函数
│   ├── taskManager/        # 任务管理云函数
│   ├── examManager/        # 考试管理云函数
│   └── studyManager/       # 学习管理云函数
├── utils/
│   ├── cloudApi.js         # 云开发API封装
│   └── dbInit.js           # 数据库初始化工具
└── pages/                  # 页面文件
```

## 云函数说明

### 1. login 云函数
- **功能**: 用户登录，获取openid
- **调用**: `CloudApi.login()`

### 2. taskManager 云函数
- **功能**: 任务管理（增删改查、统计）
- **主要方法**:
  - `getTasks()` - 获取任务列表
  - `addTask()` - 添加任务
  - `updateTask()` - 更新任务
  - `deleteTask()` - 删除任务
  - `completeTask()` - 完成任务
  - `getTaskStats()` - 获取任务统计

### 3. examManager 云函数
- **功能**: 考试管理（增删改查、提醒）
- **主要方法**:
  - `getExams()` - 获取考试列表
  - `addExam()` - 添加考试
  - `updateExam()` - 更新考试
  - `deleteExam()` - 删除考试
  - `getUpcomingExams()` - 获取即将到来的考试
  - `getExamStats()` - 获取考试统计

### 4. studyManager 云函数
- **功能**: 学习会话管理（番茄钟、学习统计）
- **主要方法**:
  - `startStudySession()` - 开始学习会话
  - `endStudySession()` - 结束学习会话
  - `getStudyStats()` - 获取学习统计
  - `addPomodoroSession()` - 添加番茄钟会话
  - `getPomodoroStats()` - 获取番茄钟统计

## 数据库集合

### 独立数据集合
每个实体使用独立的数据集合，提供更好的数据组织和查询性能：

- **tasks** - 任务数据集合
- **exams** - 考试数据集合
- **study_sessions** - 学习会话数据集合
- **pomodoro_sessions** - 番茄钟数据集合

#### tasks 集合
```javascript
{
  _id: String,           // 自动生成
  _openid: String,       // 用户openid（自动注入）
  title: String,         // 任务标题
  subject: String,       // 科目
  priority: String,      // 优先级：high/medium/low
  estimatedTime: Number, // 预估时间（分钟）
  dueDate: Date,         // 截止日期
  description: String,   // 任务描述
  completed: Boolean,    // 是否完成
  createTime: Date,      // 创建时间
  updateTime: Date,      // 更新时间
  completedTime: Date    // 完成时间
}
```

#### exams 集合
```javascript
{
  _id: String,           // 自动生成
  _openid: String,       // 用户openid（自动注入）
  title: String,         // 考试标题
  subject: String,       // 科目
  examDate: Date,        // 考试日期
  examTime: String,      // 考试时间
  location: String,      // 考试地点
  description: String,   // 考试描述
  reminderDays: Number,  // 提醒天数
  createTime: Date,      // 创建时间
  updateTime: Date       // 更新时间
}
```

#### study_sessions 集合
```javascript
{
  _id: String,           // 自动生成
  _openid: String,       // 用户openid（自动注入）
  subject: String,       // 学习科目
  taskId: String,        // 关联任务ID（可选）
  startTime: Date,       // 开始时间
  endTime: Date,         // 结束时间
  totalDuration: Number, // 总时长（分钟）
  pausedDuration: Number,// 暂停时长（分钟）
  status: String,        // 状态：active/paused/completed
  notes: String,         // 学习笔记
  createTime: Date       // 创建时间
}
```

#### pomodoro_sessions 集合
```javascript
{
  _id: String,           // 自动生成
  _openid: String,       // 用户openid（自动注入）
  subject: String,       // 学习科目
  taskId: String,        // 关联任务ID（可选）
  duration: Number,      // 番茄钟时长（分钟）
  completed: Boolean,    // 是否完成
  startTime: Date,       // 开始时间
  endTime: Date,         // 结束时间
  createTime: Date       // 创建时间
}
```

## 使用方法

### 1. 调用云函数
```javascript
// 引入API工具类
const CloudApi = require('../../utils/cloudApi')

// 获取任务列表
const result = await CloudApi.getTasks()
if (result.success) {
  console.log('任务列表:', result.data)
}

// 添加任务
const taskData = {
  title: '数学复习',
  subject: '数学',
  priority: 'high',
  estimatedTime: 120,
  dueDate: new Date(),
  description: '复习高等数学第一章'
}
const addResult = await CloudApi.addTask(taskData)
```

### 2. 数据库初始化
应用启动时会自动检查并初始化数据库，创建示例数据。如需手动初始化：

```javascript
const DatabaseInit = require('./utils/dbInit')
const result = await DatabaseInit.checkAndInit()
```

## 部署说明

### 1. 云函数部署
在微信开发者工具中，按以下顺序部署云函数：

**必须首先部署的云函数：**
```
cloudfunctions/initDatabase/    # 数据库初始化云函数（必须先部署）
```

**其他云函数：**
```
cloudfunctions/login/           # 用户登录云函数
cloudfunctions/taskManager/     # 任务管理云函数
cloudfunctions/examManager/     # 考试管理云函数
cloudfunctions/studyManager/    # 学习管理云函数
```

**部署步骤：**
1. 在微信开发者工具中打开项目
2. 右键点击 `cloudfunctions/initDatabase` 文件夹
3. 选择"上传并部署：云端安装依赖"
4. 等待部署完成后，再部署其他云函数

### 2. 数据库自动初始化
**无需手动创建数据库集合！** 应用会自动完成以下操作：

1. **自动创建集合**: 应用启动时会自动创建 `kaoba` 集合
2. **自动写入示例数据**: 包括示例任务、考试、学习记录等
3. **智能检测**: 如果数据已存在，会跳过初始化过程

**初始化过程：**
- 应用启动 → 调用 `initDatabase` 云函数 → 检查数据是否存在 → 创建示例数据（如需要）

**手动管理（可选）：**
如果需要手动管理数据库，可以在云开发控制台中：
1. 进入"数据库"页面
2. 查看 `kaoba` 集合
3. 手动添加、编辑或删除数据

### 3. 权限配置
数据库权限已设置为"仅创建者可读写"，确保数据安全。

## 数据库管理功能

### 自动初始化
应用提供了完整的数据库自动初始化功能：

```javascript
// 在 app.js 中自动调用
await this.initCloudDatabase()
```

### 手动管理工具
可以使用 `DatabaseManager` 工具类进行数据库管理：

```javascript
const DatabaseManager = require('./utils/databaseManager')

// 检查数据库状态
const status = await DatabaseManager.checkStatus()

// 获取统计信息
const stats = await DatabaseManager.getStats()

// 重置数据库
const reset = await DatabaseManager.resetDatabase()

// 备份数据到本地
const backup = await DatabaseManager.backupToLocal()

// 从本地恢复数据
const restore = await DatabaseManager.restoreFromLocal()
```

### 数据库操作
- **自动创建**: 首次启动时自动创建集合和示例数据
- **智能检测**: 检测现有数据，避免重复初始化
- **数据备份**: 支持备份到本地存储
- **数据恢复**: 支持从本地备份恢复
- **数据清理**: 支持清理过期数据

## 注意事项

1. **环境ID**: 确保使用正确的云开发环境ID
2. **基础库版本**: 需要微信基础库版本 2.2.3 或以上
3. **云函数部署**: 必须先部署 `initDatabase` 云函数
4. **网络连接**: 首次启动需要网络连接以初始化云数据库
3. **网络权限**: 确保小程序有网络访问权限
4. **数据安全**: 所有数据操作都会自动注入用户openid，确保数据隔离

## 故障排除

### 常见问题

1. **云函数调用失败**
   - 检查云函数是否正确部署
   - 检查网络连接
   - 查看控制台错误信息

2. **数据库操作失败**
   - 检查数据库权限设置
   - 确认集合是否存在
   - 检查数据格式是否正确

3. **初始化失败**
   - 检查环境ID是否正确
   - 确认基础库版本
   - 查看控制台日志

### 调试方法

1. 在微信开发者工具中查看控制台日志
2. 在云开发控制台查看云函数日志
3. 使用真机调试测试网络环境
