// pages/notification-center/index.js
Page({
  data: {
    totalCount: 0,
    unreadCount: 0,
    todayCount: 0,

    currentFilter: 'all',
    notificationFilters: [
      { id: 'all', label: '全部', count: 0 },
      { id: 'unread', label: '未读', count: 0 },
      { id: 'exam', label: '考试提醒', count: 0 },
      { id: 'task', label: '任务提醒', count: 0 },
      { id: 'system', label: '系统通知', count: 0 }
    ],

    allNotifications: [],
    filteredNotifications: [],
    groupedNotifications: [],

    showActionSheet: false,
    selectedNotification: {},

    showSettingsModal: false,
    notificationsEnabled: true,

    notificationSettings: [
      {
        type: 'exam_reminder',
        name: '考试提醒',
        description: '考试前的倒计时提醒',
        enabled: true
      },
      {
        type: 'task_deadline',
        name: '任务截止',
        description: '任务即将到期的提醒',
        enabled: true
      },
      {
        type: 'study_reminder',
        name: '学习提醒',
        description: '定时学习提醒',
        enabled: true
      },
      {
        type: 'achievement',
        name: '成就通知',
        description: '解锁新成就时的通知',
        enabled: true
      },
      {
        type: 'system_update',
        name: '系统更新',
        description: '应用更新和功能通知',
        enabled: false
      }
    ],

    quietHours: {
      start: '22:00',
      end: '08:00'
    }
  },

  onLoad() {
    this.loadNotifications()
    this.loadNotificationSettings()
  },

  onShow() {
    this.refreshNotifications()
  },

  // 加载通知数据
  loadNotifications() {
    try {
      // 从本地存储加载通知
      const notifications = wx.getStorageSync('notifications') || []

      // 如果没有通知，生成模拟数据
      if (notifications.length === 0) {
        this.generateMockNotifications()
      } else {
        this.setData({ allNotifications: notifications })
        this.updateNotificationStats()
        this.filterNotifications()
      }

    } catch (error) {
      console.error('加载通知失败:', error)
      this.generateMockNotifications()
    }
  },

  // 生成模拟通知数据
  generateMockNotifications() {
    const mockNotifications = [
      {
        id: 'notif_001',
        type: 'exam_reminder',
        title: '考试提醒',
        message: '距离"2025年考研"还有156天，记得按时复习哦！',
        icon: '📅',
        iconBg: '#E6F7FF',
        time: '2小时前',
        date: '今天',
        read: false,
        actions: [
          { id: 'view_exam', label: '查看考试', type: 'primary' },
          { id: 'dismiss', label: '忽略', type: 'secondary' }
        ]
      },
      {
        id: 'notif_002',
        type: 'task_deadline',
        title: '任务截止提醒',
        message: '任务"数学高数第一章复习"将在今天18:00截止',
        icon: '⏰',
        iconBg: '#FFF7E6',
        time: '4小时前',
        date: '今天',
        read: false,
        actions: [
          { id: 'view_task', label: '查看任务', type: 'primary' }
        ]
      },
      {
        id: 'notif_003',
        type: 'achievement',
        title: '成就解锁',
        message: '恭喜！你解锁了"坚持达人"成就，连续学习15天！',
        icon: '🏆',
        iconBg: '#F6FFED',
        time: '昨天 20:30',
        date: '昨天',
        read: true,
        actions: [
          { id: 'view_achievement', label: '查看成就', type: 'primary' }
        ]
      },
      {
        id: 'notif_004',
        type: 'study_reminder',
        title: '学习提醒',
        message: '该开始今天的学习计划了，加油！',
        icon: '📚',
        iconBg: '#F9F0FF',
        time: '昨天 09:00',
        date: '昨天',
        read: true
      },
      {
        id: 'notif_005',
        type: 'system_update',
        title: '功能更新',
        message: '新增成就系统功能，快来体验吧！',
        icon: '🎉',
        iconBg: '#FFF1F0',
        time: '3天前',
        date: '3天前',
        read: true
      }
    ]

    // 保存到本地存储
    try {
      wx.setStorageSync('notifications', mockNotifications)
    } catch (error) {
      console.error('保存通知失败:', error)
    }

    this.setData({ allNotifications: mockNotifications })
    this.updateNotificationStats()
    this.filterNotifications()
  },

  // 更新通知统计
  updateNotificationStats() {
    const notifications = this.data.allNotifications
    const today = new Date().toDateString()

    const totalCount = notifications.length
    const unreadCount = notifications.filter(n => !n.read).length
    const todayCount = notifications.filter(n => {
      // 简化实现，实际应该比较日期
      return n.date === '今天'
    }).length

    // 更新筛选器计数
    const filters = this.data.notificationFilters.map(filter => {
      let count = 0
      switch (filter.id) {
        case 'all':
          count = totalCount
          break
        case 'unread':
          count = unreadCount
          break
        case 'exam':
          count = notifications.filter(n => n.type === 'exam_reminder').length
          break
        case 'task':
          count = notifications.filter(n => n.type === 'task_deadline').length
          break
        case 'system':
          count = notifications.filter(n => n.type === 'system_update').length
          break
      }
      return { ...filter, count }
    })

    this.setData({
      totalCount,
      unreadCount,
      todayCount,
      notificationFilters: filters
    })
  },

  // 筛选通知
  filterNotifications() {
    const { allNotifications, currentFilter } = this.data
    let filtered = []

    switch (currentFilter) {
      case 'all':
        filtered = allNotifications
        break
      case 'unread':
        filtered = allNotifications.filter(n => !n.read)
        break
      case 'exam':
        filtered = allNotifications.filter(n => n.type === 'exam_reminder')
        break
      case 'task':
        filtered = allNotifications.filter(n => n.type === 'task_deadline')
        break
      case 'system':
        filtered = allNotifications.filter(n => n.type === 'system_update')
        break
      default:
        filtered = allNotifications
    }

    // 按日期分组
    const grouped = this.groupNotificationsByDate(filtered)

    this.setData({
      filteredNotifications: filtered,
      groupedNotifications: grouped
    })
  },

  // 按日期分组通知
  groupNotificationsByDate(notifications) {
    const groups = {}

    notifications.forEach(notification => {
      const date = notification.date
      if (!groups[date]) {
        groups[date] = {
          date,
          notifications: []
        }
      }
      groups[date].notifications.push(notification)
    })

    // 转换为数组并排序
    return Object.values(groups).sort((a, b) => {
      // 简化排序，实际应该按日期排序
      const order = { '今天': 0, '昨天': 1, '3天前': 2 }
      return (order[a.date] || 999) - (order[b.date] || 999)
    })
  },

  // 切换筛选器
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterNotifications()
  },

  // 全部标记为已读
  markAllAsRead() {
    wx.showModal({
      title: '确认操作',
      content: '确定要将所有通知标记为已读吗？',
      success: (res) => {
        if (res.confirm) {
          const notifications = this.data.allNotifications.map(n => ({
            ...n,
            read: true
          }))

          this.updateNotifications(notifications)

          wx.showToast({
            title: '已全部标记为已读',
            icon: 'success'
          })
        }
      }
    })
  },

  // 打开通知
  openNotification(e) {
    const notification = e.currentTarget.dataset.notification

    // 标记为已读
    if (!notification.read) {
      this.markNotificationAsRead(notification.id)
    }

    // 根据通知类型跳转
    this.handleNotificationAction(notification)
  },

  // 处理通知操作
  handleNotificationAction(notification) {
    switch (notification.type) {
      case 'exam_reminder':
        wx.navigateTo({
          url: '/pages/exam-center/index'
        })
        break
      case 'task_deadline':
        wx.navigateTo({
          url: '/pages/task-center/index'
        })
        break
      case 'achievement':
        wx.navigateTo({
          url: '/pages/achievement-system/index'
        })
        break
      case 'study_reminder':
        wx.switchTab({
          url: '/pages/pomodoro/index'
        })
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  // 显示通知操作菜单
  showNotificationActions(e) {
    const notification = e.currentTarget.dataset.notification
    this.setData({
      selectedNotification: notification,
      showActionSheet: true
    })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({ showActionSheet: false })
  },

  // 标记为已读
  markAsRead() {
    const notification = this.data.selectedNotification
    this.markNotificationAsRead(notification.id)
    this.hideActionSheet()
  },

  // 标记为未读
  markAsUnread() {
    const notification = this.data.selectedNotification
    this.markNotificationAsUnread(notification.id)
    this.hideActionSheet()
  },

  // 删除通知
  deleteNotification() {
    const notification = this.data.selectedNotification

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条通知吗？',
      success: (res) => {
        if (res.confirm) {
          const notifications = this.data.allNotifications.filter(n => n.id !== notification.id)
          this.updateNotifications(notifications)
          this.hideActionSheet()

          wx.showToast({
            title: '已删除',
            icon: 'success'
          })
        }
      }
    })
  },

  // 屏蔽通知类型
  muteNotificationType() {
    const notification = this.data.selectedNotification

    wx.showModal({
      title: '屏蔽通知',
      content: `确定要屏蔽"${this.getNotificationTypeName(notification.type)}"类型的通知吗？`,
      success: (res) => {
        if (res.confirm) {
          // 更新通知设置
          const settings = this.data.notificationSettings.map(setting => {
            if (setting.type === notification.type) {
              return { ...setting, enabled: false }
            }
            return setting
          })

          this.setData({ notificationSettings: settings })
          this.saveNotificationSettings()
          this.hideActionSheet()

          wx.showToast({
            title: '已屏蔽此类通知',
            icon: 'success'
          })
        }
      }
    })
  },

  // 执行通知操作
  executeNotificationAction(e) {
    const { notification, action } = e.currentTarget.dataset

    switch (action.id) {
      case 'view_exam':
        wx.navigateTo({
          url: '/pages/exam-center/index'
        })
        break
      case 'view_task':
        wx.navigateTo({
          url: '/pages/task-center/index'
        })
        break
      case 'view_achievement':
        wx.navigateTo({
          url: '/pages/achievement-system/index'
        })
        break
      case 'dismiss':
        this.markNotificationAsRead(notification.id)
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  // 标记通知为已读
  markNotificationAsRead(notificationId) {
    const notifications = this.data.allNotifications.map(n => {
      if (n.id === notificationId) {
        return { ...n, read: true }
      }
      return n
    })

    this.updateNotifications(notifications)
  },

  // 标记通知为未读
  markNotificationAsUnread(notificationId) {
    const notifications = this.data.allNotifications.map(n => {
      if (n.id === notificationId) {
        return { ...n, read: false }
      }
      return n
    })

    this.updateNotifications(notifications)
  },

  // 更新通知数据
  updateNotifications(notifications) {
    try {
      wx.setStorageSync('notifications', notifications)
      this.setData({ allNotifications: notifications })
      this.updateNotificationStats()
      this.filterNotifications()
    } catch (error) {
      console.error('更新通知失败:', error)
    }
  },

  // 获取通知类型名称
  getNotificationTypeName(type) {
    const typeMap = {
      'exam_reminder': '考试提醒',
      'task_deadline': '任务截止',
      'study_reminder': '学习提醒',
      'achievement': '成就通知',
      'system_update': '系统更新'
    }
    return typeMap[type] || '未知类型'
  },

  // 打开通知设置
  openNotificationSettings() {
    this.setData({ showSettingsModal: true })
  },

  // 隐藏设置弹窗
  hideSettingsModal() {
    this.setData({ showSettingsModal: false })
  },

  // 切换通知类型
  toggleNotificationType(e) {
    const type = e.currentTarget.dataset.type
    const enabled = e.detail.value

    const settings = this.data.notificationSettings.map(setting => {
      if (setting.type === type) {
        return { ...setting, enabled }
      }
      return setting
    })

    this.setData({ notificationSettings: settings })
    this.saveNotificationSettings()
  },

  // 设置免打扰开始时间
  setQuietStart(e) {
    this.setData({
      'quietHours.start': e.detail.value
    })
    this.saveNotificationSettings()
  },

  // 设置免打扰结束时间
  setQuietEnd(e) {
    this.setData({
      'quietHours.end': e.detail.value
    })
    this.saveNotificationSettings()
  },

  // 加载通知设置
  loadNotificationSettings() {
    try {
      const settings = wx.getStorageSync('notificationSettings')
      if (settings) {
        this.setData({
          notificationSettings: settings.types || this.data.notificationSettings,
          quietHours: settings.quietHours || this.data.quietHours
        })
      }
    } catch (error) {
      console.error('加载通知设置失败:', error)
    }
  },

  // 保存通知设置
  saveNotificationSettings() {
    try {
      const settings = {
        types: this.data.notificationSettings,
        quietHours: this.data.quietHours
      }
      wx.setStorageSync('notificationSettings', settings)
    } catch (error) {
      console.error('保存通知设置失败:', error)
    }
  },

  // 开启通知
  enableNotifications() {
    wx.showModal({
      title: '开启通知',
      content: '开启通知后，你将及时收到考试提醒、任务截止等重要信息',
      confirmText: '开启',
      success: (res) => {
        if (res.confirm) {
          this.setData({ notificationsEnabled: true })

          wx.showToast({
            title: '通知已开启',
            icon: 'success'
          })
        }
      }
    })
  },

  // 刷新通知
  refreshNotifications() {
    this.loadNotifications()
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshNotifications()
    wx.stopPullDownRefresh()
  }
})