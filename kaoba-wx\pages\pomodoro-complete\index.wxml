<!--pages/pomodoro-complete/index.wxml-->
<view class="container">
  <!-- 完成庆祝 -->
  <view class="celebration-container">
    <view class="celebration-icon">
      <text class="celebration-emoji">🎉</text>
    </view>
    <text class="celebration-title">专注完成！</text>
    <text class="celebration-subtitle">恭喜你完成了一个番茄钟</text>
  </view>

  <!-- 本次统计 -->
  <view class="session-stats-container">
    <view class="section-header">
      <text class="section-title">本次统计</text>
    </view>

    <view class="stats-grid">
      <view class="stat-card">
        <text class="stat-icon">⏱️</text>
        <text class="stat-value">{{sessionData.duration}}</text>
        <text class="stat-label">专注时长</text>
      </view>

      <view class="stat-card">
        <text class="stat-icon">🎯</text>
        <text class="stat-value">{{sessionData.efficiency}}%</text>
        <text class="stat-label">专注效率</text>
      </view>

      <view class="stat-card">
        <text class="stat-icon">📚</text>
        <text class="stat-value">{{sessionData.taskName}}</text>
        <text class="stat-label">学习任务</text>
      </view>

      <view class="stat-card">
        <text class="stat-icon">🔥</text>
        <text class="stat-value">{{sessionData.streak}}</text>
        <text class="stat-label">连续天数</text>
      </view>
    </view>
  </view>

  <!-- 效率评价 -->
  <view class="efficiency-container">
    <view class="section-header">
      <text class="section-title">效率评价</text>
    </view>

    <view class="efficiency-rating">
      <view class="rating-stars">
        <text class="star {{index < sessionData.rating ? 'active' : ''}}"
              wx:for="{{5}}"
              wx:key="*this"
              bindtap="rateSession"
              data-rating="{{index + 1}}">
          ⭐
        </text>
      </view>
      <text class="rating-text">{{sessionData.ratingText}}</text>
    </view>

    <view class="efficiency-feedback">
      <text class="feedback-title">本次专注反馈：</text>
      <text class="feedback-text">{{sessionData.feedback}}</text>
    </view>
  </view>

  <!-- 学习笔记 -->
  <view class="notes-container">
    <view class="section-header">
      <text class="section-title">学习笔记</text>
    </view>

    <view class="notes-input-container">
      <textarea class="notes-input"
                placeholder="记录这次学习的收获和感想..."
                value="{{sessionData.notes}}"
                bindinput="updateNotes"
                maxlength="200"/>
      <text class="notes-counter">{{sessionData.notes.length}}/200</text>
    </view>
  </view>

  <!-- 下一步行动 -->
  <view class="next-actions-container">
    <view class="section-header">
      <text class="section-title">下一步</text>
    </view>

    <view class="actions-grid">
      <button class="action-btn continue-btn" bindtap="continueStudy">
        <text class="action-icon">🍅</text>
        <text class="action-text">继续专注</text>
      </button>

      <button class="action-btn break-btn" bindtap="takeBreak">
        <text class="action-icon">☕</text>
        <text class="action-text">休息一下</text>
      </button>

      <button class="action-btn task-btn" bindtap="viewTasks">
        <text class="action-icon">📝</text>
        <text class="action-text">查看任务</text>
      </button>

      <button class="action-btn stats-btn" bindtap="viewStats">
        <text class="action-icon">📊</text>
        <text class="action-text">查看统计</text>
      </button>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="finish-btn" bindtap="finishSession">完成并返回</button>
  </view>
</view>