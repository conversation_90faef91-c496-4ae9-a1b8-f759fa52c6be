<!--pages/exam-center/index.wxml-->
<view class="container">
  <!-- 顶部统计 -->
  <view class="stats-header">
    <view class="stats-card">
      <view class="stats-item" wx:for="{{examStats}}" wx:key="label">
        <text class="stats-value">{{item.value}}</text>
        <text class="stats-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 筛选和搜索 -->
  <view class="filter-section">
    <view class="filter-tabs">
      <view class="filter-tab {{currentFilter === item.value ? 'active' : ''}}" 
            wx:for="{{filterTabs}}" 
            wx:key="value" 
            bindtap="switchFilter" 
            data-filter="{{item.value}}">
        <text>{{item.label}}</text>
        <text class="tab-count" wx:if="{{item.count > 0}}">{{item.count}}</text>
      </view>
    </view>
    <view class="search-bar" bindtap="openSearch">
      <text class="search-icon">🔍</text>
      <text class="search-placeholder">搜索考试...</text>
    </view>
  </view>

  <!-- 考试列表 -->
  <view class="exams-section" wx:if="{{filteredExams.length > 0}}">
    <view class="exam-item" 
          wx:for="{{filteredExams}}" 
          wx:key="id" 
          bindtap="viewExamDetail" 
          data-id="{{item.id}}"
          bindlongpress="showExamActions"
          data-exam="{{item}}">
      
      <!-- 考试状态指示器 -->
      <view class="exam-status-indicator status-{{item.status}}"></view>
      
      <view class="exam-content">
        <!-- 考试头部信息 -->
        <view class="exam-header">
          <view class="exam-info">
            <text class="exam-name">{{item.name}}</text>
            <view class="exam-meta">
              <text class="exam-type">{{item.type}}</text>
              <text class="exam-date">{{item.date}}</text>
            </view>
          </view>
          <view class="exam-actions">
            <text class="exam-status-text status-{{item.status}}">{{item.statusText}}</text>
            <text class="more-icon" bindtap="showExamActions" data-exam="{{item}}" catchtap="true">⋯</text>
          </view>
        </view>

        <!-- 倒计时 -->
        <view class="exam-countdown" wx:if="{{item.countdown}}">
          <view class="countdown-item" wx:for="{{item.countdown}}" wx:key="unit" wx:for-item="countItem">
            <text class="countdown-number">{{countItem.value}}</text>
            <text class="countdown-unit">{{countItem.unit}}</text>
          </view>
        </view>

        <!-- 准备进度 -->
        <view class="exam-progress">
          <view class="progress-header">
            <text class="progress-label">准备进度</text>
            <text class="progress-percentage">{{item.preparationProgress}}%</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.preparationProgress}}%; background-color: {{item.progressColor}}"></view>
          </view>
        </view>

        <!-- 科目列表 -->
        <view class="exam-subjects" wx:if="{{item.subjects && item.subjects.length > 0}}">
          <view class="subject-item" wx:for="{{item.subjects}}" wx:key="name" wx:for-item="subject">
            <text class="subject-name">{{subject.name}}</text>
            <view class="subject-progress">
              <view class="subject-progress-bar">
                <view class="subject-progress-fill" style="width: {{subject.progress}}%"></view>
              </view>
              <text class="subject-progress-text">{{subject.progress}}%</text>
            </view>
          </view>
        </view>

        <!-- 快捷操作 -->
        <view class="exam-quick-actions">
          <button class="quick-action-btn" bindtap="startStudyForExam" data-id="{{item.id}}" catchtap="true">
            <text class="action-icon">📚</text>
            <text class="action-text">开始学习</text>
          </button>
          <button class="quick-action-btn" bindtap="viewExamTasks" data-id="{{item.id}}" catchtap="true">
            <text class="action-icon">📝</text>
            <text class="action-text">查看任务</text>
          </button>
          <button class="quick-action-btn" bindtap="editExam" data-id="{{item.id}}" catchtap="true">
            <text class="action-icon">✏️</text>
            <text class="action-text">编辑</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <text class="empty-icon">📅</text>
    <text class="empty-title">{{getEmptyTitle()}}</text>
    <text class="empty-message">{{getEmptyMessage()}}</text>
    <button class="btn btn-primary" bindtap="addExam">添加考试</button>
  </view>

  <!-- 添加按钮 -->
  <view class="add-exam-section" wx:if="{{filteredExams.length > 0}}">
    <button class="add-exam-btn" bindtap="addExam">
      <text class="add-icon">+</text>
      <text class="add-text">添加考试</text>
    </button>
  </view>
</view>

<!-- 考试操作菜单 -->
<view class="action-sheet-mask" wx:if="{{showActionSheet}}" bindtap="hideActionSheet">
  <view class="action-sheet" catchtap="stopPropagation">
    <view class="action-sheet-header">
      <text class="action-sheet-title">{{selectedExam.name}}</text>
      <text class="action-sheet-close" bindtap="hideActionSheet">×</text>
    </view>
    <view class="action-sheet-body">
      <view class="action-item" wx:for="{{examActions}}" wx:key="id" bindtap="executeExamAction" data-action="{{item.action}}">
        <text class="action-icon">{{item.icon}}</text>
        <text class="action-text">{{item.text}}</text>
        <text class="action-arrow">›</text>
      </view>
    </view>
  </view>
</view>
