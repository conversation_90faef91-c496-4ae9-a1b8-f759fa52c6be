// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        console.log('登录成功', res.code)
      }
    })

    // 检查是否是首次启动
    this.checkFirstLaunch()
  },

  onShow() {
    // 应用被用户重新打开时触发
    console.log('应用显示')
  },

  onHide() {
    // 应用被用户隐藏时触发
    console.log('应用隐藏')
  },

  onError(error) {
    // 应用发生错误时触发
    console.error('应用错误:', error)
  },

  // 检查是否首次启动
  checkFirstLaunch() {
    const hasLaunched = wx.getStorageSync('hasLaunched')
    if (!hasLaunched) {
      // 首次启动，设置标记
      wx.setStorageSync('hasLaunched', true)
      this.globalData.isFirstLaunch = true
    } else {
      this.globalData.isFirstLaunch = false
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isFirstLaunch: false,
    version: '1.0.0'
  }
})
