{"pageInfo": {"pageName": "编辑任务", "pageId": "editTask", "pageType": "page", "description": "编辑任务信息页面", "version": "1.0.0", "lastModified": "2025-06-27"}, "layout": {"type": "scroll-view", "direction": "vertical", "backgroundColor": "#F5F5F5", "padding": "20rpx"}, "components": [{"id": "header", "type": "view", "className": "header-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "page-title", "type": "text", "content": "编辑任务", "style": {"fontSize": "36rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "8rpx"}}, {"id": "task-status", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "8rpx"}, "children": [{"id": "status-indicator", "type": "view", "style": {"width": "12rpx", "height": "12rpx", "borderRadius": "50%", "backgroundColor": "{{taskData.completed ? '#52C41A' : '#FAAD14'}}"}}, {"id": "status-text", "type": "text", "content": "{{taskData.completed ? '已完成' : '进行中'}}", "style": {"fontSize": "26rpx", "color": "#666666"}}]}]}, {"id": "task-form", "type": "view", "className": "task-form-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "task-title-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "task-title-label", "type": "text", "content": "任务标题 *", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "task-title-input", "type": "input", "placeholder": "请输入任务标题", "value": "{{formData.title}}", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "fontSize": "28rpx", "border": "1rpx solid #E9ECEF"}, "events": {"input": "updateTitle"}}]}, {"id": "task-subject-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "task-subject-label", "type": "text", "content": "所属科目", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "task-subject-selector", "type": "picker", "range": "{{availableSubjects}}", "rangeKey": "name", "value": "{{formData.subjectIndex}}", "style": {"backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx", "border": "1rpx solid #E9ECEF"}, "events": {"change": "selectSubject"}, "children": [{"type": "text", "content": "{{selectedSubject ? selectedSubject.name : '请选择科目'}}", "style": {"fontSize": "28rpx", "color": "{{selectedSubject ? '#333333' : '#999999'}}"}}]}]}, {"id": "task-priority-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "priority-label", "type": "text", "content": "优先级", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "priority-options", "type": "view", "style": {"display": "flex", "gap": "12rpx"}, "children": [{"id": "priority-option", "type": "button", "forEach": "{{priorityOptions}}", "forItem": "option", "forIndex": "index", "content": "{{option.label}}", "style": {"fontSize": "26rpx", "padding": "12rpx 20rpx", "borderRadius": "8rpx", "border": "1rpx solid #D9D9D9", "backgroundColor": "{{formData.priority === option.value ? option.color : '#FFFFFF'}}", "color": "{{formData.priority === option.value ? '#FFFFFF' : '#333333'}}"}, "events": {"tap": "selectPriority"}}]}]}, {"id": "task-completion-field", "type": "view", "style": {"marginBottom": "32rpx"}, "children": [{"id": "completion-label", "type": "text", "content": "完成状态", "style": {"fontSize": "28rpx", "fontWeight": "500", "color": "#333333", "marginBottom": "12rpx"}}, {"id": "completion-toggle", "type": "view", "style": {"display": "flex", "alignItems": "center", "gap": "12rpx", "backgroundColor": "#F8F9FA", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"id": "completion-switch", "type": "switch", "checked": "{{formData.completed}}", "color": "#52C41A", "events": {"change": "toggleCompletion"}}, {"id": "completion-text", "type": "text", "content": "{{formData.completed ? '已完成' : '未完成'}}", "style": {"fontSize": "28rpx", "color": "#333333"}}]}]}]}, {"id": "task-stats", "type": "view", "condition": "{{taskData.studyTime > 0}}", "className": "task-stats-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "stats-title", "type": "text", "content": "任务统计", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#333333", "marginBottom": "20rpx"}}, {"id": "stats-grid", "type": "view", "style": {"display": "grid", "gridTemplateColumns": "1fr 1fr 1fr", "gap": "16rpx"}, "children": [{"id": "study-time", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#E6F7FF", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{taskData.studyTime}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#1890FF", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "学习时长", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "pomodoro-count", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#FFF2E8", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{taskData.pomodoroCount}}", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#FA8C16", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "番茄钟", "style": {"fontSize": "22rpx", "color": "#666666"}}]}, {"id": "completion-rate", "type": "view", "style": {"textAlign": "center", "backgroundColor": "#F6FFED", "borderRadius": "8rpx", "padding": "16rpx"}, "children": [{"type": "text", "content": "{{taskData.completionRate}}%", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#52C41A", "display": "block", "marginBottom": "4rpx"}}, {"type": "text", "content": "完成度", "style": {"fontSize": "22rpx", "color": "#666666"}}]}]}]}, {"id": "danger-zone", "type": "view", "className": "danger-zone-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)", "border": "1rpx solid #FFE7E7"}, "children": [{"id": "danger-title", "type": "text", "content": "危险操作", "style": {"fontSize": "28rpx", "fontWeight": "600", "color": "#FF4D4F", "marginBottom": "16rpx"}}, {"id": "delete-task-btn", "type": "button", "content": "删除任务", "style": {"fontSize": "28rpx", "color": "#FF4D4F", "backgroundColor": "#FFF2F0", "border": "1rpx solid #FFCCC7", "borderRadius": "8rpx", "padding": "16rpx", "width": "100%"}, "events": {"tap": "confirmDeleteTask"}}]}, {"id": "form-actions", "type": "view", "className": "form-actions-container", "style": {"backgroundColor": "#FFFFFF", "borderRadius": "16rpx", "padding": "32rpx", "marginBottom": "24rpx", "boxShadow": "0 4rpx 12rpx rgba(0,0,0,0.1)"}, "children": [{"id": "action-buttons", "type": "view", "style": {"display": "flex", "gap": "16rpx"}, "children": [{"id": "cancel-btn", "type": "button", "content": "取消", "style": {"flex": "1", "fontSize": "28rpx", "color": "#666666", "backgroundColor": "#F5F5F5", "borderRadius": "8rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "cancelEdit"}}, {"id": "save-btn", "type": "button", "content": "保存", "disabled": "{{!isFormValid}}", "style": {"flex": "2", "fontSize": "28rpx", "color": "#FFFFFF", "backgroundColor": "{{isFormValid ? '#1890FF' : '#D9D9D9'}}", "borderRadius": "8rpx", "padding": "16rpx", "border": "none"}, "events": {"tap": "saveTask"}}]}]}], "data": {"taskId": "", "taskData": {"completed": false, "studyTime": "2.5h", "pomodoroCount": 5, "completionRate": 80}, "formData": {"title": "数学高数第一章复习", "subjectIndex": 0, "priority": "high", "completed": false}, "selectedSubject": {"id": "math", "name": "数学"}, "isFormValid": true, "availableSubjects": [{"id": "math", "name": "数学"}, {"id": "english", "name": "英语"}, {"id": "politics", "name": "政治"}, {"id": "professional", "name": "专业课"}, {"id": "other", "name": "其他"}], "priorityOptions": [{"value": "low", "label": "低", "color": "#52C41A"}, {"value": "medium", "label": "中", "color": "#FAAD14"}, {"value": "high", "label": "高", "color": "#FF4D4F"}]}, "methods": {"updateTitle": {"type": "function", "description": "更新任务标题", "params": ["value"], "implementation": "setTaskTitle"}, "selectSubject": {"type": "function", "description": "选择科目", "params": ["index"], "implementation": "setTaskSubject"}, "selectPriority": {"type": "function", "description": "选择优先级", "params": ["priority"], "implementation": "setTaskPriority"}, "toggleCompletion": {"type": "function", "description": "切换完成状态", "params": ["completed"], "implementation": "setTaskCompletion"}, "confirmDeleteTask": {"type": "modal", "title": "确认删除", "content": "删除后无法恢复，确定要删除这个任务吗？", "confirmText": "删除", "cancelText": "取消", "confirmAction": "deleteTask"}, "deleteTask": {"type": "function", "description": "删除任务", "implementation": "removeTask"}, "cancelEdit": {"type": "navigate", "url": "back"}, "saveTask": {"type": "function", "description": "保存任务修改", "implementation": "updateTask"}}, "lifecycle": {"onLoad": ["loadTaskData", "initForm"], "onShow": ["validateForm"]}}